import type { InferSelectModel } from 'drizzle-orm';
import { and, eq, isNull, lt, sql } from 'drizzle-orm';
import { createSelectSchema } from 'drizzle-zod';
import type { EffectData, EffectSource, EffectType } from '../../schema/effects';
import { BaseModel } from '../model';

export type EffectSelect = InferSelectModel<typeof import('../../schema/effects').effects>;

export interface CreateEffectParams {
  userId: string;
  type: EffectType;
  source: EffectSource;
  sourceId?: string;
  name: string;
  description: string;
  data?: EffectData;
  stacks?: number;
  expiresAt?: Date;
}

export interface EffectCalculationResult {
  multiplier: number;
  bonus: number;
}

export class Effects extends BaseModel {
  public schema = createSelectSchema(this.schemas.effects);

  /**
   * Seed initial effects (cleanup expired effects)
   */
  public async seed(): Promise<void> {
    // Clean up expired effects on startup
    await this.cleanupExpiredEffects();
  }

  /**
   * Get all active effects for a user
   */
  public async getUserEffects(userId: string): Promise<EffectSelect[]> {
    return await this.client.instance.query.effects.findMany({
      where: and(
        eq(this.schemas.effects.userId, userId),
        // Only get non-expired effects
        sql`(expires_at IS NULL OR expires_at > NOW())`
      ),
      orderBy: [this.schemas.effects.createdAt],
    });
  }

  /**
   * Get effects of a specific type for a user
   */
  public async getUserEffectsByType(userId: string, type: EffectType): Promise<EffectSelect[]> {
    return await this.client.instance.query.effects.findMany({
      where: and(
        eq(this.schemas.effects.userId, userId),
        eq(this.schemas.effects.type, type),
        sql`(expires_at IS NULL OR expires_at > NOW())`
      ),
      orderBy: [this.schemas.effects.createdAt],
    });
  }

  /**
   * Create or update an effect for a user
   */
  public async createEffect(params: CreateEffectParams): Promise<EffectSelect> {
    // Check if effect already exists and is stackable
    const existing = await this.client.instance.query.effects.findFirst({
      where: and(
        eq(this.schemas.effects.userId, params.userId),
        eq(this.schemas.effects.type, params.type),
        eq(this.schemas.effects.sourceId, params.sourceId || ''),
        sql`(expires_at IS NULL OR expires_at > NOW())`
      ),
    });

    if (existing && params.data?.stackable) {
      // Update existing effect with new stacks
      const newStacks = Math.min(existing.stacks + (params.stacks || 1), params.data.maxStacks || 999);

      const [updated] = await this.client.instance
        .update(this.schemas.effects)
        .set({
          stacks: newStacks,
          expiresAt: params.expiresAt,
          updatedAt: new Date(),
        })
        .where(eq(this.schemas.effects.id, existing.id))
        .returning();

      return updated!;
    }

    // Create new effect
    const [effect] = await this.client.instance
      .insert(this.schemas.effects)
      .values({
        userId: params.userId,
        type: params.type,
        source: params.source,
        sourceId: params.sourceId,
        name: params.name,
        description: params.description,
        data: params.data || {},
        stacks: params.stacks || 1,
        expiresAt: params.expiresAt,
      })
      .returning();

    return effect!;
  }

  /**
   * Remove an effect
   */
  public async removeEffect(effectId: number): Promise<void> {
    await this.client.instance.delete(this.schemas.effects).where(eq(this.schemas.effects.id, effectId));
  }

  /**
   * Remove expired effects
   */
  public async cleanupExpiredEffects(): Promise<number> {
    const result = await this.client.instance
      .delete(this.schemas.effects)
      .where(and(sql`expires_at IS NOT NULL`, lt(this.schemas.effects.expiresAt, new Date())));

    return result.rowCount || 0;
  }

  /**
   * Calculate rift multiplier and bonus for a user
   */
  public async calculateRiftEffects(userId: string): Promise<EffectCalculationResult> {
    const effects = await this.getUserEffectsByType(userId, 'RIFT_MULTIPLIER');
    const bonusEffects = await this.getUserEffectsByType(userId, 'RIFT_BONUS');

    let totalMultiplier = 1;
    let totalBonus = 0;

    // Calculate multipliers
    for (const effect of effects) {
      const multiplier = effect.data.multiplier || 1;
      const stacks = effect.stacks;

      if (effect.data.stackable) {
        // Additive stacking: 1.5x with 2 stacks = 1 + (0.5 * 2) = 2x
        totalMultiplier += (multiplier - 1) * stacks;
      } else {
        // Take the highest multiplier if not stackable
        totalMultiplier = Math.max(totalMultiplier, multiplier);
      }
    }

    // Calculate bonuses
    for (const effect of bonusEffects) {
      const bonus = effect.data.bonus || 0;
      const stacks = effect.stacks;
      totalBonus += bonus * stacks;
    }

    return {
      multiplier: totalMultiplier,
      bonus: totalBonus,
    };
  }

  /**
   * Calculate experience multiplier and bonus for a user
   */
  public async calculateExpEffects(userId: string): Promise<EffectCalculationResult> {
    const effects = await this.getUserEffectsByType(userId, 'EXP_MULTIPLIER');
    const bonusEffects = await this.getUserEffectsByType(userId, 'EXP_BONUS');

    let totalMultiplier = 1;
    let totalBonus = 0;

    // Calculate multipliers
    for (const effect of effects) {
      const multiplier = effect.data.multiplier || 1;
      const stacks = effect.stacks;

      if (effect.data.stackable) {
        totalMultiplier += (multiplier - 1) * stacks;
      } else {
        totalMultiplier = Math.max(totalMultiplier, multiplier);
      }
    }

    // Calculate bonuses
    for (const effect of bonusEffects) {
      const bonus = effect.data.bonus || 0;
      const stacks = effect.stacks;
      totalBonus += bonus * stacks;
    }

    return {
      multiplier: totalMultiplier,
      bonus: totalBonus,
    };
  }

  /**
   * Calculate currency-specific effects
   */
  public async calculateCurrencyEffects(userId: string, currencyName: string): Promise<EffectCalculationResult> {
    const multiplierEffects = await this.getUserEffectsByType(userId, 'CURRENCY_MULTIPLIER');
    const bonusEffects = await this.getUserEffectsByType(userId, 'CURRENCY_BONUS');

    // Filter for specific currency
    const relevantMultipliers = multiplierEffects.filter((effect) => effect.data.currency === currencyName);
    const relevantBonuses = bonusEffects.filter((effect) => effect.data.currency === currencyName);

    let totalMultiplier = 1;
    let totalBonus = 0;

    // Calculate multipliers
    for (const effect of relevantMultipliers) {
      const multiplier = effect.data.multiplier || 1;
      const stacks = effect.stacks;

      if (effect.data.stackable) {
        totalMultiplier += (multiplier - 1) * stacks;
      } else {
        totalMultiplier = Math.max(totalMultiplier, multiplier);
      }
    }

    // Calculate bonuses
    for (const effect of relevantBonuses) {
      const bonus = effect.data.bonus || 0;
      const stacks = effect.stacks;
      totalBonus += bonus * stacks;
    }

    return {
      multiplier: totalMultiplier,
      bonus: totalBonus,
    };
  }
}
