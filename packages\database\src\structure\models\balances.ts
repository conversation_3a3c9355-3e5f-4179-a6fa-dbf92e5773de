import type { InferSelectModel } from 'drizzle-orm';
import { eq } from 'drizzle-orm';
import { createSelectSchema } from 'drizzle-zod';
import { BaseModel } from '../model';

export type CurrencySelect = InferSelectModel<typeof import('../../schema/currencies').currencies>;
export type BalanceSelect = InferSelectModel<typeof import('../../schema/balances').balances>;

export interface UserCurrencyBalance extends BalanceSelect {
  currency: CurrencySelect;
}

export class Currencies extends BaseModel {
  public schema = createSelectSchema(this.schemas.balances);

  public async of(user: string) {
    return await this.client.instance.query.balances.findMany({
      where: { userId: user },
      with: {
        currency: true,
      },
    });
  }

  public async set(user: string, currency: number, amount: number): Promise<BalanceSelect> {
    const existing = await this.client.instance.query.balances.findFirst({
      where: { userId: user, currencyId: currency },
    });

    if (existing) {
      const [updated] = await this.client.instance
        .update(this.schemas.balances)
        .set({ amount, updatedAt: new Date() })
        .where(eq(this.schemas.balances.id, existing.id))
        .returning();
      return updated!;
    }

    const [created] = await this.client.instance
      .insert(this.schemas.balances)
      .values({ userId: user, currencyId: currency, amount })
      .returning();

    return created!;
  }

  public async add(user: string, currency: number, amount: number): Promise<BalanceSelect> {
    const existing = await this.client.instance.query.balances.findFirst({
      where: { userId: user, currencyId: currency },
    });

    if (existing) {
      const newAmount = existing.amount + amount;
      const [updated] = await this.client.instance
        .update(this.schemas.balances)
        .set({ amount: newAmount, updatedAt: new Date() })
        .where(eq(this.schemas.balances.id, existing.id))
        .returning();
      return updated!;
    }

    const [created] = await this.client.instance
      .insert(this.schemas.balances)
      .values({ userId: user, currencyId: currency, amount })
      .returning();

    return created!;
  }

  public async initialize(id: string): Promise<BalanceSelect[]> {
    const currencies = await this.client.currencies.getAllCurrencies();
    const balances = await Promise.all(currencies.map((currency) => this.set(id, currency.id, 0)));

    return balances;
  }
}
