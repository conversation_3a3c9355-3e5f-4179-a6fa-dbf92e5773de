import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Fire Storm',
  name: 'Fire Storm',
  element: 'Sirius',
  manaCost: 50,
  cooldown: 7,
  description: 'Creates a storm of fire, damaging and burning all enemies in an area.',
  execute: (caster, target, formulas) => {
    // This would require a more complex targeting and status effect system.
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const finalDamage = Math.round(damage * 1.7);
    return {
      damage: finalDamage,
      isCritical,
      log: `${caster.name} creates a Fire Storm, damaging and burning all enemies for ${finalDamage} damage${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
