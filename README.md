<div align="center">
 <img src="https://img.shields.io/badge/Project-Goddess-8A2BE2?style=for-the-badge&labelColor=000000" alt="Project Goddess" />
 <br />
  <a href="/about"><strong>Explore the World of 2D Females</strong></a>
  <br />
  <br />
  <a href="https://github.com/Kuureki/Goddess/issues/new?assignees=&labels=bug&template=01_BUG_REPORT.md&title=bug%3A+">Report a Bug</a>
  ·
  <a href="https://github.com/Kuureki/Goddess/issues/new?assignees=&labels=enhancement&template=02_FEATURE_REQUEST.md&title=feat%3A+">Request a Feature</a>
  · <a href="https://github.com/Kuureki/Goddess/discussions">Ask a Question</a>
  <br />
  <br />
</div>

<p align="center">
  <a href="https://github.com/Kuureki/">
    <img src="https://img.shields.io/badge/Kuureki-black?style=for-the-badge&logo=github&labelColor=000000" alt="Kuureki" />
  </a>

  <div align="center">
  <img src="https://img.shields.io/static/v1?label=PRs&message=welcome&style=for-the-badge&color=8A2BE2&labelColor=000000" alt="PRs welcome!" />
  <img alt="License" src="https://img.shields.io/github/license/Kuureki/Goddess?style=for-the-badge&color=8A2BE2&labelColor=000000">
  <a href="https://twitter.com/intent/follow?screen_name=Kuureki">
    <img src="https://img.shields.io/twitter/follow/Kuureki?style=for-the-badge&color=8A2BE2&labelColor=000000" alt="Follow Kuureki" />
  </a>
  <img src="https://img.shields.io/badge/Status-In%20Progress-8A2BE2?style=for-the-badge&labelColor=000000" alt="Status In Progress" />
  </div>
</p>

## Stats

<p align="center">
  <img src="https://img.shields.io/github/stars/Kuureki/Goddess?style=for-the-badge&color=8A2BE2&labelColor=000000" alt="GitHub Stars" />
  <img src="https://img.shields.io/github/forks/Kuureki/Goddess?style=for-the-badge&color=8A2BE2&labelColor=000000" alt="GitHub Forks" />
  <img src="https://img.shields.io/github/issues/Kuureki/Goddess?style=for-the-badge&color=8A2BE2&labelColor=000000" alt="GitHub Issues" />
  <img src="https://img.shields.io/github/last-commit/Kuureki/Goddess?style=for-the-badge&color=8A2BE2&labelColor=000000" alt="Last Commit" />
</p>

<br>

**Goddess** is a full-featured Waifu Image Board. This project will serve as an **automatic image board with automatic image tagging features**, with future plans to offer **reverse image search**. It's built upon a solid foundation.

- 🚀 **Next.js 15 App Router & React 18**
- ⚙️ **Tailwind CSS 4** - A utility-first CSS framework
- 📏 **ESLint** — Pluggable JavaScript linter
- 💖 **Prettier** - Opinionated Code Formatter
- 😁 **Shadcn** - Beautifully designed components that you can copy and paste into your apps.

---

## 🚀 Getting Started

- Add getting started guide

---

## Contributing

First off, thanks for taking the time to contribute! Contributions are what make the open-source community such an amazing place to learn, inspire, and create. Any contributions you make will benefit everybody else and are **greatly appreciated**.

Please read [our contribution guidelines](docs/CONTRIBUTING.md), and thank you for being involved!

## Authors & contributors

The original setup of this repository is by [Kuureki](https://github.com/Kuureki).

For a full list of all authors and contributors, see [the contributors page](https://github.com/Kuureki/Goddess/contributors).

## Security

Goddess follows good practices of security, but 100% security cannot be assured.
Goddess is provided **"as is"** without any **warranty**. Use at your own risk.

_For more information and to report security issues, please refer to our [security documentation](docs/SECURITY.md)._

## License

This project is licensed under the **Apache Software License 2.0**.

See [LICENSE](LICENSE) for more information.
