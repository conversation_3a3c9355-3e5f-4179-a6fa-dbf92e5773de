import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Energy Shield',
  name: 'Energy Shield',
  element: 'Arcane',
  manaCost: 20,
  cooldown: 3,
  description: 'Creates a shield of energy that absorbs damage. The shield\'s strength is based on the caster\'s intelligence.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Mana Shield',
        name: 'Energy Shield',
        duration: 3,
        sourceId: caster.id,
      },
      log: `${caster.name} creates an Energy Shield.`,
    };
  },
});
