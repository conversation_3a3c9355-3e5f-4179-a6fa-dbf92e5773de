import { integer, pgTable, serial, text, timestamp, uniqueIndex } from 'drizzle-orm/pg-core';
import { currencies } from './currencies';

export const balances = pgTable(
  'balances',
  {
    id: serial('id').primaryKey(),
    userId: text('user_id').notNull(),
    currencyId: integer('currency_id')
      .notNull()
      .references(() => currencies.id, { onDelete: 'cascade' }),
    amount: integer('amount').default(0).notNull(),
    createdAt: timestamp('created_at').defaultNow().notNull(),
    updatedAt: timestamp('updated_at').defaultNow().notNull(),
  },
  (table) => [uniqueIndex('user_currency_idx').on(table.userId, table.currencyId)]
);
