{"$schema": "https://biomejs.dev/schemas/2.1.3/schema.json", "extends": ["ultracite"], "linter": {"rules": {"style": {"useBlockStatements": "off", "useConsistentMemberAccessibility": "off", "noNonNullAssertion": "off", "useFilenamingConvention": "off"}, "nursery": {"noUnknownAtRule": "off"}, "complexity": {"noExcessiveCognitiveComplexity": "off"}, "performance": {"noNamespaceImport": "off"}}}, "formatter": {"lineWidth": 120}}