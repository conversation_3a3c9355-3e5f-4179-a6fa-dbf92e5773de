import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Radiant Strike',
  name: 'Radiant Strike',
  element: 'Sirius',
  manaCost: 14,
  cooldown: 1,
  description: 'Strike with radiant light, dealing damage and boosting your attack power.',
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);

    return {
      damage: Math.round(damage * 1.1),
      isCritical,
      appliedEffect: {
        id: 'radiant_boost',
        name: 'Radiant Boost',
        duration: 2,
        potency: 8, // +8 attack
        sourceId: caster.id,
      },
      log: `${caster.name} strikes ${target.name} with radiant light for ${Math.round(damage * 1.1)} damage${isCritical ? ' (CRIT!)' : ''} and gains power!`,
    };
  },
});
