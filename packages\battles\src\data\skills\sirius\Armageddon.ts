import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Armageddon',
  name: 'Armageddon',
  element: 'Sirius',
  manaCost: 100,
  cooldown: 12,
  description: 'The caster calls down Armageddon, dealing immense damage to all enemies and having a chance to stun them.',
  execute: (caster, target, formulas) => {
    // This would require a more complex targeting and status effect system.
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const finalDamage = Math.round(damage * 4);
    return {
      damage: finalDamage,
      isCritical,
      log: `${caster.name} calls down Armageddon, dealing ${finalDamage} damage to all enemies${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
