import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Arcane Resonance',
  name: 'Arcane Resonance',
  element: 'Arcane',
  manaCost: 25,
  cooldown: 4,
  description: 'Causes the target to resonate with arcane energy, taking damage whenever they use a skill.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Mana Burn Effect',
        name: 'Arcane Resonance',
        duration: 3,
        sourceId: caster.id,
      },
      log: `${caster.name} causes ${target.name} to resonate with arcane energy.`,
    };
  },
});
