import type { DropTypeConfig } from '@megami/config/lib/drops';
import { translate } from '@megami/locale';
import { MessageFlags, SeparatorBuilder, SeparatorSpacingSize, TextDisplayBuilder } from 'discord.js';
import { defineSubCommand } from '../../../handlers/command';
import { MegamiContainer } from '../../../helpers/containers';
import { defer } from '../../../helpers/defer';
import { type PaginationPage, SelectPaginator } from '../../../lib/components/pagination';

export default defineSubCommand((builder) => ({
  builder: builder.setName('view').setDescription('Browse available drops in the Rift Shop'),

  config: {},

  execute: async (interaction) => {
    await defer(interaction, false);

    const user = await interaction.client.database.users.get(interaction.user.id);
    if (!user) {
      const container = new MegamiContainer([
        new TextDisplayBuilder().setContent('❌ **Not Registered**'),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent(translate('errors.user.unregistered.description')),
      ]);

      return await interaction.editReply({
        components: [container],
        flags: MessageFlags.IsComponentsV2,
      });
    }

    try {
      // Get user's current rifts and available drop types
      const userRifts = await interaction.client.database.users.getRifts(user.id);
      const availableDrops = await interaction.client.database.drops.getAvailableDropTypes(user.id);
      const activePenalties = await interaction.client.database.drops.getActivePenalties(user.id);

      // Create pagination pages for each drop type
      const pages: PaginationPage[] = [
        {
          label: 'Shop Overview',
          description: 'Browse all available drops',
          emoji: '🏪',
          components: createOverviewPage(userRifts, availableDrops, activePenalties),
        },
        ...availableDrops.map((drop) => ({
          label: drop.name,
          description: `${drop.cost} Rifts - ${drop.description}`,
          emoji: drop.icon,
          components: createDropTypePage(drop, userRifts),
        })),
      ];

      // Create paginator
      const paginator = new SelectPaginator({
        interaction,
        containers: pages,
        placeholder: 'Select a drop type to view details...',
        timeout: 300_000, // 5 minutes
      });

      // Start pagination
      await paginator.start(pages[0].components);
    } catch (error) {
      interaction.client.logger.error('Error showing shop overview:', error);

      const container = new MegamiContainer([
        new TextDisplayBuilder().setContent('❌ **Error**'),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent('An error occurred while loading the shop.'),
        new TextDisplayBuilder().setContent('Please try again or contact support.'),
      ]);

      await interaction.editReply({
        components: [container],
        flags: MessageFlags.IsComponentsV2,
      });
    }
  },
}));

function createOverviewPage(userRifts: number, availableDrops: DropTypeConfig[], activePenalties: any[]) {
  const components = [
    new TextDisplayBuilder().setContent('🏪 **Rift Shop**'),
    new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
    new TextDisplayBuilder().setContent('Welcome to the Rift Shop! Spend your rifts on various drop types.'),
    new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Large),
    new TextDisplayBuilder().setContent(`💰 **Your Rifts:** ${userRifts}/100`),
  ];

  // Show active penalties if any
  if (activePenalties.length > 0) {
    components.push(
      new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Large),
      new TextDisplayBuilder().setContent('⚠️ **Active Penalties**')
    );

    for (const penalty of activePenalties) {
      const expiresIn = Math.ceil((penalty.expiresAt.getTime() - Date.now()) / 60_000);
      components.push(new TextDisplayBuilder().setContent(`• ${penalty.description} (${expiresIn}m remaining)`));
    }
  }

  components.push(
    new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Large),
    new TextDisplayBuilder().setContent('📋 **Available Drops**')
  );

  // List available drops
  for (const drop of availableDrops) {
    const canAfford = userRifts >= drop.cost;
    const affordIcon = canAfford ? '✅' : '❌';

    components.push(
      new TextDisplayBuilder().setContent(`${affordIcon} ${drop.icon} **${drop.name}** - ${drop.cost} Rifts`),
      new TextDisplayBuilder().setContent(`   ${drop.description}`)
    );
  }

  components.push(
    new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Large),
    new TextDisplayBuilder().setContent('💡 **Tip:** Use `/shop buy type:<drop-name>` to purchase directly!')
  );

  return components;
}

function createDropTypePage(drop: DropTypeConfig, userRifts: number) {
  const canAfford = userRifts >= drop.cost;

  const components = [
    new TextDisplayBuilder().setContent(`${drop.icon} **${drop.name}**`),
    new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
    new TextDisplayBuilder().setContent(drop.description),
    new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Large),
    new TextDisplayBuilder().setContent(`💰 **Cost:** ${drop.cost} Rifts`),
    new TextDisplayBuilder().setContent(`💳 **You have:** ${userRifts} Rifts ${canAfford ? '✅' : '❌'}`),
    new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Large),
    new TextDisplayBuilder().setContent('🎲 **Drop Odds**'),
    new TextDisplayBuilder().setContent(`⭐ Stardust: ${drop.rarityOdds.stardust}%`),
    new TextDisplayBuilder().setContent(`🌕 Moonlight: ${drop.rarityOdds.moonlight}%`),
    new TextDisplayBuilder().setContent(`✨ Celestial: ${drop.rarityOdds.celestial}%`),
  ];

  // Show penalty information
  if (drop.penalty) {
    const severityEmoji = {
      LOW: '🟡',
      MEDIUM: '🟠',
      HIGH: '🔴',
      VERY_HIGH: '🚨',
    }[drop.penalty.severity];

    components.push(
      new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Large),
      new TextDisplayBuilder().setContent(`${severityEmoji} **Penalty Risk: ${drop.penalty.chance}%**`),
      new TextDisplayBuilder().setContent(`⚠️ ${drop.penalty.description}`)
    );
  } else {
    components.push(
      new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Large),
      new TextDisplayBuilder().setContent('✅ **No Penalty Risk**'),
      new TextDisplayBuilder().setContent('This drop type is completely safe!')
    );
  }

  // Show requirements
  if (drop.requirements) {
    components.push(new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Large));

    if (drop.requirements.minLevel) {
      components.push(new TextDisplayBuilder().setContent(`📊 **Min Level:** ${drop.requirements.minLevel}`));
    }

    if (drop.requirements.maxPerDay) {
      components.push(new TextDisplayBuilder().setContent(`📅 **Daily Limit:** ${drop.requirements.maxPerDay}`));
    }

    if (drop.requirements.cooldownMinutes) {
      components.push(
        new TextDisplayBuilder().setContent(`⏰ **Cooldown:** ${drop.requirements.cooldownMinutes} minutes`)
      );
    }
  }

  components.push(
    new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Large),
    new TextDisplayBuilder().setContent(`💡 **Purchase:** \`/shop buy type:${drop.id}\``)
  );

  return components;
}
