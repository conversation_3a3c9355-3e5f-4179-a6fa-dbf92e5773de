import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Leyline Manipulation',
  name: '<PERSON><PERSON><PERSON> Manipulation',
  element: 'Arcane',
  manaCost: 40,
  cooldown: 6,
  description: 'Manipulates the leylines to increase the mana regeneration of the caster\'s party.',
  execute: (caster, target, formulas) => {
    // This would require a party and mana regeneration system. For now, it affects the caster.
    return {
      appliedEffect: {
        id: 'Intelligence Up',
        name: '<PERSON><PERSON><PERSON> Manipulation',
        duration: 4,
        sourceId: caster.id,
      },
      log: `${caster.name} manipulates the leylines, increasing their mana regeneration.`,
    };
  },
});
