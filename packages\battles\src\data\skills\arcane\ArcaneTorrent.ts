import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Arcane Torrent',
  name: 'Arcane Torrent',
  element: 'Arcane',
  manaCost: 35,
  cooldown: 4,
  description: 'A torrent of arcane energy that damages and silences all enemies in an area.',
  execute: (caster, target, formulas) => {
    // This would require a more complex targeting and status effect system.
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const finalDamage = Math.round(damage * 1.1);
    return {
      damage: finalDamage,
      isCritical,
      log: `${caster.name} unleashes an Arcane Torrent, damaging and silencing ${target.name} for ${finalDamage} damage${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
