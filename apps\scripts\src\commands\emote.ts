import * as prompt from '@clack/prompts';
import { $ } from 'bun';

const regex = /(\.\w+)$/;

export async function animate() {
  prompt.intro('Megami Scripts - MP4 To GIF');

  const input = (await prompt.text({
    message: 'Input file path',
    placeholder: 'e.g. O:/My Drive/Megami/Cards/MP4s/2.mp4',
    validate: (value) => {
      if (!value) return 'Required';
    },
  })) as string;

  const strictness = (await prompt.text({
    message: 'Colorkey strictness (0.0 - 1.0)',
    placeholder: 'e.g. 0.3',
    validate: (value) => {
      if (!value) return 'Required';
      if (Number.isNaN(Number(value))) return 'Must be a number';
      if (Number(value) < 0 || Number(value) > 1) return 'Must be between 0 and 1';
    },
  })) as string;

  const denoise = (await prompt.text({
    message: 'Denoise (0.0 - 1.0)',
    placeholder: 'e.g. 0.3',
    validate: (value) => {
      if (!value) return 'Required';
      if (Number.isNaN(Number(value))) return 'Must be a number';
      if (Number(value) < 0 || Number(value) > 1) return 'Must be between 0 and 1';
    },
  })) as string;

  const path = input
    .trim()
    .replace(/^["']|["']$/g, '')
    .replaceAll('\\', '/');

  const output = path.replace(regex, '_output$1').replace('mp4', 'gif');

  await prompt.tasks([
    {
      title: 'Processing video',
      task: async (_message) => {
        await $`ffmpeg -i ${path} -filter_complex "[0:v]hqdn3d=1:1:1:1,colorkey=0x000000:${strictness}:${denoise},crop='min(in_w\,in_h)':'min(in_w\,in_h)',scale=64:-1:flags=spline,fps=12,split[a][b];[b]reverse[r];[a][r]concat=n=2:v=1:a=0,split[c1][c2];[c1]palettegen=max_colors=48:reserve_transparent=1[p];[c2][p]paletteuse=dither=floyd_steinberg" -loop 0 -an ${output}`.quiet();
        return `Video processed, Output: ${output}`;
      },
    },
  ]);

  prompt.outro('Done!');
}
