import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Feedback Loop',
  name: 'Feedback Loop',
  element: 'Vortex',
  manaCost: 30,
  cooldown: 5,
  description: 'The target is trapped in a feedback loop, taking damage whenever they use a skill. The damage increases with each skill used.',
  execute: (caster, target, formulas) => {
    // This would require a status effect system to handle the feedback loop.
    return {
      log: `${caster.name} traps ${target.name} in a Feedback Loop.`,
    };
  },
});
