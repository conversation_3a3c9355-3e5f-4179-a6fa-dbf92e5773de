import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Tidal Wave',
  name: 'Tidal Wave',
  element: 'Selene',
  manaCost: 40,
  cooldown: 6,
  description: 'Summons a massive tidal wave that damages and slows all enemies.',
  execute: (caster, target, formulas) => {
    // This would require a more complex targeting and status effect system.
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const finalDamage = Math.round(damage * 1.6);
    return {
      damage: finalDamage,
      isCritical,
      log: `${caster.name} summons a Tidal Wave, damaging and slowing all enemies for ${finalDamage} damage${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
