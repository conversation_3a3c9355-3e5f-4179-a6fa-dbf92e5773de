import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Chrono Sphere',
  name: 'Chrono Sphere',
  element: 'Vortex',
  manaCost: 40,
  cooldown: 6,
  description: 'Creates a sphere of distorted time that slows any enemy that enters it.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Slow',
        name: 'Chrono Sphere',
        duration: 3,
        sourceId: caster.id,
      },
      log: `${caster.name} creates a Chrono Sphere.`,
    };
  },
});
