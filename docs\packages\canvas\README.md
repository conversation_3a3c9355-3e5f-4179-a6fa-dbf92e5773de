# `packages/canvas`

This package provides a set of tools for image manipulation, including GIF composition, image framing, and upscaling.

## Installation

To install dependencies:

```bash
bun install
```

## Usage

To run:

```bash
bun run index.ts
```

---

## Architecture

The `canvas` package offers a variety of image manipulation capabilities through its client, `MegamiCanvasClient`.

### `MegamiCanvasClient`

The main client for interacting with the canvas utilities. It provides access to the `gif` and `frames` services.

```typescript
import { MegamiCanvasClient } from './structure/client';

const canvas = new MegamiCanvasClient();
```

### GIF Composition (`gif`)

The `GifOverlayService` allows you to compose a base image (static or animated) with two GIF overlays.

**Methods:**

-   **`processFiles(baseImagePath, topLeftGifPath, topRightGifPath, options)`**: Composes the images and returns a buffer.
-   **`processAndSave(baseImagePath, topLeftGifPath, topRightGifPath, outputPath, options)`**: Composes the images and saves the result to a file.

**Example:**

```typescript
const resultBuffer = await canvas.gif.processFiles(
  'path/to/base.png',
  'path/to/left.gif',
  'path/to/right.gif',
  { outputFormat: 'gif' }
);
```

### Image Framing (`frames`)

The `MegamiFrameClient` can be used to create framed images. It fetches a frame from a URL, and then places a character image inside it. The frame is tinted based on the dominant color of the character image.

**Methods:**

-   **`fromURL(url)`**: Creates a `MegamiFrame` instance from a URL.
-   **`around(character)`**: Composes the frame with a character image.

**Example:**

```typescript
const frame = await canvas.frames.fromURL('url/to/frame.png');
const character = await fetch('url/to/character.png').then(res => res.arrayBuffer());
const resultBuffer = await frame.around(Buffer.from(character));
```

### Image Upscaling

The `upscale` function can be used to upscale an image using the `waifu2x` algorithm.

**`upscale(options, parameters)`**

-   `options`: An object containing the `source` buffer.
-   `parameters`: `waifu2x` options (e.g., `scale`, `noise`).
-   **Returns:** A promise that resolves to the upscaled image buffer.

**Example:**

```typescript
import { upscale } from './lib/upscaler';

const sourceBuffer = ...; // Your image buffer
const upscaledBuffer = await upscale({ source: sourceBuffer }, { scale: 2 });
```
