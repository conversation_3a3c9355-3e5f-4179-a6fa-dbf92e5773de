import { createSelectSchema } from 'drizzle-zod';
import { BaseModel } from '../model';

export class <PERSON><PERSON>s extends BaseModel {
  public schema = createSelectSchema(this.schemas.frames);

  public async get(name: string) {
    return await this.client.instance.query.frames.findFirst({
      where: {
        name,
      },
    });
  }

  public async getAll() {
    return await this.client.instance.query.frames.findMany();
  }

  public async create(name: 'string', url: string, rarity: 'STARDUST' | 'MOONLIGHT') {
    const [frame] = await this.client.instance
      .insert(this.schemas.frames)
      .values({
        name,
        url,
        rarity,
      })
      .returning()
      .execute();

    return frame!;
  }
}
