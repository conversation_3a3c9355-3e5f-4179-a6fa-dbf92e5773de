import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Flashpoint',
  name: 'Flashpoint',
  element: 'Sirius',
  manaCost: 25,
  cooldown: 4,
  description: 'The target reaches its flashpoint, causing all burns on them to instantly deal their remaining damage.',
  execute: (caster, target, formulas) => {
    // This would require a status effect system to handle burns and their damage calculation.
    return {
      log: `${caster.name} causes the burns on ${target.name} to erupt.`,
    };
  },
});
