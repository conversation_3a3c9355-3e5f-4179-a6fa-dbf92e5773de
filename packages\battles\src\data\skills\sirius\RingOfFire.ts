import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Ring of Fire',
  name: 'Ring of Fire',
  element: 'Sirius',
  manaCost: 35,
  cooldown: 5,
  description: 'Creates a ring of fire around the caster that damages any enemy that enters or leaves it.',
  execute: (caster, target, formulas) => {
    // This would require a system for creating and managing persistent area effects.
    return {
      log: `${caster.name} creates a Ring of Fire.`,
    };
  },
});
