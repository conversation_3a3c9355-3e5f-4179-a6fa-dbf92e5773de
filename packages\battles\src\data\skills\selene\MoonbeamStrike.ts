import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Moonbeam Strike',
  name: 'Moonbeam Strike',
  element: 'Selene',
  manaCost: 8,
  cooldown: 0,
  description: 'A basic strike of moonlight. Deals moderate damage.',
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    return {
      damage,
      isCritical,
      log: `${caster.name} strikes ${target.name} with a Moonbeam for ${damage} damage${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
