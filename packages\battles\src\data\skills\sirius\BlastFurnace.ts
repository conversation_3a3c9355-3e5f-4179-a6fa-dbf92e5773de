import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Blast Furnace',
  name: 'Blast Furnace',
  element: 'Sirius',
  manaCost: 40,
  cooldown: 6,
  description: 'The caster becomes a blast furnace, increasing their defense and dealing damage to any enemy that attacks them.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Blast Furnace Effect',
        name: 'Blast Furnace',
        duration: 4,
        sourceId: caster.id,
      },
      log: `${caster.name} becomes a blast furnace.`,
    };
  },
});
