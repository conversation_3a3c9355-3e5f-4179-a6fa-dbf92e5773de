import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Arcane Shield',
  name: 'Arcane Shield',
  element: 'Arcane',
  manaCost: 10,
  cooldown: 2,
  description: 'Surround yourself with a shield of arcane energy, reducing incoming damage for 2 turns.',
  execute: (caster) => {
    return {
      log: `${caster.name} conjures an Arcane Shield, reducing incoming damage!`,
      appliedEffect: {
        id: 'arcane_shield',
        name: 'Arcane Shield',
        duration: 2,
        potency: 0.7, // 30% damage reduction (to be handled in engine)
        sourceId: caster.id,
      },
    };
  },
});
