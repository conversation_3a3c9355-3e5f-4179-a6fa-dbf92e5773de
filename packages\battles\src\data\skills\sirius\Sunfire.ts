import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Sunfire',
  name: 'Sunfire',
  element: 'Sirius',
  manaCost: 45,
  cooldown: 6,
  description: 'The target is engulfed in sunfire, taking damage over time and spreading the effect to nearby enemies.',
  execute: (caster, target, formulas) => {
    // This would require a status effect system to handle the DoT and spreading effect.
    return {
      log: `${caster.name} engulfs ${target.name} in Sunfire.`,
    };
  },
});
