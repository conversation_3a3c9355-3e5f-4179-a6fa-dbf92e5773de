import { EventEmitter } from 'node:events';
import {
  type ActionRowBuilder,
  type <PERSON>tonBuilder,
  type ButtonInteraction,
  type ChatInputCommandInteraction,
  ComponentType,
  ContainerBuilder,
  type InteractionCollector,
  type Message,
  type MessageComponentType,
  MessageFlags,
  SeparatorBuilder,
  SeparatorSpacingSize,
  type Snowflake,
  type StringSelectMenuBuilder,
  type StringSelectMenuInteraction,
  TextDisplayBuilder,
} from 'discord.js';
import type TypedEmitter from 'typed-emitter';
import { type Components, MegamiContainer } from '../../../helpers/containers';
import { logger } from '../../../structure/logger';
import type { Page } from './page';

export interface PaginationPage {
  components: Components[];
  label?: string;
  description?: string;
  emoji?: string;
}

export interface BasePaginationOptions {
  interaction: ChatInputCommandInteraction;
  pages: {
    total: number;
    current: number;
  };
  startPage?: number;
  timeout?: number;
  editReply?: boolean;
  users?: Snowflake[];
}

export interface ButtonPaginationOptions extends BasePaginationOptions {
  emojis?: {
    start?: string;
    prev?: string;
    stop?: string;
    next?: string;
    end?: string;
  };
}

export interface SelectPaginationOptions extends BasePaginationOptions {
  placeholder?: string;
  maxValues?: number;
  minValues?: number;
}

export type PaginationEvents = {
  change: (
    number: number,
    page: Page,
    interaction: ButtonInteraction | StringSelectMenuInteraction
  ) => unknown | Promise<unknown>;
  start: (page: Page) => unknown | Promise<unknown>;
  stop: (
    page: number,
    interaction: ButtonInteraction | StringSelectMenuInteraction | null
  ) => unknown | Promise<unknown>;
  timeout: (page: number) => unknown | Promise<unknown>;
  error: (error: Error, page: number) => unknown | Promise<unknown>;
};

export abstract class BasePaginator extends (EventEmitter as new () => TypedEmitter<PaginationEvents>) {
  protected interaction: ChatInputCommandInteraction;
  public pages: BasePaginationOptions['pages'];
  // @ts-expect-error - ignore this
  public instance: Page;
  protected timeout: number;
  protected editReply: boolean;
  protected users: Snowflake[];
  protected message: Message | null = null;
  protected collector: InteractionCollector<ButtonInteraction | StringSelectMenuInteraction> | null = null;
  protected isDestroyed = false;

  constructor(options: BasePaginationOptions) {
    super();
    this.interaction = options.interaction;
    this.pages = options.pages;

    this.timeout = options.timeout || 300_000; // 5 minutes
    this.editReply = options.editReply ?? true;
    this.users = options.users ?? [];
  }

  // Abstract methods to be implemented by subclasses
  abstract createComponents(disabled?: boolean): ActionRowBuilder<ButtonBuilder | StringSelectMenuBuilder>[];
  abstract getComponentType(): MessageComponentType;

  protected getMessageOptions(page: number, components: Components[], disabled = false) {
    const footerText = `Page ${page + 1} of ${this.pages.total}`;

    components.push(
      new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
      new TextDisplayBuilder().setContent(footerText)
    );

    const row = this.createComponents(disabled);
    if (row.length > 0) {
      components.push(new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small));
      components.push(...row);
    }

    return { components };
  }

  async start(components: Components[]): Promise<void> {
    if (this.isDestroyed) {
      throw new Error('Paginator has been destroyed');
    }

    try {
      const message = (await this.interaction.editReply({
        components: [
          new MegamiContainer([
            ...components,
            new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
            ...this.createComponents(false),
          ]),
        ],
        flags: MessageFlags.IsComponentsV2,
      })) as Message;

      this.message = message;

      this.setupCollector();
    } catch (error) {
      logger.error('Failed to start pagination:', error);
      this.emit('error', error as Error, this.pages.current);
    }
  }

  protected setupCollector(): void {
    if (!this.message) return;

    // @ts-expect-error - ignore this
    this.collector = this.message.createMessageComponentCollector({
      componentType: this.getComponentType(),
      time: this.timeout,
      filter: (interaction) => this.filterInteraction(interaction as ButtonInteraction | StringSelectMenuInteraction),
    });

    if (!this.collector) return;

    this.collector.on('collect', (componentInteraction) => {
      this.message = componentInteraction.message;
      this.handleInteraction(componentInteraction);
    });

    this.collector.on('end', (_collected, reason) => {
      if (reason === 'time') {
        this.handleTimeout();
      }
    });
  }

  protected filterInteraction(interaction: ButtonInteraction | StringSelectMenuInteraction): boolean {
    if (this.users.length && !this.users.includes(interaction.user.id)) {
      interaction
        .reply({
          content: 'This pagination is not for you.',
          ephemeral: true,
        })
        .catch(() => {
          // Ignore errors if interaction has already been acknowledged
        });

      return false;
    }

    return true;
  }

  protected abstract handleInteraction(interaction: ButtonInteraction | StringSelectMenuInteraction): Promise<void>;

  protected async updateMessage(disabled = false): Promise<void> {
    if (!this.message || this.isDestroyed) return;

    // @ts-expect-error - ignore this
    const container = new ContainerBuilder(this.message.components[0]!);

    try {
      const options = this.getMessageOptions(this.pages.current, container.components, disabled);

      await this.message.edit({
        components: [new MegamiContainer(options.components)],
      });
    } catch (error) {
      logger.error('Failed to update pagination message:', error);
      this.emit('error', error as Error, this.pages.current);
    }
  }

  protected handleTimeout(): void {
    this.updateMessageOnStop().catch(() => {
      // Ignore errors on timeout cleanup
    });

    this.emit('timeout', this.pages.current);
    this.destroy();
  }

  async stop(interaction?: ButtonInteraction | StringSelectMenuInteraction): Promise<void> {
    if (this.isDestroyed) return;

    await this.updateMessageOnStop();
    this.emit('stop', this.pages.current, interaction || null);
    this.destroy();
  }

  private async updateMessageOnStop(): Promise<void> {
    if (!this.message || this.isDestroyed) return;

    try {
      // Get the current message components and remove interactive elements
      const currentComponents = this.message.components[0];
      if (!currentComponents || currentComponents.type !== ComponentType.Container) {
        return;
      }

      // Extract just the content components (non-interactive)
      const contentComponents: Components[] = [];

      for (const component of currentComponents.components) {
        if (component.type === ComponentType.ActionRow) {
          continue;
        }

        contentComponents.push(component as unknown as Components);
      }

      // @ts-expect-error - ignore
      const container = new ContainerBuilder({ components: contentComponents.map((component) => component.toJSON()) });
      await this.message.edit({ components: [container] });
    } catch (error) {
      logger.error('Failed to update pagination message on stop:', error);
      // Don't emit error here as we're already stopping
    }
  }

  destroy(): void {
    if (this.isDestroyed) return;

    this.isDestroyed = true;
    if (this.collector) {
      this.collector.stop();
      this.collector = null;
    }
    this.removeAllListeners();
  }

  // Getters
  get page(): number {
    return this.pages.current;
  }

  get totalPages(): number {
    return this.pages.total;
  }

  get destroyed(): boolean {
    return this.isDestroyed;
  }
}
