import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Fate Twist',
  name: 'Fate Twist',
  element: 'Arcane',
  manaCost: 15,
  cooldown: 2,
  description: "Twist the threads of fate, lowering the target's luck and dealing damage.",
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    return {
      damage: Math.round(damage * 0.9),
      isCritical,
      appliedEffect: {
        id: 'luck_down',
        name: 'Luck Down',
        duration: 3,
        potency: -10, // -10 luck
        sourceId: caster.id,
      },
      log: `${caster.name} twists fate for ${target.name}, dealing ${Math.round(damage * 0.9)} damage${isCritical ? ' (CRIT!)' : ''} and lowering their luck!`,
    };
  },
});
