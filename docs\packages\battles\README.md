# `packages/battles`

This package contains the core logic for the battle system in the "Megami" project.

## Installation

To install dependencies:

```bash
bun install
```

## Usage

To run:

```bash
bun run index.ts
```

---

## Architecture

The `battles` package provides a complete turn-based battle system. It includes mechanics for skills, status effects, damage calculation, and more.

### `Battle` Class

The `Battle` class is the heart of the battle system. It manages the state of a battle, including the entities involved, turn order, and battle logs.

**Properties:**

-   `entities`: A map of all entities in the battle.
-   `turnOrder`: An array of entity IDs in the order of their turns.
-   `currentTurnIndex`: The index of the current turn in the `turnOrder` array.
-   `entries`: An array of log entries for the battle.
-   `isFinished`: A boolean indicating if the battle is finished.
-   `turn`: The current turn number.

**Methods:**

-   **`constructor(players: BattleEntity[], enemies: BattleEntity[])`**: Creates a new battle instance.
-   **`start()`**: Starts the battle.
-   **`processTurn(action: PlayerAction)`**: Processes a player's action.
-   **`processEnemyTurn(enemy: BattleEntity)`**: Processes an enemy's turn.
-   **`nextTurn()`**: Advances to the next turn.
-   **`endTurn()`**: Ends the current turn.

**Example:**

```typescript
import { Battle } from './structure/Battle';
import type { BattleEntity } from './types';

// Define players and enemies
const players: BattleEntity[] = [/* ... */];
const enemies: BattleEntity[] = [/* ... */];

// Create a new battle
const battle = new Battle(players, enemies);

// Start the battle
battle.start();

// Process a player's action
battle.processTurn({ type: 'BASIC_ATTACK', targetId: 'enemy-1' });
```

### Skills and Status Effects

The package includes a system for defining skills and status effects.

-   **Skills** are defined with `defineSkill` and have properties like `manaCost`, `cooldown`, and an `execute` function that determines the skill's effect.
-   **Status Effects** are defined with `defineEffect` and can have various effects that trigger on different events (e.g., `onTurnStart`).

### Battle Formulas

The `formulas` directory contains various functions for calculating battle-related values, such as:

-   `calculateDamage`
-   `calculateEvasionChance`
-   `calculateDotDamage`
-   `calculateEffectChance`

### Battle Types

The `types.ts` file contains all the core types used in the battle system, such as `BattleEntity`, `Stats`, `ActiveStatusEffect`, and `PlayerAction`.
