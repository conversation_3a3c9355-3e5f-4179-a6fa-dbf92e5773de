import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Aether Pulse',
  name: 'Aether Pulse',
  element: 'Arcane',
  manaCost: 15,
  cooldown: 1,
  description: 'A pulse of raw arcane energy that damages all enemies.',
  execute: (caster, target, formulas) => {
    // In a real implementation, this would target all enemies.
    // For now, we'll just damage the primary target.
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const finalDamage = Math.round(damage * 0.8);
    return {
      damage: finalDamage,
      isCritical,
      log: `${caster.name} releases an Aether Pulse, damaging ${target.name} for ${finalDamage} damage${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
