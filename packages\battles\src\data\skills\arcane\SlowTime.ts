import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Slow Time',
  name: 'Slow Time',
  element: 'Arcane',
  manaCost: 35,
  cooldown: 5,
  description: 'Slows time in an area, reducing the attack and movement speed of enemies.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Slow',
        name: 'Slow Time',
        duration: 3,
        sourceId: caster.id,
      },
      log: `${caster.name} slows time, reducing the speed of their enemies.`,
    };
  },
});
