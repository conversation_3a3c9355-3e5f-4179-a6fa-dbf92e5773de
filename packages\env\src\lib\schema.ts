import { z } from 'zod';

export const DOPPLER_SECRET = z.object({
  computed: z.string(),
  raw: z.string(),
  note: z.string().optional(),
});

export const ENVIRONMENT = z.object({
  // Database
  DATABASE_URL: DOPPLER_SECRET,

  // Cloduinary
  CLOUDINARY_API_KEY: DOPPLER_SECRET,
  CLOUDINARY_API_SECRET: DOPPLER_SECRET,
  CLOUDINARY_CLOUD_NAME: DOPPLER_SECRET,

  // Discord
  DISCORD_CLIENT_APPLICATION_ID: DOPPLER_SECRET,
  DISCORD_CLIENT_PUBLIC_KEY: DOPPLER_SECRET,
  DISCORD_CLIENT_SECRET: DOPPLER_SECRET,
  DISCORD_CLIENT_TOKEN: DOPPLER_SECRET,
});

export const parse = ENVIRONMENT.parse;
