import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Arcane Bolt',
  name: 'Arcane Bolt',
  element: 'Arcane',
  manaCost: 10,
  cooldown: 0,
  description: 'A basic bolt of raw arcane energy. Deals moderate damage.',
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    return {
      damage,
      isCritical,
      log: `${caster.name} fires an Arcane Bolt at ${target.name} for ${damage} damage${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
