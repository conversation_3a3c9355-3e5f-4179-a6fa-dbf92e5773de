'use client';

import { motion } from 'framer-motion';
import { Navbar } from '@/components/navbar';
import { PricingCard } from '@megami/ui/components/jules/pricing-card';

const subscriptions = [
  {
    title: "Collector's Pass",
    price: '$4.99/month',
    features: [
      '+100 Crowns weekly (400 total)',
      '+1 Drop Token daily',
      'Exclusive Role + Icon Frame',
      '+10% EXP boost',
    ],
    buttonText: 'Subscribe',
  },
  {
    title: "Royal Collector's Pass",
    price: '$14.99/month',
    features: [
      '+250 Crowns weekly (1000 total)',
      '+1 Moonlight Drop weekly',
      'Double Daily Rewards',
      'Unlock Premium Buff Menu',
      'Exclusive monthly Celestial Frame',
      "Pass Holder's Box (5% Celestial drop chance)",
    ],
    buttonText: 'Subscribe',
    isFeatured: true,
  },
];

export default function SubscriptionsPage() {
  return (
    <main className="min-h-screen w-full bg-black text-white">
      <Navbar />
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1 }}
        className="container mx-auto px-4 py-8"
      >
        <h1 className="text-5xl font-bold text-center mb-4">Subscriptions</h1>
        <p className="text-lg text-gray-400 text-center mb-12">
          Subscribe to get exclusive benefits and a steady supply of Crowns.
        </p>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
          {subscriptions.map((sub, i) => (
            <motion.div
              key={sub.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: i * 0.1 }}
            >
              <PricingCard {...sub} />
            </motion.div>
          ))}
        </div>
      </motion.div>
    </main>
  );
}
