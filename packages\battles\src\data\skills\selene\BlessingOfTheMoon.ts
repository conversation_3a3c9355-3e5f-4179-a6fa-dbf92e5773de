import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Blessing of the Moon',
  name: 'Blessing of the Moon',
  element: 'Se<PERSON>',
  manaCost: 40,
  cooldown: 6,
  description: 'The caster and their allies are blessed by the moon, granting them increased health regeneration and a chance to dodge attacks.',
  execute: (caster, target, formulas) => {
    // This would require a party and status effect system. For now, it affects the caster.
    return {
      appliedEffect: {
        id: 'Health Regen Up',
        name: 'Blessing of the Moon',
        duration: 4,
        sourceId: caster.id,
      },
      log: `${caster.name} is blessed by the moon.`,
    };
  },
});
