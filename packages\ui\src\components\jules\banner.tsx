import { cn } from '@megami/ui/lib/utils';
import * as React from 'react';

export interface BannerProps extends React.HTMLAttributes<HTMLDivElement> {
  src: string;
  alt: string;
}

const Banner = React.forwardRef<HTMLDivElement, BannerProps>(({ className, src, alt, ...props }, ref) => (
  <div className={cn('relative h-48 w-full overflow-hidden', className)} ref={ref} {...props}>
    <img alt={alt} className="absolute top-0 left-0 h-full w-full object-cover" src={src} />
  </div>
));
Banner.displayName = 'Banner';

export { Banner };
