import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Blood Moon',
  name: 'Blood Moon',
  element: '<PERSON><PERSON>',
  manaCost: 50,
  cooldown: 7,
  description: 'The caster is empowered by the blood moon, gaining lifesteal on their attacks.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Lifesteal',
        name: 'Blood Moon',
        duration: 3,
        sourceId: caster.id,
      },
      log: `${caster.name} is empowered by the blood moon.`,
    };
  },
});
