'use client';

import { cn } from '@megami/ui/lib/utils';
import { motion } from 'framer-motion';
import * as React from 'react';
import { Avatar } from './avatar';

export interface TestimonialCardProps extends React.HTMLAttributes<HTMLDivElement> {
  name: string;
  role: string;
  avatarSrc: string;
  testimonial: string;
}

const TestimonialCard = React.forwardRef<HTMLDivElement, TestimonialCardProps>(
  ({ className, name, role, avatarSrc, testimonial, ...props }, ref) => {
    return (
      <motion.div
        className={cn('relative w-full rounded-lg bg-gray-900/50 p-6 text-white backdrop-blur-sm', className)}
        initial={{ opacity: 0, y: 20 }}
        ref={ref}
        transition={{ duration: 0.5 }}
        viewport={{ once: true }}
        whileInView={{ opacity: 1, y: 0 }}
        {...props}
      >
        <p className="text-gray-300">"{testimonial}"</p>
        <div className="mt-4 flex items-center">
          <Avatar alt={name} className="mr-4" size="sm" src={avatarSrc} />
          <div>
            <h4 className="font-bold">{name}</h4>
            <p className="text-gray-500 text-sm">{role}</p>
          </div>
        </div>
      </motion.div>
    );
  }
);
TestimonialCard.displayName = 'TestimonialCard';

export { TestimonialCard };
