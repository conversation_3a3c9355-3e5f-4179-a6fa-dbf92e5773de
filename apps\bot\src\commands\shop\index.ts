import { SlashCommandBuilder } from 'discord.js';
import { getObject, translate } from '@megami/locale';
import type { SlashCommandSubcommandBuilder } from 'discord.js';
import { defineCommand } from '../../handlers/command';
import buy from './modules/buy';
import view from './modules/view';

export default defineCommand((builder) => ({
  builder: builder
    .setName('shop')
    .setDescription('Browse and purchase drops from the Rift Shop')
    .addSubcommand(view.builder as SlashCommandSubcommandBuilder)
    .addSubcommand(buy.builder as SlashCommandSubcommandBuilder),

  config: {},

  execute: async (interaction) => {
    const subcommand = interaction.options.getSubcommand();

    switch (subcommand) {
      case 'view': {
        await view.execute(interaction);
        break;
      }

      case 'buy': {
        await buy.execute(interaction);
        break;
      }

      default: {
        await interaction.reply({
          content: translate('errors.general.unknown.description'),
          ephemeral: true,
        });
      }
    }
  },
}));
