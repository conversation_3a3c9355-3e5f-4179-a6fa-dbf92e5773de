import { randomUUID } from 'node:crypto';
import fs from 'node:fs';
import os from 'node:os';
import path from 'node:path';
import waifu2x, { type Waifu2xOptions } from 'waifu2x';
export interface UpscaleOptions {
  source: Buffer;
}

const PATHS = {
  TEMP: {
    original: path.join(os.tmpdir(), 'images', 'original'),
    upscaled: path.join(os.tmpdir(), 'images', 'upscaled'),
  },
};

export async function upscale(options: UpscaleOptions, parameters: Waifu2xOptions): Promise<Buffer> {
  fs.existsSync(PATHS.TEMP.original) || fs.mkdirSync(PATHS.TEMP.original, { recursive: true });
  fs.existsSync(PATHS.TEMP.upscaled) || fs.mkdirSync(PATHS.TEMP.upscaled, { recursive: true });

  const uuid = randomUUID();
  const original = path.join(PATHS.TEMP.original, `image_${uuid}.jpg`);
  const upscaled = path.join(PATHS.TEMP.upscaled, `image_upscaled_${uuid}.jpg`);

  fs.writeFileSync(original, options.source);

  const result = await waifu2x.upscaleImage(original, upscaled, parameters);

  const product = fs.readFileSync(result);
  return product;
}
