import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Crescent Strike',
  name: 'Crescent Strike',
  element: 'Selene',
  manaCost: 15,
  cooldown: 2,
  description: 'A quick strike in the shape of a crescent moon.',
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const finalDamage = Math.round(damage * 1.1);
    return {
      damage: finalDamage,
      isCritical,
      log: `${caster.name} strikes ${target.name} with a Crescent Strike for ${finalDamage} damage${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
