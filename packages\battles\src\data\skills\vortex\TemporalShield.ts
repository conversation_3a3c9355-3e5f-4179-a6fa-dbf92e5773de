import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Temporal Shield',
  name: 'Temporal Shield',
  element: 'Vortex',
  manaCost: 15,
  cooldown: 3,
  description: 'Create a temporal barrier that reduces incoming damage and slows attackers.',
  execute: (caster) => {
    return {
      appliedEffect: {
        id: 'temporal_shield',
        name: 'Temporal Shield',
        duration: 3,
        potency: 0.6, // 40% damage reduction
        sourceId: caster.id,
      },
      log: `${caster.name} creates a Temporal Shield, reducing incoming damage!`,
    };
  },
});
