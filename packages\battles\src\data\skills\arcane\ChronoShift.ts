import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Chrono Shift',
  name: 'Chrono Shift',
  element: 'Arcane',
  manaCost: 25,
  cooldown: 4,
  description: 'Manipulates time, granting the caster an extra turn.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Attack Speed Up',
        name: 'Chrono Shift',
        duration: 2,
        sourceId: caster.id,
      },
      log: `${caster.name} shifts time, increasing their attack speed.`,
    };
  },
});
