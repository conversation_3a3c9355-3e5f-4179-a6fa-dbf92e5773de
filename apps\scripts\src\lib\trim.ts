import * as prompt from '@clack/prompts';
import ffmpeg from 'fluent-ffmpeg';

/**
 * Interactively trims a video if the user wants, else copies it as-is.
 *
 * @param input Path to input video
 * @param output Path to output video (trimmed or same as input if skipped)
 */
export async function trim(input: string, output: string): Promise<void> {
  prompt.log.info('Optional: Enter start and end time to trim the video (press Enter to skip).');

  const start = (await prompt.text({
    message: 'Start time (e.g. 00:00:05 or 5):',
    placeholder: 'Leave empty to skip trimming',
    defaultValue: '',
  })) as string;

  const end = (await prompt.text({
    message: 'End time (e.g. 00:00:10 or 10):',
    placeholder: 'Leave empty to skip trimming',
    defaultValue: '',
  })) as string;

  const shouldTrim = start.trim() !== '' || end.trim() !== '';

  return new Promise((resolve, reject) => {
    const cmd = ffmpeg(input).outputOptions('-y');

    if (shouldTrim) {
      if (start.trim() !== '') cmd.setStartTime(start);
      if (end.trim() !== '') cmd.setDuration(`${parseTime(end) - parseTime(start || '0')}`);

      prompt.log.info(`Trimming video from ${start || '0s'} to ${end || 'end'}...`);
    } else {
      prompt.log.info('Skipping trimming — using original video.');
    }

    cmd
      .on('start', (commandLine) => {
        prompt.log.info(`Running: ${commandLine}`);
      })
      .on('error', (err, _stdout, stderr) => {
        prompt.log.error(`Trim error: ${err.message}`);
        prompt.log.error(`ffmpeg stderr: ${stderr}`);
        reject(err);
      })
      .on('end', () => {
        prompt.log.info(`Video saved to: ${output}`);
        resolve();
      })
      .save(output);
  });
}

/**
 * Converts time string (HH:MM:SS or SS) to seconds (number)
 */
function parseTime(time: string): number {
  const parts = time.split(':').map(Number);
  if (parts.some((p) => Number.isNaN(p))) return 0;

  if (parts.length === 1) return parts[0]!;
  if (parts.length === 2) return parts[0]! * 60 + parts[1]!;
  if (parts.length === 3) return parts[0]! * 3600 + parts[1]! * 60 + parts[2]!;
  return 0;
}
