import { index, pgTable, serial, text, timestamp } from 'drizzle-orm/pg-core';

export const notifications = pgTable(
  'notifications',
  {
    id: serial('id').primaryKey(),
    key: text('key').notNull(),
    title: text('title').notNull(),
    content: text('content').notNull(),
    type: text('type').$type<'ANNOUNCEMENT' | 'CHANGELOG' | 'INFO'>().notNull(),
    createdAt: timestamp('created_at').defaultNow().notNull(),
    updatedAt: timestamp('updated_at').defaultNow().notNull(),
  },
  (table) => [index('user_id_idx').on(table.key), index('type_idx').on(table.type)]
);
