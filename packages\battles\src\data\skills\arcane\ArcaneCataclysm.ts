import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Arcane Cataclysm',
  name: 'Arcane Cataclysm',
  element: 'Arcane',
  manaCost: 80,
  cooldown: 10,
  description: 'A cataclysmic explosion of arcane energy that deals massive damage to all enemies.',
  execute: (caster, target, formulas) => {
    // This would require a more complex targeting system.
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const finalDamage = Math.round(damage * 3);
    return {
      damage: finalDamage,
      isCritical,
      log: `${caster.name} unleashes an Arcane Cataclysm, dealing ${finalDamage} damage to all enemies${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
