'use client';

import { cn } from '@megami/ui/lib/utils';
import { motion } from 'framer-motion';
import Link from 'next/link';
import * as React from 'react';

export interface PremiumCardProps extends React.HTMLAttributes<HTMLDivElement> {
  title: string;
  description: string;
  href: string;
  icon: React.ReactNode;
}

const PremiumCard = React.forwardRef<HTMLDivElement, PremiumCardProps>(
  ({ className, title, description, href, icon, ...props }, ref) => {
    return (
      <motion.div className={cn('relative w-full', className)} ref={ref} whileHover={{ scale: 1.05 }} {...props}>
        <Link href={href}>
          <div className="rounded-lg bg-gray-900/50 p-6 text-center text-white backdrop-blur-sm transition-colors hover:bg-gray-800/50">
            <div className="mb-4 flex items-center justify-center text-purple-400">{icon}</div>
            <h3 className="font-bold text-2xl">{title}</h3>
            <p className="mt-2 text-gray-400">{description}</p>
          </div>
        </Link>
      </motion.div>
    );
  }
);
PremiumCard.displayName = 'PremiumCard';

export { PremiumCard };
