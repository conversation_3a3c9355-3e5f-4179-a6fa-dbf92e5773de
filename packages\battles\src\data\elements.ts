export interface Element {
  name: '<PERSON><PERSON>' | 'Vortex' | 'Sirius' | 'Selene';
  description: string;
  advantage: '<PERSON>ane' | 'Vortex' | 'Sirius' | 'Selene';
  disadvantage: 'Arcane' | 'Vortex' | 'Sirius' | 'Selene';
}

export const elements: Readonly<Element[]> = [
  {
    name: '<PERSON><PERSON>',
    description:
      'The embodiment of cosmic chance and raw magical energy. Arcane wielders manipulate fate and reality, drawing power from the unseen forces that bind the universe.',
    advantage: 'Vortex',
    disadvantage: '<PERSON>lene',
  },
  {
    name: 'Vortex',
    description:
      'The inescapable pull of the void and the relentless march of time. Vortex wielders command gravity and temporal forces, consuming light and energy.',
    advantage: '<PERSON>',
    disadvantage: '<PERSON><PERSON>',
  },
  {
    name: '<PERSON>',
    description:
      'The brilliant, primal power of a burning star given form. Sirius wielders are noble and fierce, channeling celestial light to pierce through darkness and illusion.',
    advantage: 'Selene',
    disadvantage: 'Vortex',
  },
  {
    name: '<PERSON><PERSON>',
    description:
      'The power of the moon, shrouded in illusion and dreams. Selene wielders manipulate perception and harness chaotic energy to confuse and misdirect their foes.',
    advantage: '<PERSON>ane',
    disadvantage: '<PERSON>',
  },
];
