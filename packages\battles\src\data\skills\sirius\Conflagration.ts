import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Conflagration',
  name: 'Conflagration',
  element: 'Sirius',
  manaCost: 40,
  cooldown: 6,
  description: 'The target is engulfed in a conflagration, taking heavy damage over time. The conflagration can spread to nearby enemies.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Burn',
        name: 'Conflagration',
        duration: 4,
        sourceId: caster.id,
      },
      log: `${caster.name} engulfs ${target.name} in a conflagration.`,
    };
  },
});
