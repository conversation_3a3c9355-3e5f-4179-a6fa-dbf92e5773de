import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Lunar Flare',
  name: 'Lunar Flare',
  element: 'Selene',
  manaCost: 30,
  cooldown: 4,
  description: 'A flare of lunar energy that damages and burns the target.',
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const finalDamage = Math.round(damage * 1.3);
    // This would also require a status effect system to handle the burn.
    return {
      damage: finalDamage,
      isCritical,
      log: `${caster.name} strikes ${target.name} with a Lunar Flare for ${finalDamage} damage${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
