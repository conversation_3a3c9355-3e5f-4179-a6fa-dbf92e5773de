import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Nightmare Feed',
  name: 'Nightmare Feed',
  element: 'Selene',
  manaCost: 22,
  cooldown: 3,
  description: "Feed on the target's nightmares, dealing damage and healing yourself.",
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const nightmareDamage = Math.round(damage * 1.2);
    const healAmount = Math.round(nightmareDamage * 0.4); // 40% of damage dealt

    caster.stats.health = Math.min(caster.stats.maxHealth, caster.stats.health + healAmount);

    return {
      damage: nightmareDamage,
      isCritical,
      appliedEffect: {
        id: 'nightmare',
        name: 'Nightmare',
        duration: 2,
        potency: Math.round(caster.stats.attack * 0.25), // DoT effect
        sourceId: caster.id,
      },
      log: `${caster.name} feeds on ${target.name}'s nightmares for ${nightmareDamage} damage${isCritical ? ' (CRIT!)' : ''} and heals for ${healAmount} HP!`,
    };
  },
});
