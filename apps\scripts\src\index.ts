import * as prompt from '@clack/prompts';

import { animate as animate5DX } from './commands/5DX';
import { animate as animateEmote } from './commands/emote';

export interface SelectOption {
  label: string;
  value: string;
  hint?: string;
}

const COMMANDS: SelectOption[] = [
  { label: '5DX', value: '5DX', hint: 'Convert MP4 to animated WebP (9:16 Ratio - No BG)' },
  { label: 'Emote', value: 'emote', hint: 'Convert MP4 to GIF (Square - No BG)' },
];

async function main() {
  prompt.intro('Megami Scripts');

  const command = await prompt.select({
    message: 'Which command are you looking to run today?',
    options: [...COMMANDS],
  });

  switch (command) {
    case '5DX':
      await animate5DX();
      break;
    case 'emote':
      await animateEmote();
      break;
    default:
      prompt.log.error('Invalid command selected.');
      break;
  }
}

main().catch((error) => {
  prompt.log.error(`An error occurred in the main function: ${error.message}`);
});
