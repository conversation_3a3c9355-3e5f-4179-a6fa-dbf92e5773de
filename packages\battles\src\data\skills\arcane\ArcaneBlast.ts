import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Arcane Blast',
  name: 'Arcane Blast',
  element: 'Arcane',
  manaCost: 20,
  cooldown: 2,
  description: 'A powerful blast of arcane energy that deals heavy damage.',
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    return {
      damage: Math.round(damage * 1.5),
      isCritical,
      log: `${caster.name} unleashes an Arcane Blast on ${target.name} for ${Math.round(damage * 1.5)} damage${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
