import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Shadowmeld',
  name: 'Shadowmel<PERSON>',
  element: '<PERSON><PERSON>',
  manaCost: 25,
  cooldown: 4,
  description: 'The caster melds into the shadows, becoming invisible and gaining bonus damage on their next attack.',
  execute: (caster, target, formulas) => {
    // This would require a status effect system to handle invisibility and bonus damage.
    return {
      log: `${caster.name} melds into the shadows.`,
    };
  },
});
