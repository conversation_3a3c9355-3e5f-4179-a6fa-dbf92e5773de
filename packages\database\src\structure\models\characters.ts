import { generate } from '@megami/utils/lib/stats';
import { createSelectSchema } from 'drizzle-zod';
import { BaseModel } from '../model';

export class Characters extends BaseModel {
  public schema = createSelectSchema(this.schemas.characters);

  public async get(name: string) {
    return await this.client.instance.query.characters.findFirst({
      where: {
        name,
      },
    });
  }

  public async getAll() {
    return await this.client.instance.query.characters.findMany();
  }

  public async create(name: string, description: string, series: string) {
    const [character] = await this.client.instance
      .insert(this.schemas.characters)
      .values({
        name,
        description,
        stats: generate(),
        seriesId: series,
      })
      .returning()
      .execute();

    return character!;
  }
}
