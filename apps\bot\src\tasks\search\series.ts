import { defineTask } from '../../structure/scheduler';

export default defineTask({
  id: 'search:index:series',
  name: 'Update Series Search Index',
  expression: '0 10 2 * * *', // Daily at 2:10 AM UTC
  enabled: true,
  execute: async (client) => {
    try {
      client.logger.info('Starting series search index update...');

      const series = await client.database.series.getAll();
      const existing = Object.keys(client.search.series.instance.toJSON().documentIds);

      const indexed = new Set(existing);
      const missing = series.filter((s) => !indexed.has(s.id));

      if (missing.length > 0) {
        await client.search.series.add(...missing);
        client.logger.info(`Added ${missing.length} series to search index`);
      } else {
        client.logger.info('No new series to add to search index');
      }

      client.logger.info('Series search index update completed');
    } catch (error) {
      client.logger.error('Failed to update series search index:', error);
      throw error;
    }
  },
});
