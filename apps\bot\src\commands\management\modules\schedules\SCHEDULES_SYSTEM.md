# Megami Schedules Management System

A comprehensive, production-ready scheduler management system integrated into the management command structure as a subgroup. This system provides full control over scheduled tasks with enhanced features, detailed monitoring, and robust error handling.

## System Architecture

### 📁 **File Structure**
```
apps/bot/src/commands/management/modules/schedules/
├── index.ts                    # Main schedules subgroup handler
├── modules/
│   ├── status.ts              # Scheduler status and health monitoring
│   ├── list.ts                # Enhanced task listing with filters
│   ├── enable.ts              # Enable tasks with autocomplete
│   ├── disable.ts             # Disable tasks with safety checks
│   ├── execute.ts             # Manual task execution
│   └── restart.ts             # Scheduler system restart
└── SCHEDULES_SYSTEM.md        # This documentation
```

### 🔧 **Integration Pattern**
- **Subgroup Structure**: Follows the new management subgroup pattern
- **Translation Support**: Full i18n integration with locale keys
- **Container UI**: Modern Discord.js v2 container components
- **Type Safety**: Complete TypeScript coverage
- **Error Handling**: Comprehensive error management

## Command Overview

### 📊 **Status Command** (`/management schedules status`)
**Purpose**: Display comprehensive scheduler health and running task information

**Features:**
- ✅ **Scheduler Status**: Running/Stopped state
- 📈 **Task Statistics**: Total, enabled, running task counts
- 🔄 **Running Tasks**: Currently executing tasks with schedules
- 🖥️ **System Health**: Bot uptime, memory usage, scheduler uptime
- ⏰ **Next Execution**: Calculated next run times

**Output Example:**
```
📅 Scheduler Status
Status: ✅ Running
Total Tasks: 12
Enabled Tasks: 8
Running Tasks: 3

⏰ Currently Running Tasks:
  🔄 Daily Cleanup (daily-cleanup)
    └ Schedule: `0 0 * * *` | Next: in 6 hours
  🔄 User Stats Update (user-stats)
    └ Schedule: `*/15 * * * *` | Next: in 12 minutes

🔧 System Health:
Bot Uptime: 2d 14h 32m
Memory Usage: 245 MB
Scheduler Uptime: 2d 14h 32m
```

### 📋 **List Command** (`/management schedules list [filter]`)
**Purpose**: Display detailed information about all scheduled tasks with filtering options

**Features:**
- 🔍 **Smart Filtering**: All, Enabled, Disabled, Running tasks
- 📊 **Organized Display**: Grouped by status with clear indicators
- ⏰ **Next Execution**: Calculated next run times for each task
- 🚀 **Startup Indicators**: Shows tasks that run on bot startup
- 📈 **Quick Statistics**: Running, enabled, disabled counts
- 🎯 **Legend**: Clear explanation of status indicators

**Filter Options:**
- `all` - Show all tasks (default)
- `enabled` - Show only enabled tasks
- `disabled` - Show only disabled tasks  
- `running` - Show only currently running tasks

**Output Example:**
```
📅 Scheduled Tasks (All)
Total: 12 tasks

✅ Enabled Tasks:
  🔄 Daily Cleanup (daily-cleanup) 🚀
    └ `0 0 * * *` | Next: in 6 hours
    └ Cleans up old data and optimizes database
  ⏸️ User Stats Update (user-stats)
    └ `*/15 * * * *` | Next: in 12 minutes

❌ Disabled Tasks:
  ⏹️ Weekly Reports (weekly-reports)
    └ `0 0 * * 0` | Status: Disabled

Legend:
🔄 Currently Running | ⏸️ Enabled but Idle | ⏹️ Disabled
🚀 Runs on Startup | ✅ Enabled | ❌ Disabled

📊 Quick Stats:
Running: 3 | Enabled: 8 | Disabled: 4
```

### ✅ **Enable Command** (`/management schedules enable <task>`)
**Purpose**: Enable a disabled scheduled task with comprehensive validation

**Features:**
- 🔍 **Smart Autocomplete**: Shows only disabled tasks with status info
- ✅ **Status Validation**: Checks if task is already enabled
- ⏰ **Next Run Calculation**: Shows when task will next execute
- 🚀 **Startup Notification**: Indicates if task runs on startup
- 📝 **Detailed Feedback**: Comprehensive success/error messages
- 🔒 **Safety Checks**: Validates task existence and state

**Autocomplete Format:**
```
Task Name (task-id) - cron-expression
Example: Daily Cleanup (daily-cleanup) - 0 0 * * *
```

**Success Output:**
```
✅ Task Enabled Successfully
Task: Daily Cleanup (`daily-cleanup`)
Schedule: `0 0 * * *`
Next Run: in 6 hours
Status: 🔄 Now Running
Note: This task will also run immediately on bot startup.
```

### ❌ **Disable Command** (`/management schedules disable <task> [force]`)
**Purpose**: Disable an enabled scheduled task with safety mechanisms

**Features:**
- 🔍 **Smart Autocomplete**: Shows enabled tasks with running status
- ⚠️ **Running Task Protection**: Warns if task is currently executing
- 💪 **Force Option**: Override protection for running tasks
- 🛡️ **Safety Validation**: Multiple confirmation layers
- 📝 **Detailed Feedback**: Clear status updates and warnings
- 🔒 **State Verification**: Validates task existence and current state

**Options:**
- `task` (required): Task ID to disable
- `force` (optional): Force disable even if running

**Running Task Warning:**
```
⚠️ Task Currently Running
Task: Daily Cleanup (`daily-cleanup`)
Status: 🔄 Currently Executing
This task is currently running. To disable it anyway, use the `force` option.
Warning: Force disabling may interrupt the current execution.
```

### ⚡ **Execute Command** (`/management schedules execute <task> [force]`)
**Purpose**: Manually execute a scheduled task with real-time feedback

**Features:**
- 🔍 **Smart Autocomplete**: All tasks with status indicators (Ready/Running/Disabled)
- ⚠️ **State Validation**: Checks disabled and running states
- 💪 **Force Execution**: Override state protections
- ⏱️ **Real-time Updates**: Live execution progress
- 📊 **Execution Metrics**: Timing and performance data
- 📝 **Output Capture**: Shows task output and errors
- 🔒 **Conflict Prevention**: Warns about multiple instances

**Options:**
- `task` (required): Task ID to execute
- `force` (optional): Force execute regardless of state

**Execution Flow:**
```
⏳ Executing Task...
Task: Daily Cleanup (`daily-cleanup`)
Status: 🔄 Starting Execution
Please wait while the task executes...

↓ (Updates to) ↓

✅ Task Executed Successfully
Task: Daily Cleanup (`daily-cleanup`)
Execution Time: 2,847ms
Status: ✅ Completed Successfully
Output: ```
Cleaned 1,247 old records
Optimized 3 database tables
Freed 45MB of storage
```
Executed by: @username
```

### 🔄 **Restart Command** (`/management schedules restart [force]`)
**Purpose**: Restart the entire scheduler system with comprehensive safety checks

**Features:**
- ⚠️ **Running Task Protection**: Lists all running tasks before restart
- 💪 **Force Restart**: Override protection for emergency situations
- ⏱️ **Real-time Progress**: Live restart status updates
- 📊 **Restart Metrics**: Timing and performance data
- 📈 **Before/After Comparison**: Status comparison pre/post restart
- 🚀 **Startup Task Tracking**: Shows which tasks started on restart
- 🔒 **State Validation**: Ensures clean restart process

**Options:**
- `force` (optional): Force restart even with running tasks

**Restart Summary:**
```
✅ Scheduler Restarted Successfully
Restart Time: 1,234ms
Previous Status: Running (12 tasks)
New Status: ✅ Running (12 tasks)

📊 Restart Summary:
Tasks Terminated: 3
Tasks Restarted: 5
Enabled Tasks: 8
Total Tasks: 12

🚀 Tasks Started on Restart:
  ✅ Daily Cleanup (daily-cleanup)
  ✅ User Stats Update (user-stats)
  ✅ Cache Warmer (cache-warmer)
```

## Technical Features

### 🎨 **Modern UI Components**
- **Container-based UI**: Uses Discord.js v2 container system
- **Consistent Styling**: Matches other management commands
- **Rich Formatting**: Emojis, code blocks, and structured layout
- **Responsive Design**: Adapts to different data amounts

### 🔍 **Smart Autocomplete**
- **Context-aware**: Shows relevant tasks based on command
- **Status Indicators**: Visual cues for task states
- **Filtered Results**: Only shows applicable tasks
- **Descriptive Names**: Includes schedule and status info

### 🛡️ **Safety & Validation**
- **State Verification**: Comprehensive task state checking
- **Conflict Prevention**: Warns about dangerous operations
- **Force Options**: Override protections when necessary
- **Error Recovery**: Graceful handling of edge cases

### 📊 **Enhanced Monitoring**
- **Real-time Status**: Live task execution tracking
- **Performance Metrics**: Execution timing and resource usage
- **Health Monitoring**: System health and uptime tracking
- **Detailed Logging**: Comprehensive audit trail

### 🌍 **Internationalization**
- **Full i18n Support**: Complete translation integration
- **Locale Keys**: Structured translation keys
- **Consistent Naming**: Follows established patterns

## Usage Examples

### 📋 **Basic Task Management**
```bash
# View scheduler status
/management schedules status

# List all tasks
/management schedules list

# List only running tasks
/management schedules list filter:running

# Enable a task
/management schedules enable task:daily-cleanup

# Disable a running task (with force)
/management schedules disable task:user-stats force:true
```

### ⚡ **Task Execution**
```bash
# Execute a task manually
/management schedules execute task:daily-cleanup

# Force execute a disabled task
/management schedules execute task:weekly-reports force:true
```

### 🔄 **System Management**
```bash
# Restart scheduler (safe)
/management schedules restart

# Force restart with running tasks
/management schedules restart force:true
```

## Integration Points

### 🔗 **Management Command Structure**
- Integrated as a subgroup under `/management`
- Follows established patterns from items subgroup
- Consistent with other management modules

### 📦 **Scheduler Service Integration**
- Assumes `interaction.client.scheduler` service exists
- Uses standard scheduler interface methods
- Compatible with existing scheduler implementations

### 🎯 **Permission System**
- Inherits management command permissions
- Restricted to users with `manager` role
- Audit logging for all operations

This comprehensive scheduler management system provides administrators with powerful tools to monitor, control, and maintain scheduled tasks while ensuring system stability and providing excellent user experience through modern UI components and comprehensive safety features.
