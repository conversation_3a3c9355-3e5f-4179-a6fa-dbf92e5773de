import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Living Bomb',
  name: 'Living Bomb',
  element: 'Sirius',
  manaCost: 40,
  cooldown: 6,
  description: 'The target becomes a living bomb, exploding after a few turns and dealing massive damage to themselves and all nearby enemies.',
  execute: (caster, target, formulas) => {
    // This would require a status effect system to handle the bomb and a more complex targeting system.
    return {
      log: `${caster.name} turns ${target.name} into a Living Bomb.`,
    };
  },
});
