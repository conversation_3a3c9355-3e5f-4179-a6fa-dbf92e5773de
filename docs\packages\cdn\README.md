# `packages/cdn`

This package provides a simple interface for interacting with the Cloudinary CDN.

## Installation

To install dependencies:

```bash
bun install
```

## Usage

To run:

```bash
bun run index.ts
```

---

## Architecture

The `cdn` package is a wrapper around the `cloudinary` library, making it easy to upload files.

### `CDN` Class

The `CDN` class is used to configure and interact with the Cloudinary API.

**Methods:**

-   **`constructor(options: CDNOptions)`**: Creates a new `CDN` instance and configures the `cloudinary` library.
-   **`configure(options: CDNOptions)`**: Re-configures the `cloudinary` library with new options.
-   **`upload(file: string, options: UploadApiOptions)`**: Uploads a file to Cloudinary.
    -   `file`: The path to the file to upload.
    -   `options`: Cloudinary upload options.
    -   **Returns:** A promise that resolves to the upload result.

**Example:**

```typescript
import { CDN } from './index';

// Configure the CDN
const cdn = new CDN({
  cloud_name: 'your_cloud_name',
  api_key: 'your_api_key',
  api_secret: 'your_api_secret',
});

// Upload a file
const result = await cdn.upload('path/to/your/file.jpg', {
  public_id: 'my-image',
});

console.log(result);
```
