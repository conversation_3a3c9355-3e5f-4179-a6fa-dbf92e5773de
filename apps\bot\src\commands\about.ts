import os from 'node:os';
import process from 'node:process';
import { currencies } from '@megami/config/lib/currencies';
import { elements } from '@megami/config/lib/elements';
import { emotes } from '@megami/config/lib/emotes';
import { getObject, translate } from '@megami/locale';
import { SeparatorBuilder, SeparatorSpacingSize, TextDisplayBuilder } from 'discord.js';
import { defineCommand } from '../handlers/command';
import { MegamiContainer } from '../helpers/containers';
import { defer } from '../helpers/defer';
import { formatters } from '../helpers/formatters';

function formatUptime(options: number): string {
  const seconds = Math.floor(options);
  const d = Math.floor(seconds / (3600 * 24));
  const h = Math.floor((seconds % (3600 * 24)) / 3600);
  const m = Math.floor((seconds % 3600) / 60);
  const s = Math.floor(seconds % 60);

  return [d > 0 ? `${d}d` : '', h > 0 ? `${h}h` : '', m > 0 ? `${m}m` : '', s > 0 ? `${s}s` : '']
    .filter(Boolean)
    .join(' ');
}

export default defineCommand((builder) => ({
  builder: builder
    .setName(translate('commands.about.name'))
    .setNameLocalizations(getObject('commands.about.name'))
    .setDescription(translate('commands.about.description'))
    .setDescriptionLocalizations(getObject('commands.about.description')),
  config: {},
  execute: async (interaction) => {
    await defer(interaction, false);

    const info: Record<string, string> = {};

    info.elements = formatters.lists(
      elements.map(
        (e) =>
          `${emotes.elements[e.name.toLowerCase() as keyof typeof emotes.elements]} **${e.name}** - Strong Against **${e.advantage}**`
      )
    );

    info.currencies = formatters.lists(
      currencies.map(
        (c) => `${emotes.currencies[c.name.toLowerCase() as keyof typeof emotes.currencies]} **${c.name} (${c.tier})**`
      )
    );

    info.memory = (process.memoryUsage().rss / 1024 / 1024).toFixed(2);
    info.uptime = formatUptime(process.uptime());

    info.system = formatters.lists([
      `**Node.js Version:** \`${process.version}\``,
      `**Platform:** \`${os.platform()}\``,
      `**CPU Arch:** \`${os.arch()}\``,
      `**Memory Usage:** \`${info.memory} MB\``,
      `**Uptime:** \`${info.uptime}\``,
    ]);

    const container = new MegamiContainer([
      new TextDisplayBuilder().setContent(
        'Welcome to **Megami**! I am a celestial entity dedicated to helping you collect and cherish your favorite characters from across the realms of anime, manga, and games.'
      ),
      new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
      new TextDisplayBuilder().setContent(
        'My primary function is to provide a rich character collection experience. You can summon, trade, and power up characters using a unique elemental and economic system.'
      ),
      new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
      new TextDisplayBuilder().setContent(
        'Every character is aligned with one of four powerful elements, creating a strategic layer of advantages and disadvantages:'
      ),
      new TextDisplayBuilder().setContent(info.elements),
      new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
      new TextDisplayBuilder().setContent(
        'The realms operate on a multi-tiered economy. Use these to expand your collection and power:'
      ),
      new TextDisplayBuilder().setContent(info.currencies),
      new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
      new TextDisplayBuilder().setContent('System Information:'),
      new TextDisplayBuilder().setContent(info.system),
    ]);

    return await interaction.editReply({ components: [container.toJSON()] });
  },
}));
