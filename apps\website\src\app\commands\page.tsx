'use client';

import { motion } from 'framer-motion';
import { Navbar } from '@/components/navbar';
import { Card } from '@megami/ui/components/jules/card';

const commands = [
  { name: '/profile', description: 'View your Megami profile.' },
  { name: '/daily', description: 'Claim your daily rewards.' },
  { name: ' /gacha', description: 'Roll the gacha for new cards.' },
  { name: '/inventory', description: 'View your card collection.' },
  { name: '/trade', description: 'Trade cards with other players.' },
  { name: '/battle', description: 'Battle with other players.' },
  { name: '/shop', description: 'Buy items from the shop.' },
  { name: '/leaderboard', description: 'View the server leaderboard.' },
];

export default function CommandsPage() {
  return (
    <main className="min-h-screen w-full bg-black text-white">
      <Navbar />
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1 }}
        className="container mx-auto px-4 py-8"
      >
        <h1 className="text-4xl font-bold text-center mb-8">Commands</h1>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {commands.map((command, i) => (
            <motion.div
              key={command.name}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: i * 0.1 }}
            >
              <Card>
                <h3 className="text-xl font-bold">{command.name}</h3>
                <p className="text-gray-400">{command.description}</p>
              </Card>
            </motion.div>
          ))}
        </div>
      </motion.div>
    </main>
  );
}
