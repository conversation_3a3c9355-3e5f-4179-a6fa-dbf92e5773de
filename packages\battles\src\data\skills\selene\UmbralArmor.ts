import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Umbral Armor',
  name: 'Umbral Armor',
  element: 'Selene',
  manaCost: 30,
  cooldown: 5,
  description: 'The caster is protected by umbral armor, increasing their defense and reflecting a portion of incoming damage.',
  execute: (caster, target, formulas) => {
    // This would require a status effect system to handle the defense buff and damage reflection.
    return {
      log: `${caster.name} is protected by Umbral Armor.`,
    };
  },
});
