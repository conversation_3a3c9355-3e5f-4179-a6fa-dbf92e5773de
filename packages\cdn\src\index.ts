/* biome-ignore-all lint/suspicious/useAwait: This is a top-level await for the CDN configuration. */
import { v2 as cloudinary, type UploadApiOptions } from 'cloudinary';

export type * from 'cloudinary';

export interface CDNOptions {
  cloud_name: string;
  api_key: string;
  api_secret: string;
}

export class CDN {
  public options: CDNOptions;

  constructor(options: CDNOptions) {
    this.options = options;
    cloudinary.config({
      cloud_name: options.cloud_name,
      api_key: options.api_key,
      api_secret: options.api_secret,
      secure: true,
    });
  }

  public configure(options: CDNOptions) {
    this.options = options;
    cloudinary.config({
      cloud_name: options.cloud_name,
      api_key: options.api_key,
      api_secret: options.api_secret,
      secure: true,
    });
  }

  public async upload(file: string, options: UploadApiOptions) {
    return await cloudinary.uploader.upload(file, options);
  }
}
