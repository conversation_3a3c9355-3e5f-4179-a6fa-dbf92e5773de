import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Energy Drain',
  name: 'Energy Drain',
  element: 'Vortex',
  manaCost: 10,
  cooldown: 2,
  description: 'Drain energy from the target, dealing damage and restoring your mana.',
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const manaDrained = Math.min(12, target.stats.mana);
    target.stats.mana -= manaDrained;
    caster.stats.mana = Math.min(caster.stats.maxMana, caster.stats.mana + manaDrained);

    return {
      damage: Math.round(damage * 0.8),
      isCritical,
      log: `${caster.name} drains energy from ${target.name} for ${Math.round(damage * 0.8)} damage${isCritical ? ' (CRIT!)' : ''} and restores ${manaDrained} mana!`,
    };
  },
});
