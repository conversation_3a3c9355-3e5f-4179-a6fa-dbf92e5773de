import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Phase Shift',
  name: 'Phase Shift',
  element: 'Selene',
  manaCost: 12,
  cooldown: 2,
  description: 'Shift between phases of reality, boosting speed and luck while dealing damage.',
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);

    return {
      damage: Math.round(damage * 1.0),
      isCritical,
      appliedEffect: {
        id: 'phase_shift',
        name: 'Phase Shift',
        duration: 3,
        potency: 7, // +7 speed and luck
        sourceId: caster.id,
      },
      log: `${caster.name} phase shifts to strike ${target.name} for ${Math.round(damage * 1.0)} damage${isCritical ? ' (CRIT!)' : ''} and gains ethereal speed!`,
    };
  },
});
