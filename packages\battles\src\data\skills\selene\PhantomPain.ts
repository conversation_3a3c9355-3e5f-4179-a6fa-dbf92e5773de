import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Phantom Pain',
  name: 'Phantom Pain',
  element: '<PERSON><PERSON>',
  manaCost: 25,
  cooldown: 4,
  description: 'Inflicts the target with phantom pain, causing them to take damage whenever they use a skill.',
  execute: (caster, target, formulas) => {
    // This would require a status effect system to handle the phantom pain.
    return {
      log: `${caster.name} inflicts ${target.name} with Phantom Pain.`,
    };
  },
});
