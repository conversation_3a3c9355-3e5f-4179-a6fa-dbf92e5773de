import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Starlight',
  name: 'Starlight',
  element: 'Selene',
  manaCost: 10,
  cooldown: 1,
  description: 'A beam of starlight that heals an ally.',
  execute: (caster, target, formulas) => {
    // This would require a targeting system that can target allies and a healing formula.
    return {
      log: `${caster.name} heals an ally with Starlight.`,
    };
  },
});
