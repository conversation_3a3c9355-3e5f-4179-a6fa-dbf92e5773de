import { getObject, translate } from '@megami/locale';
import { defineSubCommand } from '../../../handlers/command';

export default defineSubCommand((builder) => ({
  builder: builder
    .setName(translate('commands.management.modules.disable.name'))
    .setNameLocalizations(getObject('commands.management.modules.disable.name'))
    .setDescription(translate('commands.management.modules.disable.description'))
    .setDescriptionLocalizations(getObject('commands.management.modules.disable.description'))
    .addStringOption((option) =>
      option
        .setName(translate('commands.management.modules.disable.options.command.name'))
        .setNameLocalizations(getObject('commands.management.modules.disable.options.command.name'))
        .setDescription(translate('commands.management.modules.disable.options.command.description'))
        .setDescriptionLocalizations(getObject('commands.management.modules.disable.options.command.description'))
        .setRequired(true)
        .setAutocomplete(true)
    ),
  config: {},
  autocomplete: async (interaction) => {
    const focused = interaction.options.getFocused(true);

    if (focused.name === translate('commands.management.modules.disable.options.command.name')) {
      const commands = interaction.client.storage.commands
        .filter((command) => !command.config.disabled)
        .map((command) => command.builder.name);

      await interaction.respond(
        commands.map((command) => ({
          name: command,
          value: command,
        }))
      );
    }
  },
  execute: async (interaction) => {
    const command = interaction.options.getString('command', true);

    const data = interaction.client.storage.commands.get(command);

    if (!data) {
      return await interaction.reply({
        content: 'That command does not exist.',
        ephemeral: true,
      });
    }

    if (data.config.disabled) {
      return await interaction.reply({
        content: 'That command is already disabled.',
        ephemeral: true,
      });
    }

    data.config.disabled = true;
    await interaction.reply({
      content: 'Command disabled.',
      ephemeral: true,
    });
  },
}));
