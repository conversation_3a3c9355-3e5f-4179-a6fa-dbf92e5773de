'use client';

import { motion } from 'framer-motion';
import { Navbar } from '@/components/navbar';

export default function BlogPostPage({ params }: { params: { slug: string } }) {
  return (
    <main className="min-h-screen w-full bg-black text-white">
      <Navbar />
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1 }}
        className="container mx-auto px-4 py-8"
      >
        <div className="prose prose-invert mx-auto">
          <h1 className="text-4xl font-bold">Blog Post: {params.slug}</h1>
          <p>This is a placeholder for the blog post content.</p>
        </div>
      </motion.div>
    </main>
  );
}
