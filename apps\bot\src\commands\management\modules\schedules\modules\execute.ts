import { getObject, translate } from '@megami/locale';
import { <PERSON><PERSON><PERSON><PERSON>, SeparatorBuilder, SeparatorSpacingSize, TextDisplayBuilder } from 'discord.js';
import { defineSubCommand } from '../../../../../handlers/command';
import { MegamiContainer } from '../../../../../helpers/containers';
import { defer } from '../../../../../helpers/defer';

export default defineSubCommand((builder) => ({
  builder: builder
    .setName(translate('commands.management.modules.schedules.modules.execute.name'))
    .setNameLocalizations(getObject('commands.management.modules.schedules.modules.execute.name'))
    .setDescription(translate('commands.management.modules.schedules.modules.execute.description'))
    .setDescriptionLocalizations(getObject('commands.management.modules.schedules.modules.execute.description'))
    .addStringOption((option) =>
      option
        .setName(translate('commands.management.modules.schedules.modules.execute.options.task.name'))
        .setNameLocalizations(getObject('commands.management.modules.schedules.modules.execute.options.task.name'))
        .setDescription(translate('commands.management.modules.schedules.modules.execute.options.task.description'))
        .setDescriptionLocalizations(
          getObject('commands.management.modules.schedules.modules.execute.options.task.description')
        )
        .setRequired(true)
        .setAutocomplete(true)
    )
    .addBooleanOption((option) =>
      option
        .setName(translate('commands.management.modules.schedules.modules.execute.options.force.name'))
        .setNameLocalizations(getObject('commands.management.modules.schedules.modules.execute.options.force.name'))
        .setDescription(translate('commands.management.modules.schedules.modules.execute.options.force.description'))
        .setDescriptionLocalizations(
          getObject('commands.management.modules.schedules.modules.execute.options.force.description')
        )
        .setRequired(false)
    ),
  config: {},
  execute: async (interaction) => {
    await defer(interaction, false);

    const taskId = interaction.options.getString('task', true);
    const force = interaction.options.getBoolean('force');
    const scheduler = interaction.client.scheduler;

    try {
      // Check if task exists
      const task = scheduler.getTask(taskId);
      if (!task) {
        const container = new MegamiContainer([
          new TextDisplayBuilder().setContent('❌ **Task Not Found**'),
          new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
          new TextDisplayBuilder().setContent(`Task with ID \`${taskId}\` does not exist.`),
          new TextDisplayBuilder().setContent('Use `/management schedules list` to see available tasks.'),
        ]);

        return await interaction.editReply({
          components: [container],
          flags: MessageFlags.IsComponentsV2,
        });
      }

      // Check if task is disabled and force is not enabled
      if (!(task.enabled || force)) {
        const container = new MegamiContainer([
          new TextDisplayBuilder().setContent('⚠️ **Task is Disabled**'),
          new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
          new TextDisplayBuilder().setContent(`**Task:** ${task.name} (\`${taskId}\`)`),
          new TextDisplayBuilder().setContent('**Status:** ⏹️ Currently Disabled'),
          new TextDisplayBuilder().setContent("This task is disabled and won't execute normally."),
          new TextDisplayBuilder().setContent('To execute it anyway, use the `force` option.'),
        ]);

        return await interaction.editReply({
          components: [container],
          flags: MessageFlags.IsComponentsV2,
        });
      }

      // Check if task is already running and force is not enabled
      const isRunning = scheduler.getRunningTasks().some((rt) => rt.id === taskId);
      if (isRunning && !force) {
        const container = new MegamiContainer([
          new TextDisplayBuilder().setContent('⚠️ **Task Already Running**'),
          new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
          new TextDisplayBuilder().setContent(`**Task:** ${task.name} (\`${taskId}\`)`),
          new TextDisplayBuilder().setContent('**Status:** 🔄 Currently Executing'),
          new TextDisplayBuilder().setContent('This task is already running.'),
          new TextDisplayBuilder().setContent('To start another instance anyway, use the `force` option.'),
          new TextDisplayBuilder().setContent('**Warning:** Multiple instances may cause conflicts.'),
        ]);

        return await interaction.editReply({
          components: [container],
          flags: MessageFlags.IsComponentsV2,
        });
      }

      // Show initial execution message
      const initialContainer = new MegamiContainer([
        new TextDisplayBuilder().setContent('⏳ **Executing Task...**'),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent(`**Task:** ${task.name} (\`${taskId}\`)`),
        new TextDisplayBuilder().setContent('**Status:** 🔄 Starting Execution'),
        new TextDisplayBuilder().setContent('Please wait while the task executes...'),
      ]);

      await interaction.editReply({
        components: [initialContainer],
        flags: MessageFlags.IsComponentsV2,
      });

      // Execute the task
      const startTime = Date.now();
      const result = await scheduler.executeTaskById(taskId);
      const executionTime = Date.now() - startTime;

      if (result) {
        const container = new MegamiContainer([
          new TextDisplayBuilder().setContent('✅ **Task Executed Successfully**'),
          new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
          new TextDisplayBuilder().setContent(`**Task:** ${task.name} (\`${taskId}\`)`),
          new TextDisplayBuilder().setContent(`**Execution Time:** ${executionTime}ms`),
          new TextDisplayBuilder().setContent('**Status:** ✅ Completed Successfully'),
          new TextDisplayBuilder().setContent(`**Executed by:** ${interaction.user.tag}`),
        ]);

        await interaction.editReply({
          components: [container],
          flags: MessageFlags.IsComponentsV2,
        });

        // Log the successful execution
        interaction.client.logger.info(
          `Task ${taskId} manually executed by ${interaction.user.tag} (${executionTime}ms)`
        );
      } else {
        const container = new MegamiContainer([
          new TextDisplayBuilder().setContent('❌ **Task Execution Failed**'),
          new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
          new TextDisplayBuilder().setContent(`**Task:** ${task.name} (\`${taskId}\`)`),
          new TextDisplayBuilder().setContent(`**Execution Time:** ${executionTime}ms`),
          new TextDisplayBuilder().setContent('**Status:** ❌ Failed'),
          new TextDisplayBuilder().setContent('Check the logs for more detailed error information.'),
        ]);

        await interaction.editReply({
          components: [container],
          flags: MessageFlags.IsComponentsV2,
        });
      }
    } catch (error) {
      const container = new MegamiContainer([
        new TextDisplayBuilder().setContent('❌ **Execution Error**'),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent('An unexpected error occurred while executing the task.'),
        new TextDisplayBuilder().setContent('Please check the logs or contact support if the issue persists.'),
      ]);

      await interaction.editReply({
        components: [container],
        flags: MessageFlags.IsComponentsV2,
      });

      interaction.client.logger.error(`Error executing task ${taskId}:`, error);
    }
  },
  autocomplete: async (interaction) => {
    const focusedValue = interaction.options.getFocused();
    const scheduler = interaction.client.scheduler;

    // Get all tasks for autocomplete, prioritizing enabled ones
    const allTasks = scheduler
      .getAllTasks()
      .filter(
        (task) =>
          task.id.toLowerCase().includes(focusedValue.toLowerCase()) ||
          task.name.toLowerCase().includes(focusedValue.toLowerCase())
      )
      .sort((a, b) => {
        // Sort enabled tasks first
        if (a.enabled && !b.enabled) return -1;
        if (!a.enabled && b.enabled) return 1;
        return a.name.localeCompare(b.name);
      })
      .slice(0, 25);

    const choices = allTasks.map((task) => {
      const isRunning = scheduler.getRunningTasks().some((rt) => rt.id === task.id);
      let status = '';
      if (!task.enabled) status = ' (Disabled)';
      else if (isRunning) status = ' (Running)';
      else status = ' (Ready)';

      return {
        name: `${task.name} (${task.id})${status} - ${task.expression}`,
        value: task.id,
      };
    });

    await interaction.respond(choices);
  },
}));
