'use client';

import { cn } from '@megami/ui/lib/utils';
import { motion } from 'framer-motion';
import * as React from 'react';

export interface TimelineItemProps extends React.HTMLAttributes<HTMLDivElement> {
  title: string;
  date: string;
  children: React.ReactNode;
}

const TimelineItem = React.forwardRef<HTMLDivElement, TimelineItemProps>(
  ({ className, title, date, children, ...props }, ref) => {
    const itemVariants = {
      hidden: { opacity: 0, x: -20 },
      visible: { opacity: 1, x: 0, transition: { duration: 0.5 } },
    };

    return (
      <motion.div
        animate="visible"
        className={cn('relative border-gray-700 border-l pl-8', className)}
        initial="hidden"
        ref={ref}
        variants={itemVariants}
        {...props}
      >
        <div className="-left-1.5 absolute top-1.5 h-3 w-3 rounded-full bg-purple-500" />
        <h3 className="font-bold text-xl">{title}</h3>
        <p className="mb-2 text-gray-500 text-sm">{date}</p>
        <div className="text-gray-400">{children}</div>
      </motion.div>
    );
  }
);
TimelineItem.displayName = 'TimelineItem';

export { TimelineItem };
