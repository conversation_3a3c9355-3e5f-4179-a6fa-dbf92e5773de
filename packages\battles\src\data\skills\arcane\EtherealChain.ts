import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Ethereal Chain',
  name: 'Ethereal Chain',
  element: 'Arcane',
  manaCost: 14,
  cooldown: 2,
  description: 'Bind the target with ethereal chains, reducing their speed and dealing damage.',
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    return {
      damage: Math.round(damage * 0.8),
      isCritical,
      appliedEffect: {
        id: 'speed_down',
        name: 'Speed Down',
        duration: 2,
        potency: -5, // -5 speed
        sourceId: caster.id,
      },
      log: `${caster.name} binds ${target.name} with Ethereal Chain, dealing ${Math.round(damage * 0.8)} damage${isCritical ? ' (CRIT!)' : ''} and reducing their speed!`,
    };
  },
});
