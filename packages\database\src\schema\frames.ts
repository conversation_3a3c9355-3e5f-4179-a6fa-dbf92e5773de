import { pgTable, serial, text, timestamp } from 'drizzle-orm/pg-core';

export const frames = pgTable('frames', {
  id: serial('id').primaryKey(),
  name: text('name').notNull(),
  url: text('url').notNull(),
  rarity: text('rarity').$type<'STARDUST' | 'MOONLIGHT'>().notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});
