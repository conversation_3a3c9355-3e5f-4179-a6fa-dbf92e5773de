import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Chaos Wave',
  name: 'Chaos Wave',
  element: 'Selene',
  manaCost: 20,
  cooldown: 2,
  description: 'Unleash chaotic lunar energy with unpredictable effects and damage.',
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);

    // Random chaos effects
    const chaosRoll = Math.random();
    let chaosMultiplier = 1.0;
    let chaosEffect = '';

    if (chaosRoll < 0.3) {
      chaosMultiplier = 1.8; // High damage
      chaosEffect = ' with devastating force';
    } else if (chaosRoll < 0.6) {
      chaosMultiplier = 0.6; // Low damage but applies debuff
      chaosEffect = ' with weakening energy';
    } else {
      chaosMultiplier = 1.2; // Normal enhanced damage
      chaosEffect = ' with chaotic energy';
    }

    return {
      damage: Math.round(damage * chaosMultiplier),
      isCritical,
      appliedEffect:
        chaosRoll >= 0.3 && chaosRoll < 0.6
          ? {
              id: 'chaos_weakness',
              name: 'Chaos Weakness',
              duration: 2,
              potency: -6, // -6 to random stat
              sourceId: caster.id,
            }
          : undefined,
      log: `${caster.name} unleashes a Chaos Wave on ${target.name} for ${Math.round(damage * chaosMultiplier)} damage${isCritical ? ' (CRIT!)' : ''}${chaosEffect}!`,
    };
  },
});
