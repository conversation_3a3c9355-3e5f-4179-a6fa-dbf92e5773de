import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Telekinesis',
  name: 'Telekinesis',
  element: 'Arcane',
  manaCost: 10,
  cooldown: 1,
  description: 'Hurls a nearby object at the target, dealing physical damage.',
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target, 'physical');
    const finalDamage = Math.round(damage * 0.9);
    return {
      damage: finalDamage,
      isCritical,
      log: `${caster.name} hurls an object at ${target.name} with Telekinesis, dealing ${finalDamage} damage${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
