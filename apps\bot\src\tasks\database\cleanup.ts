import { defineTask } from '../../structure/scheduler';

export default defineTask({
  id: 'cleanup',
  name: 'Database Cleanup',
  expression: '0 */10 * * * *', // Every 10 minutes
  enabled: true,
  execute: async (client) => {
    try {
      await client.database.effects.cleanupExpiredEffects();
    } catch (error) {
      client.logger.error('Error during effects cleanup:', error);
    }
  },
});
