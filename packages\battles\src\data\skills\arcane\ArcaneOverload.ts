import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Arcane Overload',
  name: 'Arcane Overload',
  element: 'Arcane',
  manaCost: 30,
  cooldown: 4,
  description: 'Overload the target with arcane energy, dealing massive damage but also damaging yourself.',
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const overloadDamage = Math.round(damage * 2.2);
    const selfDamage = Math.round(overloadDamage * 0.3);
    caster.stats.health = Math.max(0, caster.stats.health - selfDamage);
    return {
      damage: overloadDamage,
      isCritical,
      log: `${caster.name} unleashes Arcane Overload on ${target.name} for ${overloadDamage} damage${isCritical ? ' (CRIT!)' : ''}, but takes ${selfDamage} recoil damage!`,
    };
  },
});
