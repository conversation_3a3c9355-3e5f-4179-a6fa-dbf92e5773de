/* biome-ignore-all lint/nursery/noAwaitInLoop: Better this way */
import fs from 'node:fs';
import path from 'node:path';
import {
  type AutocompleteInteraction,
  type ChatInputCommandInteraction,
  SlashCommandBuilder,
  SlashCommandSubcommandBuilder,
  SlashCommandSubcommandGroupBuilder,
  type SlashCommandSubcommandsOnlyBuilder,
} from 'discord.js';
import type { MegamiClient } from '../structure/client';

export async function defineCommands(client: MegamiClient) {
  const folder = path.join(process.cwd(), 'src', 'commands');
  const entries = fs.readdirSync(folder, { withFileTypes: true });

  // Collect .ts files in the root of commands folder
  let files = entries
    .filter((entry) => entry.isFile() && entry.name.endsWith('.ts'))
    .map((entry) => ({
      ...entry,
      parentPath: folder,
    }));

  // Collect .ts files in immediate subfolders, except 'modules' (which is ignored recursively)
  for (const entry of entries) {
    if (entry.isDirectory() && entry.name !== 'modules') {
      const subfolder = path.join(folder, entry.name);
      const subfiles = fs
        .readdirSync(subfolder, { withFileTypes: true })
        .filter((subentry) => subentry.isFile() && subentry.name.endsWith('.ts'))
        .map((subentry) => ({
          ...subentry,
          parentPath: subfolder,
        }));
      files = files.concat(subfiles);
    }
  }

  for (const file of files) {
    const command = (await import(path.join(file.parentPath, file.name))).default;
    client.storage.commands.set(command.builder.name, command);
    client.logger.info(`Loaded command ${command.builder.name}`);
  }
}

export interface Command {
  builder:
    | SlashCommandBuilder
    | SlashCommandSubcommandsOnlyBuilder
    | SlashCommandSubcommandBuilder
    | SlashCommandSubcommandGroupBuilder;
  config: {
    only?: 'owner' | 'manager' | 'developer' | 'artist';
    disabled?: boolean;
  };
  execute: (interaction: ChatInputCommandInteraction) => unknown | Promise<unknown>;
  autocomplete?: (interaction: AutocompleteInteraction) => unknown | Promise<unknown>;
}

export type CommandBuilderCallback = (builder: SlashCommandBuilder) => Command;
export type SubCommandBuilderCallback = (builder: SlashCommandSubcommandBuilder) => Command;
export type SubCommandGroupBuilderCallback = (builder: SlashCommandSubcommandGroupBuilder) => Command;

export function defineCommand(command: CommandBuilderCallback) {
  return command(new SlashCommandBuilder());
}

export function defineSubCommand(command: SubCommandBuilderCallback) {
  return command(new SlashCommandSubcommandBuilder());
}

export function defineSubCommandGroup(command: SubCommandGroupBuilderCallback) {
  return command(new SlashCommandSubcommandGroupBuilder());
}
