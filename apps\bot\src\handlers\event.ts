/* biome-ignore-all lint/nursery/noAwaitInLoop: Better this way */
import fs from 'node:fs';
import path from 'node:path';
import type { ClientEvents } from 'discord.js';
import type { MegamiClient } from '../structure/client';

export interface Event<K extends keyof ClientEvents = keyof ClientEvents> {
  name: K;
  once?: boolean;
  execute: (...args: ClientEvents[K]) => unknown | Promise<unknown>;
}

export async function defineEvents(client: MegamiClient) {
  const folder = path.join(process.cwd(), 'src', 'events');
  const files = fs.readdirSync(folder, {
    withFileTypes: true,
    recursive: true,
  });

  for (const file of files) {
    if (file.isFile() && file.name.endsWith('.ts')) {
      const event: Event = (await import(`${file.parentPath}/${file.name}`)).default;
      const handler = (...args: unknown[]) =>
        // @ts-expect-error - ignore this type error
        event.execute(...(args as unknown));
      if (event.once) {
        client.once(event.name, handler);
      } else {
        client.on(event.name, handler);
      }

      client.logger.info(`Loaded event ${event.name}`);
    }
  }
}

export function defineEvent<K extends keyof ClientEvents>(event: Event<K>): Event<K> {
  return event;
}
