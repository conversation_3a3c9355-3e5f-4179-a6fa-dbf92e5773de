import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Deja Vu',
  name: 'Deja Vu',
  element: 'Vortex',
  manaCost: 35,
  cooldown: 5,
  description: 'The target is afflicted with déjà vu, causing them to repeat their last action.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Stun',
        name: 'Deja Vu',
        duration: 1,
        sourceId: caster.id,
      },
      log: `${caster.name} afflicts ${target.name} with déjà vu.`,
    };
  },
});
