'use client';

import { motion } from 'framer-motion';
import { Twitter, Github, Disc } from 'lucide-react';
import Link from 'next/link';

export function Footer() {
  return (
    <motion.footer
      className="bg-gray-950/50 py-8 px-4 text-white"
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      transition={{ duration: 1 }}
      viewport={{ once: true }}
    >
      <div className="container mx-auto flex flex-col md:flex-row justify-between items-center">
        <p>&copy; {new Date().getFullYear()} Megami. All rights reserved.</p>
        <div className="flex gap-4 mt-4 md:mt-0">
          <Link href="#" target="_blank" rel="noopener noreferrer">
            <Twitter className="h-6 w-6 hover:text-purple-400 transition-colors" />
          </Link>
          <Link href="#" target="_blank" rel="noopener noreferrer">
            <Github className="h-6 w-6 hover:text-purple-400 transition-colors" />
          </Link>
          <Link href="#" target="_blank" rel="noopener noreferrer">
            <Disc className="h-6 w-6 hover:text-purple-400 transition-colors" />
          </Link>
        </div>
      </div>
    </motion.footer>
  );
}
