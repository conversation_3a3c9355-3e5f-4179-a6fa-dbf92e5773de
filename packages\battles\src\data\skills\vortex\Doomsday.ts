import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Doomsday',
  name: 'Doomsday',
  element: 'Vortex',
  manaCost: 100,
  cooldown: 12,
  description: 'The caster brings about doomsday, dealing immense damage to all enemies and applying a random debuff.',
  execute: (caster, target, formulas) => {
    // This would require a more complex targeting system and a system for handling random debuffs.
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const finalDamage = Math.round(damage * 4);
    return {
      damage: finalDamage,
      isCritical,
      log: `${caster.name} brings about doomsday, dealing ${finalDamage} damage to all enemies${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
