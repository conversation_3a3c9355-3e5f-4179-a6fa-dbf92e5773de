# Megami Profile System

A comprehensive, beautifully organized profile system that displays detailed user statistics, achievements, and progress in an elegant container-based UI.

## Features Overview

### 📊 **Comprehensive User Statistics**
- **Basic Information**: Level, experience, rifts, account age
- **Activity Streaks**: Daily, weekly, monthly consistency tracking
- **Character Collection**: Vessel statistics and constellation data
- **Inventory Overview**: Item counts, equipped gear, collection breakdown
- **Achievements System**: Dynamic badges based on user progress
- **Progress Tracking**: Visual progress bars and milestone indicators

### 🎨 **Beautiful Visual Design**
- **Modern Container UI**: Uses the latest Discord.js container system
- **Rich Emojis**: Context-appropriate emojis for all sections
- **Progress Bars**: Visual representation of experience progress
- **Achievement Badges**: Dynamic achievement system with tier-based rewards
- **Organized Layout**: Logical grouping with clear separators
- **Responsive Display**: Adapts to different data amounts gracefully

## Command Structure

### `/profile view [user]`
- **Description**: View detailed profile information
- **Parameters**:
  - `user` (optional): Target user to view (defaults to command user)
- **Permissions**: Available to all users
- **Response**: Ephemeral container-based UI

## Profile Sections

### 👤 **Header Section**
```
👤 **Username's Profile**
```
- Clean, prominent header with target user's name
- Distinguishes between own profile and others

### 📊 **Basic Information**
```
📊 Basic Information
Level: 42 ⭐
Experience: 168,000 XP
Progress: 0/4,000 XP (0%) ██████████
Next Level: 4,000 XP needed
Rifts: 💰 25,750
Account Age: 127 days
```

**Features:**
- **Level with Emoji**: Dynamic emoji based on level tier
- **Experience Tracking**: Current XP with formatted numbers
- **Progress Bar**: Visual 10-segment progress bar
- **Next Level Info**: Clear indication of XP needed
- **Wealth Display**: Formatted rift count
- **Account Age**: Days since registration

### 🔥 **Activity Streaks**
```
🔥 Activity Streaks
Daily: 15 days 🥉
Weekly: 3 weeks 🔥
Monthly: 1 month 🔥
```

**Features:**
- **Streak Tracking**: Current consecutive streaks
- **Achievement Medals**: Bronze/Silver/Gold based on thresholds
- **Proper Pluralization**: Handles singular/plural forms
- **Tier System**: Different thresholds for each activity type

### ⚔️ **Character Collection**
```
⚔️ Character Collection
Total Vessels: 23
Favorite Vessels: ⭐ 5
Total Level: 487
Average Level: 21.2
Highest Level: 45 
Total Constellations: ⭐ 67
```

**Features:**
- **Collection Stats**: Comprehensive vessel statistics
- **Level Analytics**: Total, average, and highest levels
- **Constellation Tracking**: Total constellation count
- **Achievement Indicators**: Special emojis for milestones

### 🎒 **Inventory Overview**
```
🎒 Inventory Overview
Total Items: 156
Inventory Slots: 89
Equipped Items: ⚡ 7
Favorite Items: ⭐ 12
```

**Features:**
- **Item Statistics**: Total items and slot usage
- **Equipment Status**: Currently equipped item count
- **Organization Info**: Favorite items tracking
- **Efficient Display**: Concise but comprehensive

### ⚡ **Currently Equipped** (if applicable)
```
⚡ Currently Equipped
  🟡 ⚔️ Legendary Sword
  🔵 🛡️ Rare Shield
  🟢 👑 Uncommon Helmet
  ... and 4 more items
```

**Features:**
- **Rarity Indicators**: Color-coded rarity emojis
- **Item Icons**: Type-specific emojis
- **Truncation**: Shows max 5 items with overflow indicator
- **Clean Layout**: Indented for visual hierarchy

### 📦 **Item Collection**
```
📦 Item Collection
  ⚔️ EQUIPMENT: 45
  🧪 CONSUMABLE: 32
  🔨 MATERIAL: 28
  💎 COLLECTIBLE: 15
  💰 CURRENCY: 12
  🎁 GIFT: 8
```

**Features:**
- **Type Breakdown**: Items organized by category
- **Sorted Display**: Ordered by quantity (highest first)
- **Limited Display**: Shows top 6 categories
- **Visual Hierarchy**: Indented with type emojis

### 🏆 **Achievements**
```
🏆 Achievements
  👑 Centurion - Reached level 100
  🔥 Dedicated - 30 day streak
  ⚔️ Master Collector - 50+ vessels
  💰 Wealthy - 100,000+ rifts
```

**Features:**
- **Dynamic System**: Achievements based on current stats
- **Tier-Based**: Multiple achievement levels
- **Descriptive**: Clear achievement descriptions
- **Motivational**: Encourages continued engagement

### 📅 **Footer Information**
```
Joined: 3 months ago
Last Updated: 2 minutes ago
```

**Features:**
- **Discord Timestamps**: Uses Discord's relative time formatting
- **Account History**: Join date and last activity
- **Real-time**: Updates automatically

## Achievement System

### 🏆 **Level Achievements**
- ✨ **Novice**: Levels 1-9
- 🌟 **Apprentice**: Levels 10-24
- ⭐ **Experienced**: Level 25+
- 🏆 **Veteran**: Level 50+
- 💎 **Elite**: Level 75+
- 👑 **Centurion**: Level 100+

### 🔥 **Streak Achievements**
- 🔥 **Starter**: 1+ day streak
- 🥉 **Bronze**: 7+ day / 4+ week / 3+ month streak
- 🥈 **Silver**: 30+ day / 12+ week / 6+ month streak
- 🥇 **Gold**: 100+ day / 52+ week / 12+ month streak

### ⚔️ **Collection Achievements**
- 🗡️ **Collector**: 20+ vessels
- ⚔️ **Master Collector**: 50+ vessels

### 🎒 **Inventory Achievements**
- 📦 **Gatherer**: 50+ items
- 🎒 **Hoarder**: 100+ items

### 💰 **Wealth Achievements**
- 💎 **Rich**: 10,000+ rifts
- 💰 **Wealthy**: 100,000+ rifts

## Technical Implementation

### 🔧 **Database Integration**
- **User Model**: Core user data (level, experience, rifts)
- **Vessels Model**: Character collection statistics
- **Holdings Model**: Inventory and equipment data
- **Efficient Queries**: Parallel data fetching for performance

### 🎨 **UI Components**
```typescript
const container = new MegamiContainer([
  new TextDisplayBuilder().setContent('👤 **Profile Header**'),
  new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
  new TextDisplayBuilder().setContent('Profile content...'),
]);
```

### 📊 **Progress Calculations**
```typescript
// Experience progress
const currentLevelExp = (user.level - 1) * 4000;
const nextLevelExp = user.level * 4000;
const progressExp = user.experience - currentLevelExp;
const progressPercentage = Math.round((progressExp / 4000) * 100);

// Progress bar visualization
const getProgressBar = (percentage: number): string => {
  const filled = Math.floor(percentage / 10);
  const empty = 10 - filled;
  return '█'.repeat(filled) + '░'.repeat(empty);
};
```

### 🏆 **Dynamic Achievements**
```typescript
function getAchievements(user: any, vesselStats: any, inventoryStats: any): string[] {
  const achievements: string[] = [];
  
  // Level-based achievements
  if (user.level >= 100) achievements.push('👑 **Centurion** - Reached level 100');
  
  // Streak-based achievements
  if (user.activity.daily.streak >= 30) achievements.push('🔥 **Dedicated** - 30 day streak');
  
  return achievements;
}
```

## Localization Support

### 🌍 **Translation Keys**
```json
{
  "commands": {
    "profile": {
      "name": "profile",
      "description": "View detailed profile information for yourself or other users.",
      "modules": {
        "view": {
          "name": "view",
          "description": "View a user's profile with detailed statistics and information.",
          "options": {
            "user": {
              "name": "user",
              "description": "The user whose profile you want to view (leave empty for your own)"
            }
          }
        }
      }
    }
  }
}
```

## Error Handling

### ❌ **User Not Found**
- Clear error message for unregistered users
- Different messages for own profile vs others
- Helpful guidance for registration

### 🔧 **Data Loading Errors**
- Graceful fallback for missing data
- Comprehensive error logging
- User-friendly error messages

## Performance Optimizations

### ⚡ **Parallel Data Fetching**
```typescript
const [inventoryStats, vesselStats, equippedItems] = await Promise.all([
  interaction.client.database.holdings.getUserStats(user.id),
  interaction.client.database.vessels.getUserStats(user.id),
  interaction.client.database.holdings.getUserEquippedItems(user.id),
]);
```

### 📊 **Efficient Calculations**
- Pre-calculated statistics in database models
- Minimal data processing in command execution
- Cached results where appropriate

## Future Enhancements

### 🎯 **Planned Features**
1. **Profile Customization**: Custom backgrounds, themes
2. **Social Features**: Friend lists, profile visits
3. **Detailed Analytics**: Historical progress tracking
4. **Badge System**: Custom badges and titles
5. **Profile Sharing**: Export profile as image
6. **Leaderboards**: Global and friend rankings

The profile system provides a comprehensive, beautiful, and engaging way for users to view their progress and achievements in the Megami ecosystem!
