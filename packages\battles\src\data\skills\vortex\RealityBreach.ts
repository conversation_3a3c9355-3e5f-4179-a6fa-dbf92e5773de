import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Reality Breach',
  name: 'Reality Breach',
  element: 'Vortex',
  manaCost: 45,
  cooldown: 6,
  description: 'Breaches reality, dealing damage to all enemies in an area and having a chance to apply a random debuff.',
  execute: (caster, target, formulas) => {
    // This would require a more complex targeting system and a system for handling random debuffs.
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const finalDamage = Math.round(damage * 1.6);
    return {
      damage: finalDamage,
      isCritical,
      log: `${caster.name} breaches reality, dealing ${finalDamage} damage to all enemies${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
