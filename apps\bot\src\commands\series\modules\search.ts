import { getObject, translate } from '@megami/locale';
import { defineSubCommand } from '../../../handlers/command';
import { EmbedBuilder } from 'discord.js';

export default defineSubCommand((builder) => ({
  builder: builder
    .setName(translate('commands.series.modules.search.name'))
    .setNameLocalizations(getObject('commands.series.modules.search.name'))
    .setDescription(translate('commands.series.modules.search.description'))
    .setDescriptionLocalizations(getObject('commands.series.modules.search.description'))
    .addStringOption((option) =>
      option
        .setName(translate('commands.series.modules.search.options.query.name'))
        .setNameLocalizations(getObject('commands.series.modules.search.options.query.name'))
        .setDescription(translate('commands.series.modules.search.options.query.description'))
        .setDescriptionLocalizations(getObject('commands.series.modules.search.options.query.description'))
        .setRequired(true)
        .setAutocomplete(true)
    ),
  config: {},
  async autocomplete(interaction) {
    const focused = interaction.options.getFocused(true);

    if (focused.name === translate('commands.series.modules.search.options.query.name')) {
      try {
        const series = interaction.client.search.series.search(focused.value);

        await interaction.respond(
          series.map((s) => ({
            name: s.name,
            value: s.id,
          }))
        );
      } catch (error) {
        interaction.client.logger.error('Error in series autocomplete:', error);
        await interaction.respond([]);
      }
    }
  },
  async execute(interaction) {
    const query = interaction.options.getString(translate('commands.series.modules.search.options.query.name'), true);
    const results = interaction.client.search.series.search(query);

    if (results.length === 0) {
      await interaction.reply({
        content: 'No series found.',
        ephemeral: true,
      });
      return;
    }

    const embeds = results.map((result) => {
        const embed = new EmbedBuilder()
            .setTitle(result.name)
            .setDescription(result.description || 'No description available.')
            .addFields({ name: 'ID', value: result.id, inline: true });
        if (result.image) {
            embed.setThumbnail(result.image);
        }
        return embed;
    });

    await interaction.reply({ embeds });
  },
}));
