/* biome-ignore-all lint/style/noNamespace: Ignore specially for this file */
/* biome-ignore-all lint/complexity/noUselessLoneBlockStatements: Ignore specially for this file */
/* biome-ignore-all lint/correctness/noUnusedVariables: Ignore specially for this file */

import type { CDN } from '@megami/cdn';
import type { MegamiDatabaseClient } from '@megami/database';
import type { MegamiSearch } from '@megami/search';
import type { ClusterClient } from 'discord-hybrid-sharding';
import type { Logger } from 'winston';
import type { MegamiScheduler } from '../structure/scheduler';
import type { MegamiStorage } from '../structure/storage';

declare module 'discord.js' {
  export interface Client {
    database: MegamiDatabaseClient;
    logger: Logger;
    storage: MegamiStorage;
    cluster: ClusterClient;
    scheduler: MegamiScheduler;
    cdn: CDN;
    search: MegamiSearch;
  }
}
