# `packages/database`

This package manages all database interactions for the "Megami" project, using `postgres-js` and Drizzle ORM.

## Installation

To install dependencies:

```bash
bun install
```

## Usage

To run:

```bash
bun run index.ts
```

---

## Architecture

The `database` package provides a robust and type-safe way to interact with the project's PostgreSQL database.

### Database Schema

The database schema is defined in the `src/schema` directory. Each file in this directory corresponds to a table in the database. All schemas are exported from `src/schema/index.ts`. The relations between the tables are defined in `src/schema/relations.ts`.

### `MegamiDatabaseClient`

The `MegamiDatabaseClient` is the main entry point for interacting with the database. It provides access to all the different database models.

**Properties:**

-   `instance`: The Drizzle database instance.
-   Models: The client has a property for each model (e.g., `users`, `characters`).

**Methods:**

-   **`constructor(options: MegamiDatabaseClientOptions)`**: Creates a new `MegamiDatabaseClient` instance.
-   **`seed()`**: Seeds the database with initial data.

**Example:**

```typescript
import { MegamiDatabaseClient } from './structure/client';

const db = new MegamiDatabaseClient({
  url: 'your-database-url',
});

// Access a model
const allUsers = await db.users.findMany();
```

### Models

Each database table has a corresponding model in the `src/structure/models` directory. These models provide a high-level API for querying the database. They extend the `BaseModel` class, which provides a `client` property for accessing the database instance.

**Example:**

```typescript
// Get a user by their ID
const user = await db.users.findById('user-id');

// Create a new character
const newCharacter = await db.characters.create({
  name: 'New Character',
  // ... other properties
});
```
