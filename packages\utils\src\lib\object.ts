/**
 * Returns an array of the keys of an object.
 * @param obj The object.
 * @returns An array of the keys of the object.
 */
export function keys<T extends object>(obj: T): (keyof T)[] {
  return Object.keys(obj) as (keyof T)[];
}

/**
 * Returns an array of the values of an object.
 * @param obj The object.
 * @returns An array of the values of the object.
 */
export function values<T extends object>(obj: T): T[keyof T][] {
  return Object.values(obj);
}

/**
 * Returns an array of the entries of an object.
 * @param obj The object.
 * @returns An array of the entries of the object.
 */
export function entries<T extends object>(obj: T): [keyof T, T[keyof T]][] {
  return Object.entries(obj) as [keyof T, T[keyof T]][];
}

/**
 * Creates an object from an array of entries.
 * @param entries The array of entries.
 * @returns An object created from the array of entries.
 */
export function fromEntries<K extends PropertyKey, V>(entries: [K, V][]): Record<K, V> {
  return Object.fromEntries(entries) as Record<K, V>;
}

/**
 * Maps the values of an object to a new object.
 * @param obj The object.
 * @param fn The function to apply to each value.
 * @returns A new object with the mapped values.
 */
export function map<T extends object, R>(
  obj: T,
  fn: (value: T[keyof T], key: keyof T) => R
): Record<keyof T, R> {
  const result = {} as Record<keyof T, R>;
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      result[key] = fn(obj[key], key);
    }
  }
  return result;
}

/**
 * Filters the values of an object to a new object.
 * @param obj The object.
 * @param fn The function to apply to each value.
 * @returns A new object with the filtered values.
 */
export function filter<T extends object>(
  obj: T,
  fn: (value: T[keyof T], key: keyof T) => boolean
): Partial<T> {
  const result: Partial<T> = {};
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      if (fn(obj[key], key)) {
        result[key] = obj[key];
      }
    }
  }
  return result;
}

/**
 * Reduces the values of an object to a single value.
 * @param obj The object.
 * @param fn The function to apply to each value.
 * @param initialValue The initial value.
 * @returns The reduced value.
 */
export function reduce<T extends object, R>(
  obj: T,
  fn: (acc: R, value: T[keyof T], key: keyof T) => R,
  initialValue: R
): R {
  let acc = initialValue;
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      acc = fn(acc, obj[key], key);
    }
  }
  return acc;
}
