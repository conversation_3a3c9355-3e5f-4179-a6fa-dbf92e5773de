import type { series, InferSelectModel } from '@megami/database';
import MiniSearch, { type Query, type SearchOptions } from 'minisearch';

type Series = InferSelectModel<typeof series>;

export class MegamiSeriesIndex {
  public instance: MiniSearch<Series>;

  constructor() {
    this.instance = new MiniSearch<Series>({
      fields: ['name'],
      storeFields: ['name'],
    });
  }

  public async add(...series: Series[]) {
    return await this.instance.addAllAsync(series);
  }

  public remove(...series: Series[]) {
    return this.instance.removeAll(series);
  }

  public search(query: Query, options: SearchOptions = {}) {
    return this.instance.search(query, options);
  }
}
