import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Cleansing Flames',
  name: 'Cleansing Flames',
  element: 'Sirius',
  manaCost: 25,
  cooldown: 4,
  description: 'The caster is engulfed in cleansing flames, removing all negative status effects and healing for a small amount.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Dispel',
        name: 'Cleansing Flames',
        duration: 0,
        sourceId: caster.id,
      },
      log: `${caster.name} is engulfed in cleansing flames.`,
    };
  },
});
