import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: '<PERSON><PERSON>',
  name: '<PERSON><PERSON>',
  element: '<PERSON>ane',
  manaCost: 8,
  cooldown: 2,
  description: 'Drain mana from the target and restore it to yourself.',
  execute: (caster, target) => {
    const manaDrained = Math.min(15, target.stats.mana);
    target.stats.mana -= manaDrained;
    caster.stats.mana = Math.min(caster.stats.maxMana, caster.stats.mana + manaDrained);
    return {
      log: `${caster.name} leeches ${manaDrained} mana from ${target.name}!`,
    };
  },
});
