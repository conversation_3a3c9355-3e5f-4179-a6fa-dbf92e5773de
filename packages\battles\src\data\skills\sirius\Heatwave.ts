import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Heatwave',
  name: 'Heatwave',
  element: 'Sirius',
  manaCost: 30,
  cooldown: 4,
  description: 'A wave of intense heat that damages and reduces the attack speed of all enemies.',
  execute: (caster, target, formulas) => {
    // This would require a more complex targeting and status effect system.
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const finalDamage = Math.round(damage * 1.1);
    return {
      damage: finalDamage,
      isCritical,
      log: `${caster.name} unleashes a Heatwave, damaging and slowing all enemies for ${finalDamage} damage${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
