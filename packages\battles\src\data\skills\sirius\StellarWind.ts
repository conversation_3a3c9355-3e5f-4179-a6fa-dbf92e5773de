import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Stellar Wind',
  name: 'Stellar Wind',
  element: 'Sirius',
  manaCost: 18,
  cooldown: 2,
  description: 'Unleash stellar winds that deal damage and boost your speed while reducing enemy accuracy.',
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);

    return {
      damage: Math.round(damage * 1.0),
      isCritical,
      appliedEffect: {
        id: 'stellar_wind_debuff',
        name: 'Stellar Wind',
        duration: 2,
        potency: -8, // -8 luck (accuracy)
        sourceId: caster.id,
      },
      log: `${caster.name} unleashes Stellar Wind on ${target.name} for ${Math.round(damage * 1.0)} damage${isCritical ? ' (CRIT!)' : ''} and disrupts their accuracy!`,
    };
  },
});
