import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Chronomancy',
  name: 'Chronomancy',
  element: 'Arcane',
  manaCost: 30,
  cooldown: 5,
  description: "Reduces the cooldown of all of the caster's other skills.",
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Attack Speed Up',
        name: 'Chronomancy',
        duration: 3,
        sourceId: caster.id,
      },
      log: `${caster.name} uses Chronomancy to reduce their cooldowns.`,
    };
  },
});
