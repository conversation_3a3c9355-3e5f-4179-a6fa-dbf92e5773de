import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Void Prison',
  name: 'Void Prison',
  element: 'Vortex',
  manaCost: 50,
  cooldown: 7,
  description: 'Traps the target in a void prison, making them untargetable but also unable to act.',
  execute: (caster, target, formulas) => {
    // This would require a status effect system to handle the prison.
    return {
      log: `${caster.name} traps ${target.name} in a Void Prison.`,
    };
  },
});
