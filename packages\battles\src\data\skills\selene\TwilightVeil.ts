import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Twilight Veil',
  name: 'Twilight Veil',
  element: 'Selene',
  manaCost: 25,
  cooldown: 4,
  description: 'Shroud the battlefield in twilight, dealing damage and swapping a random stat with the target.',
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);

    // Randomly swap one stat (simplified - just apply opposing effects)
    const statSwapPower = 10;
    const swapEffect = formulas.randomElement(['attack', 'defense']);

    return {
      damage: Math.round(damage * 1.25),
      isCritical,
      appliedEffect: {
        id: `twilight_${swapEffect}_swap`,
        name: `Twilight ${swapEffect.charAt(0).toUpperCase() + swapEffect.slice(1)} Swap`,
        duration: 3,
        potency: swapEffect === 'attack' ? -statSwapPower : statSwapPower, // Swap effect
        sourceId: caster.id,
      },
      log: `${caster.name} shrouds the battlefield in Twilight Veil, dealing ${Math.round(damage * 1.25)} damage${isCritical ? ' (CRIT!)' : ''} to ${target.name} and manipulating their ${swapEffect}!`,
    };
  },
});
