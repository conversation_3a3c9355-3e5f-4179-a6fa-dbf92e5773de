/* biome-ignore-all lint/suspicious/useAwait: We need this to be a promise */
import os from 'node:os';
import * as prompt from '@clack/prompts';
import ffmpeg from 'fluent-ffmpeg';

/**
 * Converts a .mov file (with transparency) to animated .webp format.
 *
 * @param input Path to the input .mov video
 * @param output Path to output .webp file
 * @param options Optional settings for FPS and scaling
 */
export async function webp(
  input: string,
  output: string,
  options?: {
    fps?: number;
    scale?: number; // e.g. 480 for 480px width
    loop?: boolean;
  }
): Promise<void> {
  const fps = options?.fps ?? 30;
  const scale = options?.scale ?? 512;
  const loop = options?.loop !== false; // default: true

  return new Promise((resolve, reject) => {
    const filters = [`fps=${fps}`, `scale=${scale}:-1:flags=lanczos`];

    ffmpeg(input)
      .outputOptions([
        '-y', // overwrite
        '-loop',
        loop ? '0' : '1', // 0 = infinite loop
        '-an', // no audio
      ])
      .videoCodec('libwebp')
      .outputFormat('webp')
      .videoFilters(filters)
      .on('start', (cmd) => {
        prompt.log.info(`Converting to WebP: ${cmd}`);
      })
      .on('end', () => {
        prompt.log.info(`WebP created successfully: ${output}`);
        resolve();
      })
      .on('error', (err, _stdout, stderr) => {
        prompt.log.error(`WebP conversion error: ${err.message}`);
        prompt.log.error(`ffmpeg stderr: ${stderr}`);
        reject(err);
      })
      .save(output);
  });
}

const fileExtensionRegex = /(\.\w+)$/;

export async function looped(input: string, output: string): Promise<void> {
  const reversed = input.replace(fileExtensionRegex, '_reversed$1');
  const _looped = input.replace(fileExtensionRegex, '_looped$1');

  // 1. Reverse the input
  await new Promise<void>((resolve, reject) => {
    ffmpeg(input).outputOptions(['-y']).videoFilters('reverse').on('end', resolve).on('error', reject).save(reversed);
  });

  // 2. Concatenate original + reversed
  await new Promise<void>((resolve, reject) => {
    ffmpeg()
      .input(input)
      .input(reversed)
      .outputOptions(['-y'])
      .on('end', resolve)
      .on('error', reject)
      .mergeToFile(output, os.tmpdir());
  });
}
