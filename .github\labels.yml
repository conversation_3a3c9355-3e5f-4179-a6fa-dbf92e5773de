- name: Breaking Change
  color: 993399
  description: A change that alters the API or breaks backward compatibility for users.

- name: Bug Fix
  color: CC33CC
  description: Inconsistencies or issues that will cause a problem for users or implementers.

- name: New Feature
  color: 663399
  description: Introduces new features or options.

- name: Enhancement
  color: 9966CC
  description: An improvement to existing code without introducing new features.

- name: Refactor
  color: 9966CC
  description: 'Updates the code with simpler, easier-to-understand, or more efficient syntax or methods, without introducing new features.'

- name: Performance
  color: 9966CC
  description: Improves the performance of the project without introducing new features.

- name: Documentation
  color: 6699CC
  description: "Pertains solely to the project's documentation."

- name: Maintenance
  color: 339999
  description: Generic maintenance tasks.

- name: CI
  color: 9966CC
  description: Work that improves the continuous integration setup.

- name: Dependencies
  color: 9966CC
  description: Indicates a change in project dependencies.

- name: In Progress
  color: CC99FF
  description: The issue is currently being worked on by a developer.

- name: Stale
  color: E6E6FA
  description: No activity for a significant period.

- name: No Stale
  color: E6E6FA
  description: This issue is exempt from the stale bot.

- name: Incomplete
  color: E6E6FA
  description: Missing information required to proceed.

- name: Invalid
  color: E6E6FA
  description: "This is off-topic, spam, or otherwise doesn't apply to this project."

- name: Security
  color: 993399
  description: Addresses a vulnerability or security risk in this project.

- name: Beginner Friendly
  color: 663399
  description: A good first issue for new contributors.

- name: Help Wanted
  color: 663399
  description: We need extra hands or expertise to resolve this!

- name: Priority Critical
  color: 993399
  description: Must be addressed as soon as possible.

- name: Priority High
  color: CC33CC
  description: 'After critical issues are fixed, these should be dealt with before any further issues.'

- name: Priority Medium
  color: 663399
  description: This issue may be useful and needs some attention.

- name: Priority Low
  color: CC99FF
  description: 'A nice addition, perhaps for the future.'

- name: Major
  color: CC33CC
  description: This PR causes a major bump in the version number.

- name: Minor
  color: 663399
  description: This PR causes a minor bump in the version number.
