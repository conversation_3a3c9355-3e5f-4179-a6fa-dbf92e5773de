import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Time Warp',
  name: 'Time Warp',
  element: 'Vortex',
  manaCost: 20,
  cooldown: 4,
  description: 'Manipulate time to boost your speed and reduce cooldowns.',
  execute: (caster) => {
    // Reduce all cooldowns by 1
    for (const skillId in caster.cooldowns) {
      if (caster.cooldowns[skillId] > 0) {
        caster.cooldowns[skillId] = Math.max(0, caster.cooldowns[skillId] - 1);
      }
    }

    return {
      appliedEffect: {
        id: 'time_warp',
        name: 'Time Warp',
        duration: 3,
        potency: 8, // +8 speed
        sourceId: caster.id,
      },
      log: `${caster.name} warps time, boosting their speed and reducing cooldowns!`,
    };
  },
});
