import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Arcane Infusion',
  name: 'Arcane Infusion',
  element: 'Arcane',
  manaCost: 25,
  cooldown: 4,
  description: 'Infuses an ally with arcane energy, increasing their intelligence and healing them.',
  execute: (caster, target, formulas) => {
    // This would require a targeting system that can target allies. For now, it targets the caster.
    return {
      appliedEffect: {
        id: 'Intelligence Up',
        name: 'Intelligence Up',
        duration: 3,
        sourceId: caster.id,
      },
      log: `${caster.name} infuses themselves with arcane energy, increasing their intelligence.`,
    };
  },
});
