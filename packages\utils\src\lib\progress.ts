import { emotes } from '@megami/config/lib/emotes';

export function defineProgress(current: number, total: number): string {
  const clampedCurrent = Math.max(0, Math.min(current, total));

  const progress = total > 0 ? clampedCurrent / total : 0;

  const totalSegments = 5;
  const filledSegments = Math.floor(progress * totalSegments);

  let result = '';

  result += filledSegments > 0 ? emotes.misc.bars.filled.start : emotes.misc.bars.empty.start;

  for (let i = 1; i <= 3; i++) {
    result += i < filledSegments ? emotes.misc.bars.filled.middle : emotes.misc.bars.empty.middle;
  }

  result += filledSegments >= 5 ? emotes.misc.bars.filled.end : emotes.misc.bars.empty.end;

  return result;
}
