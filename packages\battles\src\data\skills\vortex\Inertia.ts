import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Inertia',
  name: 'Inertia',
  element: 'Vortex',
  manaCost: 25,
  cooldown: 4,
  description: 'The target is afflicted with inertia, causing them to be slowed and take damage whenever they move.',
  execute: (caster, target, formulas) => {
    // This would require a status effect system to handle the slow and damage on movement.
    return {
      log: `${caster.name} afflicts ${target.name} with inertia.`,
    };
  },
});
