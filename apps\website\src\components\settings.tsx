/* eslint-disable react/no-array-index-key */

import { themes } from '@megami/config/lib/themes';
import {
  MorphingDialogContent,
  MorphingDialogDescription,
  MorphingDialogTitle,
} from '@megami/ui/components/registry/morphing-dialog';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@megami/ui/components/registry/new-york-v4/ui/breadcrumb';
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarProvider,
} from '@megami/ui/components/registry/new-york-v4/ui/sidebar';
import { SunIcon } from '@megami/ui/icons/radix';
import { useTheme } from 'next-themes';

const data = {
  nav: [{ name: 'Theme', icon: SunIcon }],
};

export interface SettingsProps {
  isOpen: boolean;
}

export function Settings({ isOpen }: SettingsProps) {
  const { setTheme } = useTheme();

  if (!isOpen) {
    return null;
  }

  return (
    <div className="flex min-h-screen w-full items-center justify-center">
      <MorphingDialogContent className="w-[350px] overflow-hidden p-0 md:max-h-[500px] md:w-[700px] lg:w-[800px]">
        <MorphingDialogTitle className="sr-only">Settings</MorphingDialogTitle>
        <MorphingDialogDescription className="sr-only">Customize your settings here.</MorphingDialogDescription>
        <SidebarProvider className="items-start">
          <Sidebar className="hidden md:flex" collapsible="none">
            <SidebarContent>
              <SidebarGroup>
                <SidebarGroupContent>
                  <SidebarMenu>
                    {data.nav.map((item) => (
                      <SidebarMenuItem key={item.name}>
                        <SidebarMenuButton asChild isActive={item.name === 'Messages & media'}>
                          <button type="button">
                            <item.icon />
                            <span>{item.name}</span>
                          </button>
                        </SidebarMenuButton>
                      </SidebarMenuItem>
                    ))}
                  </SidebarMenu>
                </SidebarGroupContent>
              </SidebarGroup>
            </SidebarContent>
          </Sidebar>
          <main className="flex h-[480px] flex-1 flex-col overflow-hidden">
            <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12">
              <div className="flex items-center gap-2 px-4">
                <Breadcrumb>
                  <BreadcrumbList>
                    <BreadcrumbItem className="hidden md:block">
                      <BreadcrumbLink href="/">Settings</BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator className="hidden md:block" />
                    <BreadcrumbItem>
                      <BreadcrumbPage>Themes</BreadcrumbPage>
                    </BreadcrumbItem>
                  </BreadcrumbList>
                </Breadcrumb>
              </div>
            </header>
            <div className="flex flex-1 flex-col gap-4 overflow-y-auto p-4 pt-0">
              {themes.map((theme) => (
                <button
                  className="aspect-video max-w-3xl rounded-xl bg-muted/50 capitalize"
                  key={theme}
                  onClick={() => setTheme(theme)}
                  type="button"
                >
                  {theme}
                </button>
              ))}
            </div>
          </main>
        </SidebarProvider>
      </MorphingDialogContent>
    </div>
  );
}
