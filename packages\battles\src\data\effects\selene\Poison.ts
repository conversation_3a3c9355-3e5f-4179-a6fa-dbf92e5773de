import { defineEffect } from '../defineEffect';

export default defineEffect({
  id: 'Poison',
  name: '<PERSON>ison',
  element: 'Selene',
  description: 'The target is poisoned, taking damage over time.',
  onTurnStart: (target, effect, operators) => {
    const poisonDamage = 5;
    return {
      damage: poisonDamage,
      log: `${target.name} is poisoned for ${poisonDamage} damage.`,
    };
  },
});
