/**
 * Example: Creating a Rift Booster item that doubles rift gains
 * This demonstrates how to create items with effects in the new system
 */

import type { MegamiDatabaseClient } from '../src/structure/client';

export async function createRiftBoosterItem(client: MegamiDatabaseClient) {
  // Create a consumable item that doubles rift gains for 1 hour
  const riftBooster = await client.items.create({
    name: 'Rift Amplifier Potion',
    description: 'A mystical potion that enhances your connection to the rifts, doubling rift regeneration for 1 hour.',
    type: 'CONSUMABLE',
    rarity: 'RARE',
    icon: '🧪',
    stackable: true,
    maxStack: 10,
    tradeable: true,
    sellable: true,
    metadata: {
      effects: [
        {
          type: 'RIFT_MULTIPLIER',
          name: 'Rift Amplification',
          description: 'Doubles rift regeneration rate',
          data: {
            multiplier: 2.0,
            stackable: false, // Only one can be active at a time
          },
          duration: 3600, // 1 hour in seconds
          onUse: true, // Applied when item is consumed
        },
      ],
      restrictions: {
        cooldown: 300, // 5 minute cooldown between uses
      },
      economy: {
        sellPrice: 100,
        buyPrice: 250,
      },
      flavor: {
        lore: 'Crafted by the ancient Rift Mages, this potion temporarily amplifies one\'s natural rift absorption abilities.',
        tags: ['consumable', 'rift', 'boost', 'temporary'],
        color: '#9D4EDD', // Purple color for rift-related items
      },
    },
  });

  console.log('Created Rift Booster item:', riftBooster.id);
  return riftBooster;
}

export async function createRiftBonusRing(client: MegamiDatabaseClient) {
  // Create an equipment item that provides a flat rift bonus
  const riftRing = await client.items.create({
    name: 'Ring of Rift Mastery',
    description: 'An ancient ring that grants additional rift energy with each regeneration cycle.',
    type: 'EQUIPMENT',
    rarity: 'EPIC',
    icon: '💍',
    stackable: false,
    maxStack: 1,
    tradeable: true,
    sellable: true,
    metadata: {
      effects: [
        {
          type: 'RIFT_BONUS',
          name: 'Rift Mastery',
          description: 'Grants +5 bonus rifts per regeneration cycle',
          data: {
            bonus: 5,
            stackable: true, // Multiple rings can stack
            maxStacks: 3, // But only up to 3 rings
          },
          permanent: true, // Effect lasts as long as item is equipped
          onEquip: true, // Applied when item is equipped
        },
      ],
      stats: {
        luck: 10, // Also provides some luck
      },
      restrictions: {
        levelRequired: 25,
      },
      economy: {
        sellPrice: 1000,
        buyPrice: 2500,
      },
      flavor: {
        lore: 'Forged in the heart of a collapsed rift, this ring pulses with residual rift energy.',
        tags: ['equipment', 'ring', 'rift', 'permanent'],
        color: '#7209B7',
      },
    },
  });

  console.log('Created Rift Bonus Ring:', riftRing.id);
  return riftRing;
}

export async function demonstrateEffectUsage(client: MegamiDatabaseClient, userId: string) {
  // Create the items
  const booster = await createRiftBoosterItem(client);
  const ring = await createRiftBonusRing(client);

  console.log('\n=== Demonstrating Effect System ===');

  // Check user's current rift effects (should be none)
  const initialEffects = await client.effects.calculateRiftEffects(userId);
  console.log('Initial rift effects:', initialEffects);

  // Use the rift booster potion
  await client.items.applyItemEffects(userId, booster.id, 'USE');
  console.log('Applied rift booster potion');

  // Equip the rift ring
  await client.items.applyItemEffects(userId, ring.id, 'EQUIP');
  console.log('Equipped rift ring');

  // Check effects after applying items
  const finalEffects = await client.effects.calculateRiftEffects(userId);
  console.log('Final rift effects:', finalEffects);
  console.log(`Rift regeneration will now be: ${Math.floor((10 * finalEffects.multiplier) + finalEffects.bonus)} rifts per cycle`);

  // Show all active effects
  const userEffects = await client.effects.getUserEffects(userId);
  console.log('\nActive effects:');
  userEffects.forEach(effect => {
    console.log(`- ${effect.name}: ${effect.description} (expires: ${effect.expiresAt || 'never'})`);
  });

  return { booster, ring, effects: finalEffects };
}

// Example usage:
// const client = new MegamiDatabaseClient({ url: 'your-db-url' });
// await demonstrateEffectUsage(client, 'user-id-here');
