import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Moonlight Blade',
  name: 'Moonlight Blade',
  element: 'Selene',
  manaCost: 20,
  cooldown: 3,
  description: 'A blade of moonlight that damages and bleeds the target.',
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const finalDamage = Math.round(damage * 1.2);
    // This would also require a status effect system to handle the bleed.
    return {
      damage: finalDamage,
      isCritical,
      log: `${caster.name} strikes ${target.name} with Moonlight Blade for ${finalDamage} damage${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
