import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Flux',
  name: 'Flux',
  element: 'Vortex',
  manaCost: 30,
  cooldown: 5,
  description: 'The target is afflicted with flux, causing their health and mana to fluctuate randomly.',
  execute: (caster, target, formulas) => {
    // This would require a status effect system to handle the random fluctuations.
    return {
      log: `${caster.name} afflicts ${target.name} with flux.`,
    };
  },
});
