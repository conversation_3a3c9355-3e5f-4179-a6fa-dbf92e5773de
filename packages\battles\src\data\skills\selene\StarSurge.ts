import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Star Surge',
  name: 'Star Surge',
  element: 'Selene',
  manaCost: 45,
  cooldown: 6,
  description: 'The caster summons a surge of starlight, damaging and stunning all enemies in a line.',
  execute: (caster, target, formulas) => {
    // This would require a more complex targeting and status effect system.
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const finalDamage = Math.round(damage * 1.7);
    return {
      damage: finalDamage,
      isCritical,
      log: `${caster.name} summons a Star Surge, damaging and stunning all enemies in a line for ${finalDamage} damage${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
