import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Arcane Missiles',
  name: 'Arcane Missiles',
  element: 'Arcane',
  manaCost: 25,
  cooldown: 2,
  description: 'Launches a volley of arcane missiles at the target.',
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const finalDamage = Math.round(damage * 1.2);
    return {
      damage: finalDamage,
      isCritical,
      log: `${caster.name} launches Arcane Missiles at ${target.name}, dealing ${finalDamage} damage${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
