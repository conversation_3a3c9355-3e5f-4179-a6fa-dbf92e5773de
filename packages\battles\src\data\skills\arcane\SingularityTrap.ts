import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Singularity Trap',
  name: 'Singularity Trap',
  element: 'Arcane',
  manaCost: 40,
  cooldown: 6,
  description: 'Creates a trap that, when triggered, creates a singularity that pulls all enemies together and damages them.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Stun',
        name: 'Singularity Trap',
        duration: 1,
        sourceId: caster.id,
      },
      log: `${caster.name} sets a Singularity Trap.`,
    };
  },
});
