import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Singularity',
  name: 'Singularity',
  element: 'Arcane',
  manaCost: 50,
  cooldown: 6,
  description: 'Creates a singularity that pulls all enemies together and damages them.',
  execute: (caster, target, formulas) => {
    // This would require a more complex targeting system.
    // For now, we'll just damage the primary target.
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const finalDamage = Math.round(damage * 2);
    return {
      damage: finalDamage,
      isCritical,
      log: `${caster.name} creates a Singularity, damaging ${target.name} for ${finalDamage} damage${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
