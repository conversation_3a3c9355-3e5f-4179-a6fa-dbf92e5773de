import { type Database, defineDatabase } from '../database';
import { Characters } from './models/characters';
import { Currencies } from './models/currencies';
import { Drops } from './models/drops';
import { Effects } from './models/effects';
import { Frames } from './models/frames';
import { Holdings } from './models/holdings';
import { Items } from './models/items';
import { Notifications } from './models/notifications';
import { Series } from './models/series';
import { Skins } from './models/skins';
import { Users } from './models/users';
import { Vessels } from './models/vessels';

export interface MegamiDatabaseClientOptions {
  url: string;
}

export class MegamiDatabaseClient {
  public options: MegamiDatabaseClientOptions;
  public instance: Database;

  public characters: Characters;
  public currencies: Currencies;
  public drops: Drops;
  public effects: Effects;
  public frames: Frames;
  public holdings: Holdings;
  public items: Items;
  public notifications: Notifications;
  public series: Series;
  public skins: Skins;
  public users: Users;
  public vessels: Vessels;

  constructor(options: MegamiDatabaseClientOptions) {
    this.options = options;
    this.instance = defineDatabase(this.options.url);

    this.characters = new Characters(this);
    this.currencies = new Currencies(this);
    this.drops = new Drops(this);
    this.effects = new Effects(this);
    this.frames = new Frames(this);
    this.holdings = new Holdings(this);
    this.items = new Items(this);
    this.notifications = new Notifications(this);
    this.series = new Series(this);
    this.skins = new Skins(this);
    this.users = new Users(this);
    this.vessels = new Vessels(this);
  }

  /**
   * Seed all models with initial data
   * Runs all model seed methods concurrently for better performance
   */
  public async seed(): Promise<void> {
    const seedPromises = [
      this.currencies.seed(),
      this.drops.seed(),
      this.effects.seed(),
      this.items.seed(),
      this.notifications.seed(),
      // Add more model seeds here as needed
    ];

    await Promise.all(seedPromises);
  }
}
