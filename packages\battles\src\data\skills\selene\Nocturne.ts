import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Nocturne',
  name: 'Nocturne',
  element: '<PERSON><PERSON>',
  manaCost: 30,
  cooldown: 5,
  description: 'Plays a haunting nocturne, putting all enemies to sleep.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Sleep',
        name: 'Nocturne',
        duration: 2,
        sourceId: caster.id,
      },
      log: `${caster.name} plays a haunting nocturne, putting all enemies to sleep.`,
    };
  },
});
