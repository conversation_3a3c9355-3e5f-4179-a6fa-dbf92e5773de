import { defineTask } from '../../structure/scheduler';

export default defineTask({
  id: 'search:index:skins',
  name: 'Update Skins Search Index',
  expression: '0 5 2 * * *', // Daily at 2:05 AM UTC
  enabled: true,
  execute: async (client) => {
    try {
      client.logger.info('Starting skins search index update...');

      const skins = await client.database.skins.getAll();
      const existing = Object.keys(client.search.skins.instance.toJSON().documentIds);

      const indexed = new Set(existing);
      const missing = skins.filter((skin) => !indexed.has(skin.id));

      if (missing.length > 0) {
        await client.search.skins.add(...missing);
        client.logger.info(`Added ${missing.length} skins to search index`);
      } else {
        client.logger.info('No new skins to add to search index');
      }

      client.logger.info('Skins search index update completed');
    } catch (error) {
      client.logger.error('Failed to update skins search index:', error);
      throw error;
    }
  },
});
