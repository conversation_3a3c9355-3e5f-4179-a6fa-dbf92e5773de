import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Stellar Beam',
  name: 'Stellar Beam',
  element: 'Sirius',
  manaCost: 10,
  cooldown: 0,
  description: 'A basic beam of stellar light. Deals moderate damage.',
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    return {
      damage,
      isCritical,
      log: `${caster.name} fires a Stellar Beam at ${target.name} for ${damage} damage${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
