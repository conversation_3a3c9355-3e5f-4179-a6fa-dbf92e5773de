import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Cosmic Horror',
  name: 'Cosmic Horror',
  element: 'Vortex',
  manaCost: 40,
  cooldown: 6,
  description: 'The target is afflicted with a cosmic horror, causing them to be feared and take damage over time.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Cosmic Horror Effect',
        name: 'Cosmic Horror',
        duration: 2,
        sourceId: caster.id,
      },
      log: `${caster.name} afflicts ${target.name} with a Cosmic Horror.`,
    };
  },
});
