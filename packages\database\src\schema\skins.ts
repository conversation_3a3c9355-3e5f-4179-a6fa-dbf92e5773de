import type { SkinRarity } from '@megami/config/lib/rewards';
import { boolean, index, pgTable, text, timestamp, uniqueIndex, uuid } from 'drizzle-orm/pg-core';
import { characters } from './characters';
import { users } from './users';

export const skins = pgTable(
  'skins',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    name: text('name').notNull(),
    rarity: text('rarity').$type<SkinRarity>().notNull(),
    approved: boolean('approved').default(false).notNull(),
    createdAt: timestamp('created_at').defaultNow().notNull(),
    updatedAt: timestamp('updated_at').defaultNow().notNull(),
    url: text('url').notNull(),

    // References:
    authorId: text('author_id')
      .references(() => users.id)
      .notNull(),
    characterId: uuid('character_id')
      .notNull()
      .references(() => characters.id),
  },
  (table) => [
    // Unique indexes
    uniqueIndex('skin_name').on(table.name),
    uniqueIndex('skin_id').on(table.id),

    // Query optimization indexes
    index('skins_character_idx').on(table.characterId),
    index('skins_author_idx').on(table.authorId),
    index('skins_rarity_idx').on(table.rarity),
    index('skins_approved_idx').on(table.approved),
    index('skins_character_approved_idx').on(table.characterId, table.approved),
  ]
);
