import { createSelectSchema } from 'drizzle-zod';
import type { MegamiDatabaseClient } from '../client';
import { BaseModel } from '../model';

export class Guilds extends BaseModel {
  public schema = createSelectSchema(this.schemas.guilds);

  constructor(client: MegamiDatabaseClient, data: unknown) {
    super(client);

    const validated = this.schema.safeParse(data);
    if (validated.error) {
      throw new Error(`Guilds Schema Parse Error: ${validated.error.issues.length}`);
    }
  }
}
