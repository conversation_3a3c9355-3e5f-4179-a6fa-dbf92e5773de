import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Fireball',
  name: 'Fireball',
  element: 'Sirius',
  manaCost: 10,
  cooldown: 1,
  description: 'A basic fireball that deals fire damage.',
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const finalDamage = Math.round(damage * 1);
    return {
      damage: finalDamage,
      isCritical,
      log: `${caster.name} throws a Fireball at ${target.name} for ${finalDamage} damage${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
