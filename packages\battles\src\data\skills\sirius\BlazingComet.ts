import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Blazing Comet',
  name: 'Blazing Comet',
  element: 'Sirius',
  manaCost: 20,
  cooldown: 3,
  description: 'A comet of fire that damages and burns the target.',
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const finalDamage = Math.round(damage * 1.2);
    // This would also require a status effect system to handle the burn.
    return {
      damage: finalDamage,
      isCritical,
      log: `${caster.name} summons a Blazing Comet to strike ${target.name} for ${finalDamage} damage${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
