import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Spellbind',
  name: 'Spellbind',
  element: 'Arcane',
  manaCost: 15,
  cooldown: 2,
  description: 'Binds the target in arcane energy, preventing them from using skills.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Silence',
        name: 'Spellbind',
        duration: 2,
        sourceId: caster.id,
      },
      log: `${caster.name} binds ${target.name} in arcane energy, silencing them.`,
    };
  },
});
