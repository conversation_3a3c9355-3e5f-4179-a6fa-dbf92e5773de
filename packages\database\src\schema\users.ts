import { integer, jsonb, pgTable, text, timestamp } from 'drizzle-orm/pg-core';

export interface Votes {
  [key: string]: {
    streak: number;
    recent: number;
  };
}

export interface Activity {
  streak: number;
  recent: number;
}

export const users = pgTable('users', {
  id: text('id').primaryKey(),
  level: integer('level').default(1).notNull(),
  experience: integer('experience').default(0).notNull(),
  activity: jsonb('activity').$type<Activity>().default({ streak: 0, recent: 0 }).notNull(),
  votes: jsonb('votes').$type<Votes>().default({}).notNull(),
  pity: integer('pity').default(0).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});
