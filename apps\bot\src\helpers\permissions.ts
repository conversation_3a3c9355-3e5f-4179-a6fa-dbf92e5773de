import { is } from '@megami/config/lib/team';
import { toProperCase } from '@megami/utils/lib/case';
import {
  type ChatInputCommandInteraction,
  MessageFlags,
  SeparatorBuilder,
  SeparatorSpacingSize,
  TextDisplayBuilder,
} from 'discord.js';
import type { Command } from '../handlers/command';
import { MegamiContainer } from './containers';

export async function check(interaction: ChatInputCommandInteraction, command: Command) {
  if (command.config.only) {
    const approval = is(interaction.user.id, toProperCase(command.config.only));
    if (!approval) {
      await interaction.reply({
        components: [
          new MegamiContainer([
            new TextDisplayBuilder().setContent('**Unauthorized**'),
            new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
            new TextDisplayBuilder().setContent('You do not have permission to use this command.'),
          ]),
        ],
        flags: MessageFlags.IsComponentsV2,
        ephemeral: true,
      });

      return false;
    }
  }

  return true;
}
