import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Distortion',
  name: 'Distortion',
  element: 'Vortex',
  manaCost: 30,
  cooldown: 5,
  description: 'The caster distorts space, causing all attacks against them to have a chance to miss.',
  execute: (caster, target, formulas) => {
    // This would require a status effect system to handle the miss chance.
    return {
      log: `${caster.name} distorts space.`,
    };
  },
});
