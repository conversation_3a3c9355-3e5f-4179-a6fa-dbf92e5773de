import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Moonlit Blade',
  name: 'Moonlit Blade',
  element: 'Selene',
  manaCost: 15,
  cooldown: 2,
  description: 'The caster\'s blade is empowered by moonlight, causing their next basic attack to deal bonus magic damage and slow the target.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Slow',
        name: 'Moonlit Blade',
        duration: 1,
        sourceId: caster.id,
      },
      log: `${caster.name} empowers their blade with moonlight.`,
    };
  },
});
