export type ElementNames = 'Arcane' | 'Vortex' | 'Sirius' | 'Selene';

export interface Element {
  readonly name: <PERSON>ement<PERSON>ames;
  readonly description: string;
  readonly advantage: ElementNames;
  readonly disadvantage: ElementNames;
}

export const elements = [
  {
    name: 'Arcane',
    description:
      'The embodiment of cosmic chance and raw magical energy. Arcane wielders manipulate fate and reality, drawing power from the unseen forces that bind the universe.',
    advantage: 'Vortex',
    disadvantage: '<PERSON>lene',
  },
  {
    name: 'Vortex',
    description:
      'The inescapable pull of the void and the relentless march of time. Vortex wielders command gravity and temporal forces, consuming light and energy.',
    advantage: 'Sirius',
    disadvantage: 'Arcane',
  },
  {
    name: '<PERSON>',
    description:
      'The brilliant, primal power of a burning star given form. Sirius wielders are noble and fierce, channeling celestial light to pierce through darkness and illusion.',
    advantage: 'Selene',
    disadvantage: 'Vortex',
  },
  {
    name: '<PERSON><PERSON>',
    description:
      'The power of the moon, shrouded in illusion and dreams. Selene wielders manipulate perception and harness chaotic energy to confuse and misdirect their foes.',
    advantage: 'Arcane',
    disadvantage: '<PERSON>',
  },
] as const;

export function getElementByName(name: ElementNames): Element | undefined {
  return elements.find((el) => el.name.toLowerCase() === name.toLowerCase());
}
