import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: '<PERSON><PERSON><PERSON>',
  name: '<PERSON><PERSON><PERSON>',
  element: 'Vortex',
  manaCost: 50,
  cooldown: 7,
  description: 'Creates a maelstrom that pulls all enemies towards it and deals damage over time.',
  execute: (caster, target, formulas) => {
    // This would require a system for creating and managing persistent area effects with a pull and DoT.
    return {
      log: `${caster.name} creates a Maelstrom.`,
    };
  },
});
