import fs from 'node:fs';
import path from 'node:path';
import type { Skill } from './defineSkill';

const SKILLS_DIR = import.meta.dirname;
const SKILL_FILES: string[] = [];

// Recursively collect all .ts files in subfolders (excluding index.ts)
function collect(dir: string) {
  const entries = fs.readdirSync(dir, { withFileTypes: true });
  for (const entry of entries) {
    const fullPath = path.join(dir, entry.name);
    if (entry.isDirectory()) {
      collect(fullPath);
    } else if (entry.isFile() && entry.name.endsWith('.ts') && entry.name !== 'index.ts') {
      SKILL_FILES.push(fullPath);
    }
  }
}

collect(SKILLS_DIR);

const skillsSet: Record<string, Set<Skill>> = {};

for await (const file of SKILL_FILES) {
  const skill = (await import(file)).default as Skill;
  if (!skillsSet[skill.element]) {
    skillsSet[skill.element] = new Set();
  }

  skillsSet[skill.element as keyof typeof skillsSet]!.add(skill);
}

export const skills = skillsSet;
