import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Moonshadow',
  name: 'Moonshadow',
  element: 'Se<PERSON>',
  manaCost: 25,
  cooldown: 4,
  description: 'The caster steps into the moonshadow, becoming invisible and increasing their movement speed.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Moonshadow Effect',
        name: 'Moonshadow',
        duration: 3,
        sourceId: caster.id,
      },
      log: `${caster.name} steps into the moonshadow.`,
    };
  },
});
