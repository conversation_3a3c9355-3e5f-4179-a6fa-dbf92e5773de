import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Glimmer',
  name: 'Glimmer',
  element: '<PERSON><PERSON>',
  manaCost: 15,
  cooldown: 3,
  description: 'The caster teleports to a nearby location, leaving behind a decoy that explodes after a short delay.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Evasion Up',
        name: 'Glimmer',
        duration: 1,
        sourceId: caster.id,
      },
      log: `${caster.name} glimmers to a new location.`,
    };
  },
});
