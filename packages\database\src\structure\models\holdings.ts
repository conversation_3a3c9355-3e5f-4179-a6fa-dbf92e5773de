import type { InferInsertModel, InferSelectModel } from 'drizzle-orm';
import { and, eq, sql, sum } from 'drizzle-orm';
import { createSelectSchema } from 'drizzle-zod';
import type { HoldingMetadata } from '../../schema/holdings';
import type { ItemType } from '../../schema/items';
import { BaseModel } from '../model';

export type HoldingSelect = InferSelectModel<typeof import('../../schema/holdings').holdings>;
export type HoldingInsert = InferInsertModel<typeof import('../../schema/holdings').holdings>;

export interface HoldingWithItem extends HoldingSelect {
  item: InferSelectModel<typeof import('../../schema/items').items>;
}

export interface AddItemParams {
  userId: string;
  itemId: string;
  quantity?: number;
  metadata?: HoldingMetadata;
  inventorySlot?: number;
}

export interface RemoveItemParams {
  userId: string;
  itemId: string;
  quantity?: number;
  holdingId?: string; // For specific holding instance
}

export interface TransferItemParams {
  fromUserId: string;
  toUserId: string;
  itemId: string;
  quantity: number;
  holdingId?: string;
}

export class Holdings extends BaseModel {
  public schema = createSelectSchema(this.schemas.holdings);

  /**
   * Get a specific holding by ID
   */
  public async get(id: string): Promise<HoldingSelect | undefined> {
    return await this.client.instance.query.holdings.findFirst({
      where: {
        id,
      },
    });
  }

  /**
   * Get a holding with item details
   */
  public async getWithItem(id: string): Promise<HoldingWithItem | undefined> {
    return (await this.client.instance.query.holdings.findFirst({
      where: {
        id,
      },
      with: {
        item: true,
      },
    })) as HoldingWithItem | undefined;
  }

  /**
   * Get all holdings for a user
   */
  public async getUserInventory(userId: string): Promise<HoldingWithItem[]> {
    return (await this.client.instance.query.holdings.findMany({
      where: { userId },
      with: {
        item: true,
      },
      orderBy: { inventorySlot: 'desc', obtainedAt: 'desc' },
    })) as HoldingWithItem[];
  }

  /**
   * Get user's holdings of a specific item
   */
  public async getUserItem(userId: string, itemId: string): Promise<HoldingSelect[]> {
    return await this.client.instance.query.holdings.findMany({
      where: {
        userId,
        itemId,
      },
    });
  }

  /**
   * Get total quantity of an item a user has
   */
  public async getUserItemQuantity(userId: string, itemId: string): Promise<number> {
    const result = await this.client.instance
      .select({
        total: sum(this.schemas.holdings.quantity),
      })
      .from(this.schemas.holdings)
      .where(and(eq(this.schemas.holdings.userId, userId), eq(this.schemas.holdings.itemId, itemId)))
      .execute();

    return Number(result[0]?.total || 0);
  }

  /**
   * Get user's equipped items
   */
  public async getUserEquippedItems(userId: string): Promise<HoldingWithItem[]> {
    return (await this.client.instance.query.holdings.findMany({
      where: {
        userId,
        isEquipped: true,
      },
      with: {
        item: true,
      },
    })) as HoldingWithItem[];
  }

  /**
   * Get user's inventory by item type
   */
  public async getUserItemsByType(userId: string, itemType: ItemType): Promise<HoldingWithItem[]> {
    return (await this.client.instance.query.holdings.findMany({
      where: {
        userId,
      },
      with: {
        item: {
          where: {
            type: itemType,
          },
        },
      },
    })) as HoldingWithItem[];
  }

  /**
   * Add item to user's inventory
   */
  public async addItem(params: AddItemParams): Promise<HoldingSelect> {
    const { userId, itemId, quantity = 1, metadata = {}, inventorySlot } = params;

    // Get item details to check if stackable
    const item = await this.client.instance.query.items.findFirst({
      where: {
        id: itemId,
      },
    });

    if (!item) {
      throw new Error(`Item with ID ${itemId} not found`);
    }

    // If item is stackable, try to add to existing stack
    if (item.stackable) {
      const existingHolding = await this.client.instance.query.holdings.findFirst({
        where: {
          userId,
          itemId,
          inventorySlot,
        },
      });

      if (existingHolding) {
        const newQuantity = Math.min(existingHolding.quantity + quantity, item.maxStack);

        const [updatedHolding] = await this.client.instance
          .update(this.schemas.holdings)
          .set({
            quantity: newQuantity,
            updatedAt: sql`NOW()`,
          })
          .where(eq(this.schemas.holdings.id, existingHolding.id))
          .returning()
          .execute();

        return updatedHolding!;
      }
    }

    // Create new holding
    const [holding] = await this.client.instance
      .insert(this.schemas.holdings)
      .values({
        userId,
        itemId,
        quantity: Math.min(quantity, item.maxStack),
        metadata,
        inventorySlot,
      })
      .returning()
      .execute();

    return holding!;
  }

  /**
   * Remove item from user's inventory
   */
  public async removeItem(params: RemoveItemParams): Promise<boolean> {
    const { userId, itemId, quantity = 1, holdingId } = params;

    if (holdingId) {
      // Remove from specific holding
      const holding = await this.get(holdingId);
      if (!holding || holding.userId !== userId) {
        return false;
      }

      if (holding.quantity <= quantity) {
        // Remove entire holding
        await this.client.instance
          .delete(this.schemas.holdings)
          .where(eq(this.schemas.holdings.id, holdingId))
          .execute();
      } else {
        // Reduce quantity
        await this.client.instance
          .update(this.schemas.holdings)
          .set({
            quantity: holding.quantity - quantity,
            updatedAt: sql`NOW()`,
          })
          .where(eq(this.schemas.holdings.id, holdingId))
          .execute();
      }
      return true;
    }

    // Remove from any holdings of this item
    const holdings = await this.getUserItem(userId, itemId);
    let remainingToRemove = quantity;

    for await (const holding of holdings) {
      if (remainingToRemove <= 0) break;

      if (holding.quantity <= remainingToRemove) {
        // Remove entire holding
        await this.client.instance
          .delete(this.schemas.holdings)
          .where(eq(this.schemas.holdings.id, holding.id))
          .execute();
        remainingToRemove -= holding.quantity;
      } else {
        // Reduce quantity
        await this.client.instance
          .update(this.schemas.holdings)
          .set({
            quantity: holding.quantity - remainingToRemove,
            updatedAt: sql`NOW()`,
          })
          .where(eq(this.schemas.holdings.id, holding.id))
          .execute();
        remainingToRemove = 0;
      }
    }

    return remainingToRemove === 0;
  }

  /**
   * Transfer item between users
   */
  public async transferItem(params: TransferItemParams): Promise<boolean> {
    const { fromUserId, toUserId, itemId, quantity, holdingId } = params;

    // Check if sender has enough items
    const senderQuantity = await this.getUserItemQuantity(fromUserId, itemId);
    if (senderQuantity < quantity) {
      return false;
    }

    // Get item to check if tradeable
    const item = await this.client.instance.query.items.findFirst({
      where: {
        id: itemId,
        tradeable: true,
      },
    });

    if (!item) {
      return false;
    }

    // Remove from sender
    const removed = await this.removeItem({
      userId: fromUserId,
      itemId,
      quantity,
      holdingId,
    });

    if (!removed) {
      return false;
    }

    // Add to receiver
    await this.addItem({
      userId: toUserId,
      itemId,
      quantity,
      metadata: {
        acquisition: {
          source: 'TRADE',
          sourceId: fromUserId,
          acquiredAt: Date.now(),
        },
      },
    });

    return true;
  }

  /**
   * Equip an item
   */
  public async equipItem(userId: string, holdingId: string): Promise<boolean> {
    const holding = await this.getWithItem(holdingId);
    if (!holding || holding.userId !== userId) {
      return false;
    }

    // Check if item is equipment type
    if (holding.item.type !== 'EQUIPMENT') {
      return false;
    }

    // Unequip other items in the same slot if needed
    const equipmentSlot = holding.metadata.equipment?.slot;
    if (equipmentSlot) {
      await this.client.instance
        .update(this.schemas.holdings)
        .set({
          isEquipped: false,
          updatedAt: sql`NOW()`,
        })
        .where(
          and(
            eq(this.schemas.holdings.userId, userId),
            eq(this.schemas.holdings.isEquipped, true),
            sql`metadata->'equipment'->>'slot' = ${equipmentSlot}`
          )
        )
        .execute();
    }

    // Equip the item
    const [updated] = await this.client.instance
      .update(this.schemas.holdings)
      .set({
        isEquipped: true,
        updatedAt: sql`NOW()`,
      })
      .where(eq(this.schemas.holdings.id, holdingId))
      .returning()
      .execute();

    return updated !== undefined;
  }

  /**
   * Unequip an item
   */
  public async unequipItem(userId: string, holdingId: string): Promise<boolean> {
    const [updated] = await this.client.instance
      .update(this.schemas.holdings)
      .set({
        isEquipped: false,
        updatedAt: sql`NOW()`,
      })
      .where(and(eq(this.schemas.holdings.id, holdingId), eq(this.schemas.holdings.userId, userId)))
      .returning()
      .execute();

    return updated !== undefined;
  }

  /**
   * Update holding metadata
   */
  public async updateMetadata(holdingId: string, metadata: Partial<HoldingMetadata>): Promise<boolean> {
    const holding = await this.get(holdingId);
    if (!holding) return false;

    const updatedMetadata = { ...holding.metadata, ...metadata };

    const [updated] = await this.client.instance
      .update(this.schemas.holdings)
      .set({
        metadata: updatedMetadata,
        updatedAt: sql`NOW()`,
      })
      .where(eq(this.schemas.holdings.id, holdingId))
      .returning()
      .execute();

    return updated !== undefined;
  }

  /**
   * Get inventory statistics for a user
   */
  public async getUserStats(userId: string): Promise<{
    totalItems: number;
    totalSlots: number;
    equippedItems: number;
    favoriteItems: number;
    itemsByType: Record<ItemType, number>;
  }> {
    const inventory = await this.getUserInventory(userId);

    const stats = {
      totalItems: inventory.reduce((s, holding) => s + holding.quantity, 0),
      totalSlots: inventory.length,
      equippedItems: inventory.filter((holding) => holding.isEquipped).length,
      favoriteItems: inventory.filter((holding) => holding.isFavorite).length,
      itemsByType: {} as Record<ItemType, number>,
    };

    for (const holding of inventory) {
      const type = holding.item.type;
      stats.itemsByType[type] = (stats.itemsByType[type] || 0) + holding.quantity;
    }

    return stats;
  }
}
