import { getObject, translate } from '@megami/locale';
import { <PERSON><PERSON>lag<PERSON>, SeparatorBuilder, SeparatorSpacingSize, TextDisplayBuilder } from 'discord.js';
import { defineSubCommand } from '../../../handlers/command';
import { MegamiContainer } from '../../../helpers/containers';
import { defer } from '../../../helpers/defer';

export default defineSubCommand((builder) => ({
  builder: builder
    .setName(translate('commands.inventory.modules.use.name'))
    .setNameLocalizations(getObject('commands.inventory.modules.use.name'))
    .setDescription(translate('commands.inventory.modules.use.description'))
    .setDescriptionLocalizations(getObject('commands.inventory.modules.use.description'))
    .addStringOption((option) =>
      option
        .setName(translate('commands.inventory.modules.use.options.item.name'))
        .setNameLocalizations(getObject('commands.inventory.modules.use.options.item.name'))
        .setDescription(translate('commands.inventory.modules.use.options.item.description'))
        .setDescriptionLocalizations(getObject('commands.inventory.modules.use.options.item.description'))
        .setRequired(true)
        .setAutocomplete(true)
    )
    .addUserOption((option) =>
      option
        .setName(translate('commands.inventory.modules.use.options.target.name'))
        .setNameLocalizations(getObject('commands.inventory.modules.use.options.target.name'))
        .setDescription(translate('commands.inventory.modules.use.options.target.description'))
        .setDescriptionLocalizations(getObject('commands.inventory.modules.use.options.target.description'))
        .setRequired(false)
    ),
  config: {},
  autocomplete: async (interaction) => {
    const focused = interaction.options.getFocused(true);

    if (focused.name === 'item') {
      try {
        const user = await interaction.client.database.users.get(interaction.user.id);
        if (!user) {
          await interaction.respond([]);
          return;
        }

        // Get user's consumable items
        const inventory = await interaction.client.database.holdings.getUserItemsByType(user.id, 'CONSUMABLE');

        // Filter by search term and limit results
        const filteredItems = inventory
          .filter(
            (holding) => holding.item.name.toLowerCase().includes(focused.value.toLowerCase()) && holding.quantity > 0
          )
          .slice(0, 25);

        await interaction.respond(
          filteredItems.map((holding) => ({
            name: `${holding.item.icon || '🧪'} ${holding.item.name} (x${holding.quantity})`,
            value: holding.item.name,
          }))
        );
      } catch (_error) {
        await interaction.respond([]);
      }
    }
  },
  execute: async (interaction) => {
    await defer(interaction, false);

    const user = await interaction.client.database.users.get(interaction.user.id);
    if (!user) {
      const container = new MegamiContainer([
        new TextDisplayBuilder().setContent('❌ **Not Registered**'),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent(translate('errors.user.unregistered.description')),
      ]);

      return await interaction.editReply({
        components: [container],
        flags: MessageFlags.IsComponentsV2,
      });
    }

    const itemName = interaction.options.getString('item', true);
    const quantity = interaction.options.getInteger('quantity') || 1;

    try {
      // Find the item
      const item = await interaction.client.database.items.getByName(itemName);
      if (!item) {
        const container = new MegamiContainer([
          new TextDisplayBuilder().setContent('❌ **Item Not Found**'),
          new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
          new TextDisplayBuilder().setContent(`No item found with the name "${itemName}".`),
          new TextDisplayBuilder().setContent('Please check the spelling and try again.'),
        ]);

        return await interaction.editReply({
          components: [container],
          flags: MessageFlags.IsComponentsV2,
        });
      }

      // Check if item is consumable
      if (item.type !== 'CONSUMABLE') {
        const container = new MegamiContainer([
          new TextDisplayBuilder().setContent('❌ **Item Not Consumable**'),
          new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
          new TextDisplayBuilder().setContent(`"${item.name}" is not a consumable item.`),
          new TextDisplayBuilder().setContent('Only consumable items can be used.'),
        ]);

        return await interaction.editReply({
          components: [container],
          flags: MessageFlags.IsComponentsV2,
        });
      }

      // Check if user has the item
      const userQuantity = await interaction.client.database.holdings.getUserItemQuantity(user.id, item.id);
      if (userQuantity < quantity) {
        const container = new MegamiContainer([
          new TextDisplayBuilder().setContent('❌ **Insufficient Items**'),
          new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
          new TextDisplayBuilder().setContent(`You need ${quantity}x "${item.name}" but only have ${userQuantity}.`),
          new TextDisplayBuilder().setContent('Check your inventory and try again.'),
        ]);

        return await interaction.editReply({
          components: [container],
          flags: MessageFlags.IsComponentsV2,
        });
      }

      // Apply item effects using the new effects system
      const results: string[] = [];

      // Apply effects multiple times based on quantity used (concurrently)
      const effectPromises = Array.from({ length: quantity }, () =>
        interaction.client.database.items.applyItemEffects(user.id, item.id, 'USE')
      );
      await Promise.all(effectPromises);

      // Process effects for user feedback (if any exist)
      const itemEffects = item.metadata?.effects || [];
      for (const effect of itemEffects) {
        if (!effect.onUse) continue; // Only process effects that trigger on use

        switch (effect.type) {
          case 'RIFT_MULTIPLIER': {
            const duration = effect.duration ? ` for ${Math.floor(effect.duration / 60)} minutes` : '';
            results.push(`⚡ Rift regeneration multiplied by ${effect.data?.multiplier || 1}x${duration}`);
            break;
          }

          case 'RIFT_BONUS': {
            const duration = effect.duration ? ` for ${Math.floor(effect.duration / 60)} minutes` : '';
            results.push(`💎 +${effect.data?.bonus || 0} bonus rifts per regeneration${duration}`);
            break;
          }

          case 'EXP_MULTIPLIER': {
            const duration = effect.duration ? ` for ${Math.floor(effect.duration / 60)} minutes` : '';
            results.push(`⭐ Experience gains multiplied by ${effect.data?.multiplier || 1}x${duration}`);
            break;
          }

          case 'EXP_BONUS': {
            const duration = effect.duration ? ` for ${Math.floor(effect.duration / 60)} minutes` : '';
            results.push(`� +${effect.data?.bonus || 0} bonus experience per gain${duration}`);
            break;
          }

          case 'CURRENCY_MULTIPLIER': {
            const currency = effect.data?.currency || 'Unknown';
            const duration = effect.duration ? ` for ${Math.floor(effect.duration / 60)} minutes` : '';
            results.push(`💰 ${currency} gains multiplied by ${effect.data?.multiplier || 1}x${duration}`);
            break;
          }

          case 'CURRENCY_BONUS': {
            const currency = effect.data?.currency || 'Unknown';
            const duration = effect.duration ? ` for ${Math.floor(effect.duration / 60)} minutes` : '';
            results.push(`💸 +${effect.data?.bonus || 0} bonus ${currency} per gain${duration}`);
            break;
          }

          case 'CUSTOM': {
            const duration = effect.duration ? ` for ${Math.floor(effect.duration / 60)} minutes` : '';
            results.push(`🔮 ${effect.name}${duration}`);
            break;
          }

          default: {
            results.push(`✨ ${effect.name || 'Unknown effect'} applied`);
          }
        }
      }

      // If no effects were shown, check if any effects were actually applied
      if (results.length === 0 && itemEffects.some(e => e.onUse)) {
        results.push('✨ Effects applied successfully');
      }

      // Remove the used items from inventory
      const removed = await interaction.client.database.holdings.removeItem({
        userId: user.id,
        itemId: item.id,
        quantity,
      });

      if (!removed) {
        const container = new MegamiContainer([
          new TextDisplayBuilder().setContent('❌ **Failed to Use Item**'),
          new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
          new TextDisplayBuilder().setContent('An error occurred while using the item.'),
          new TextDisplayBuilder().setContent('Please try again.'),
        ]);

        return await interaction.editReply({
          components: [container],
          flags: MessageFlags.IsComponentsV2,
        });
      }

      // Show success message with effects
      const rarityEmoji = getRarityEmoji(item.rarity);

      const components = [
        new TextDisplayBuilder().setContent('✅ **Item Used Successfully**'),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent(`${rarityEmoji} ${item.icon || '🧪'} **${item.name}** x${quantity}`),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
      ];

      if (results.length > 0) {
        components.push(new TextDisplayBuilder().setContent('**Effects Applied:**'));
        for (const result of results) {
          components.push(new TextDisplayBuilder().setContent(`  ${result}`));
        }
      } else {
        components.push(new TextDisplayBuilder().setContent('Item consumed with no immediate effects.'));
      }

      const remainingQuantity = userQuantity - quantity;
      if (remainingQuantity > 0) {
        components.push(
          new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
          new TextDisplayBuilder().setContent(`**Remaining:** ${remainingQuantity}x ${item.name}`)
        );
      }

      const container = new MegamiContainer(components);
      await interaction.editReply({
        components: [container],
        flags: MessageFlags.IsComponentsV2,
      });
    } catch (error) {
      const container = new MegamiContainer([
        new TextDisplayBuilder().setContent('❌ **Error Using Item**'),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent('An unexpected error occurred while using the item.'),
        new TextDisplayBuilder().setContent('Please try again or contact support.'),
      ]);

      await interaction.editReply({
        components: [container],
        flags: MessageFlags.IsComponentsV2,
      });

      interaction.client.logger.error('Error using item:', error);
    }
  },
}));

function getRarityEmoji(rarity: string): string {
  const emojis: Record<string, string> = {
    COMMON: '⚪',
    UNCOMMON: '🟢',
    RARE: '🔵',
    EPIC: '🟣',
    LEGENDARY: '🟡',
    MYTHIC: '🔴',
  };
  return emojis[rarity] || '⚪';
}
