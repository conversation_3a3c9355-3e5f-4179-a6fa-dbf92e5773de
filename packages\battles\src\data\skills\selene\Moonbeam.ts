import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: '<PERSON>beam',
  name: '<PERSON>be<PERSON>',
  element: 'Selene',
  manaCost: 10,
  cooldown: 1,
  description: 'A concentrated beam of moonlight that damages the target.',
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const finalDamage = Math.round(damage * 1);
    return {
      damage: finalDamage,
      isCritical,
      log: `${caster.name} strikes ${target.name} with a Moonbeam for ${finalDamage} damage${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
