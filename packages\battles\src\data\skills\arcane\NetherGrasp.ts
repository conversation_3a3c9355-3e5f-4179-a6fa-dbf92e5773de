import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Nether Grasp',
  name: 'Nether Grasp',
  element: 'Arcane',
  manaCost: 25,
  cooldown: 3,
  description: 'A shadowy hand grasps the target, dealing damage over time.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Mana Burn Effect',
        name: 'Nether Grasp',
        duration: 3,
        sourceId: caster.id,
      },
      log: `${caster.name} grasps ${target.name} with a Nether Grasp, causing them to take damage over time.`,
    };
  },
});
