import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Full Moon',
  name: 'Full Moon',
  element: '<PERSON><PERSON>',
  manaCost: 40,
  cooldown: 6,
  description: 'The caster is empowered by the full moon, increasing their attack speed and causing their attacks to cleave.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Attack Speed Up',
        name: 'Full Moon',
        duration: 4,
        sourceId: caster.id,
      },
      log: `${caster.name} is empowered by the full moon.`,
    };
  },
});
