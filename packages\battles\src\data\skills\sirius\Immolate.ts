import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Immolate',
  name: 'Immolate',
  element: 'Sirius',
  manaCost: 20,
  cooldown: 3,
  description: 'The caster immolates themselves, dealing damage to all nearby enemies over time.',
  execute: (caster, target, formulas) => {
    // This would require a status effect system to handle the DoT.
    return {
      log: `${caster.name} immolates themselves, burning all nearby enemies.`,
    };
  },
});
