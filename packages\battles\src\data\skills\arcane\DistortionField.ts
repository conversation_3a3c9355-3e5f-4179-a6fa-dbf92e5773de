import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Distortion Field',
  name: 'Distortion Field',
  element: 'Arcane',
  manaCost: 30,
  cooldown: 5,
  description: 'Creates a field of distorted space that has a chance to deflect incoming attacks.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Evasion Up',
        name: 'Distortion Field',
        duration: 3,
        sourceId: caster.id,
      },
      log: `${caster.name} creates a Distortion Field, making them harder to hit.`,
    };
  },
});
