import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: '<PERSON>ymorph',
  name: '<PERSON><PERSON>or<PERSON>',
  element: '<PERSON>ane',
  manaCost: 30,
  cooldown: 5,
  description: 'Transforms the target into a harmless creature for a short time.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Polymorph Effect',
        name: 'Polymorph',
        duration: 2,
        sourceId: caster.id,
      },
      log: `${caster.name} transforms ${target.name} into a harmless creature.`,
    };
  },
});
