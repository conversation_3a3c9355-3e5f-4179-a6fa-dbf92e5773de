import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Gravity Slam',
  name: 'Gravity Slam',
  element: 'Vortex',
  manaCost: 16,
  cooldown: 2,
  description: 'Slam the target with gravitational force, dealing damage and reducing their attack power.',
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);

    return {
      damage: Math.round(damage * 1.2),
      isCritical,
      appliedEffect: {
        id: 'gravity_slam',
        name: 'Gravity Slam',
        duration: 3,
        potency: -10, // -10 attack
        sourceId: caster.id,
      },
      log: `${caster.name} slams ${target.name} with gravity for ${Math.round(damage * 1.2)} damage${isCritical ? ' (CRIT!)' : ''} and weakens their attacks!`,
    };
  },
});
