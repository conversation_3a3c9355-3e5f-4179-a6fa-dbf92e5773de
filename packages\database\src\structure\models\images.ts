import type { InferInsertModel } from 'drizzle-orm';
import { createSelectSchema } from 'drizzle-zod';
import { BaseModel } from '../model';

export class Images extends BaseModel {
  public schema = createSelectSchema(this.schemas.images);

  public async create(data: InferInsertModel<typeof this.schemas.images>) {
    const [image] = await this.client.instance.insert(this.schemas.images).values(data).returning().execute();

    return image!;
  }

  public async of(characterId: string) {
    return await this.client.instance.query.images.findMany({
      where: {
        characterId,
      },
    });
  }
}
