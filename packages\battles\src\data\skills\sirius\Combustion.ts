import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Combustion',
  name: 'Combustion',
  element: 'Sirius',
  manaCost: 35,
  cooldown: 5,
  description: 'The target combusts, taking damage over time. If the target dies while combusting, they explode and damage all nearby enemies.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Burn',
        name: 'Combustion',
        duration: 3,
        sourceId: caster.id,
      },
      log: `${caster.name} causes ${target.name} to combust.`,
    };
  },
});
