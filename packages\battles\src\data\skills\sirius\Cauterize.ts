import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: '<PERSON><PERSON><PERSON><PERSON>',
  name: '<PERSON><PERSON><PERSON><PERSON>',
  element: 'Sirius',
  manaCost: 20,
  cooldown: 4,
  description: 'The caster cauterizes their wounds, healing for a moderate amount but taking a small amount of damage over time.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Health Regen Up',
        name: '<PERSON><PERSON><PERSON><PERSON>',
        duration: 3,
        sourceId: caster.id,
      },
      log: `${caster.name} cauterizes their wounds.`,
    };
  },
});
