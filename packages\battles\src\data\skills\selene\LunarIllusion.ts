import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Lunar Illusion',
  name: 'Lunar Illusion',
  element: 'Selene',
  manaCost: 14,
  cooldown: 2,
  description: 'Create illusions that confuse the target, reducing their accuracy and dealing damage.',
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const baseChance = 60;
    const confuseChance = formulas.calculateEffectChance(caster, target, baseChance);
    const didConfuse = formulas.percentage(confuseChance / 100);

    return {
      damage: Math.round(damage * 0.9),
      isCritical,
      appliedEffect: didConfuse
        ? {
            id: 'lunar_confusion',
            name: 'Lunar Confusion',
            duration: 3,
            potency: -12, // -12 luck (accuracy)
            sourceId: caster.id,
          }
        : undefined,
      log: `${caster.name} creates Lunar Illusions around ${target.name} for ${Math.round(damage * 0.9)} damage${isCritical ? ' (CRIT!)' : ''}${didConfuse ? ' and confuses them!' : '!'}`,
    };
  },
});
