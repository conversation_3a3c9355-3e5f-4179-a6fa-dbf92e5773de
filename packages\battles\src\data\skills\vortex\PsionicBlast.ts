import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Psionic Blast',
  name: 'Psionic Blast',
  element: 'Vortex',
  manaCost: 20,
  cooldown: 3,
  description: 'A blast of psionic energy that damages and has a chance to confuse the target.',
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const finalDamage = Math.round(damage * 1.2);
    // This would also require a status effect system to handle confusion.
    return {
      damage: finalDamage,
      isCritical,
      log: `${caster.name} blasts ${target.name} with psionic energy for ${finalDamage} damage${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
