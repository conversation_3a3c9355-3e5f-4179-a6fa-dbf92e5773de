import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Event Horizon',
  name: 'Event Horizon',
  element: 'Vortex',
  manaCost: 50,
  cooldown: 7,
  description: 'Creates an event horizon around the caster, preventing any enemy from escaping and pulling them closer.',
  execute: (caster, target, formulas) => {
    // This would require a system for creating and managing persistent area effects with a pull mechanic.
    return {
      log: `${caster.name} creates an Event Horizon.`,
    };
  },
});
