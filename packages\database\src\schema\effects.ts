import { integer, jsonb, pgTable, serial, text, timestamp, uniqueIndex } from 'drizzle-orm/pg-core';
import { users } from './users';
import { items } from './items';

export type EffectType = 
  | 'RIFT_MULTIPLIER'     // Multiplies rift gains
  | 'RIFT_BONUS'          // Adds flat bonus to rift gains
  | 'EXP_MULTIPLIER'      // Multiplies experience gains
  | 'EXP_BONUS'           // Adds flat bonus to experience gains
  | 'CURRENCY_MULTIPLIER' // Multiplies specific currency gains
  | 'CURRENCY_BONUS'      // Adds flat bonus to specific currency gains
  | 'COOLDOWN_REDUCTION'  // Reduces cooldowns
  | 'DROP_RATE_BONUS'     // Increases drop rates
  | 'CUSTOM';             // Custom effects with arbitrary data

export type EffectSource = 
  | 'ITEM'                // Effect from an item
  | 'SKILL'               // Effect from a character skill
  | 'BUFF'                // Temporary buff
  | 'ACHIEVEMENT'         // Permanent effect from achievement
  | 'EVENT'               // Event-based effect
  | 'ADMIN';              // Admin-granted effect

export interface EffectData {
  // For multiplier effects
  multiplier?: number;
  
  // For flat bonus effects
  bonus?: number;
  
  // For currency-specific effects
  currency?: string;
  
  // For cooldown effects
  cooldownType?: string;
  reductionPercent?: number;
  
  // For drop rate effects
  dropType?: string;
  rateIncrease?: number;
  
  // For custom effects
  customData?: Record<string, any>;
  
  // Stacking behavior
  stackable?: boolean;
  maxStacks?: number;
}

export const effects = pgTable(
  'effects',
  {
    id: serial('id').primaryKey(),
    userId: text('user_id')
      .notNull()
      .references(() => users.id, { onDelete: 'cascade' }),
    
    // Effect identification
    type: text('type').$type<EffectType>().notNull(),
    source: text('source').$type<EffectSource>().notNull(),
    sourceId: text('source_id'), // ID of the item/skill/etc that granted this effect
    
    // Effect properties
    name: text('name').notNull(),
    description: text('description').notNull(),
    data: jsonb('data').$type<EffectData>().default({}).notNull(),
    
    // Stacking
    stacks: integer('stacks').default(1).notNull(),
    
    // Duration (null = permanent)
    expiresAt: timestamp('expires_at'),
    
    // Metadata
    createdAt: timestamp('created_at').defaultNow().notNull(),
    updatedAt: timestamp('updated_at').defaultNow().notNull(),
  },
  (table) => [
    uniqueIndex('user_effect_source_idx').on(table.userId, table.type, table.sourceId),
  ]
);
