import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Eclipse',
  name: 'Eclipse',
  element: 'Selene',
  manaCost: 40,
  cooldown: 6,
  description: 'Causes an eclipse, dealing heavy damage to all enemies and blinding them.',
  execute: (caster, target, formulas) => {
    // This would require a more complex targeting and status effect system.
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const finalDamage = Math.round(damage * 1.8);
    return {
      damage: finalDamage,
      isCritical,
      log: `${caster.name} causes an Eclipse, dealing ${finalDamage} damage to all enemies and blinding them${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
