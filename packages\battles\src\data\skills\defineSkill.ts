import type { formulas } from '../../formulas';
import type { ActiveStatusEffect, BattleEntity } from '../../types';

export type SkillElement = 'Arcane' | 'Vortex' | 'Sirius' | 'Selene';

export interface Skill {
  id: string;
  name: string;
  element: SkillElement;
  manaCost: number;
  cooldown: number;
  description: string;
  execute: (caster: BattleEntity, target: BattleEntity, operators: typeof formulas) => SkillResult;
}

export interface SkillResult {
  damage?: number;
  isCritical?: boolean;
  appliedEffect?: ActiveStatusEffect;
  log: string;
}

export function defineSkill(skill: Skill): Skill {
  return skill;
}
