import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Ignite',
  name: 'Ignite',
  element: 'Sirius',
  manaCost: 10,
  cooldown: 2,
  description: 'Ignites the target, causing them to take damage over time.',
  execute: (caster, target, formulas) => {
    // This would require a status effect system to handle the DoT.
    return {
      log: `${caster.name} ignites ${target.name}.`,
    };
  },
});
