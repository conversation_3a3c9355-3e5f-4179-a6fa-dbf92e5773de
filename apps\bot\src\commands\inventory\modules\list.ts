import type { ItemType } from '@megami/database';
import type { HoldingWithItem } from '@megami/database/src/structure/models/holdings';
import { getObject, translate } from '@megami/locale';
import { MessageFlags, SeparatorBuilder, SeparatorSpacingSize, TextDisplayBuilder } from 'discord.js';
import { defineSubCommand } from '../../../handlers/command';
import { MegamiContainer } from '../../../helpers/containers';
import { defer } from '../../../helpers/defer';

export default defineSubCommand((builder) => ({
  builder: builder
    .setName(translate('commands.inventory.modules.list.name'))
    .setNameLocalizations(getObject('commands.inventory.modules.list.name'))
    .setDescription(translate('commands.inventory.modules.list.description'))
    .setDescriptionLocalizations(getObject('commands.inventory.modules.list.description'))
    .addStringOption((option) =>
      option
        .setName(translate('commands.inventory.modules.list.options.type.name'))
        .setNameLocalizations(getObject('commands.inventory.modules.list.options.type.name'))
        .setDescription(translate('commands.inventory.modules.list.options.type.description'))
        .setDescriptionLocalizations(getObject('commands.inventory.modules.list.options.type.description'))
        .setRequired(false)
        .addChoices(
          { name: '🎒 All Items', value: 'all' },
          { name: '⚔️ Equipment', value: 'EQUIPMENT' },
          { name: '🧪 Consumables', value: 'CONSUMABLE' },
          { name: '🔨 Materials', value: 'MATERIAL' },
          { name: '💎 Collectibles', value: 'COLLECTIBLE' },
          { name: '💰 Currency', value: 'CURRENCY' },
          { name: '🎁 Gifts', value: 'GIFT' },
          { name: '🗝️ Keys', value: 'KEY' },
          { name: '📦 Miscellaneous', value: 'MISC' }
        )
    )
    .addBooleanOption((option) =>
      option.setName('equipped-only').setDescription('Show only equipped items').setRequired(false)
    )
    .addBooleanOption((option) =>
      option.setName('favorites-only').setDescription('Show only favorite items').setRequired(false)
    ),
  config: {},
  execute: async (interaction) => {
    await defer(interaction, false);

    const user = await interaction.client.database.users.get(interaction.user.id);
    if (!user) {
      const container = new MegamiContainer([
        new TextDisplayBuilder().setContent('❌ **Not Registered**'),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent(translate('errors.user.unregistered.description')),
      ]);

      return await interaction.editReply({
        components: [container],
        flags: MessageFlags.IsComponentsV2,
      });
    }

    const typeFilter = interaction.options.getString('type') || 'all';
    const equippedOnly = interaction.options.getBoolean('equipped-only') ?? false;
    const favoritesOnly = interaction.options.getBoolean('favorites-only') ?? false;

    try {
      let inventory: HoldingWithItem[] = [];

      if (equippedOnly) {
        inventory = await interaction.client.database.holdings.getUserEquippedItems(user.id);
      } else if (typeFilter === 'all') {
        inventory = await interaction.client.database.holdings.getUserInventory(user.id);
      } else {
        inventory = await interaction.client.database.holdings.getUserItemsByType(user.id, typeFilter as ItemType);
      }

      // Apply favorites filter if requested
      if (favoritesOnly) {
        inventory = inventory.filter((holding) => holding.isFavorite);
      }

      if (inventory.length === 0) {
        const filterText = getFilterText(typeFilter, equippedOnly, favoritesOnly);

        const container = new MegamiContainer([
          new TextDisplayBuilder().setContent('🎒 **Your Inventory**'),
          new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
          new TextDisplayBuilder().setContent(`No items found${filterText}.`),
          new TextDisplayBuilder().setContent('Complete activities and quests to earn items!'),
        ]);

        return await interaction.editReply({
          components: [container],
          flags: MessageFlags.IsComponentsV2,
        });
      }

      // Group items by type for better organization
      const itemsByType = inventory.reduce(
        (acc, holding) => {
          const type = holding.item.type;
          if (!acc[type]) acc[type] = [];
          acc[type].push(holding);
          return acc;
        },
        {} as Record<string, typeof inventory>
      );

      // Get inventory stats
      const stats = await interaction.client.database.holdings.getUserStats(user.id);
      const filterText = getFilterText(typeFilter, equippedOnly, favoritesOnly);

      const components = [
        new TextDisplayBuilder().setContent(`🎒 **Your Inventory${filterText}**`),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent(
          `**Total Items:** ${stats.totalItems} | **Slots Used:** ${stats.totalSlots}`
        ),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
      ];

      // Display items by type
      for (const [type, items] of Object.entries(itemsByType)) {
        const typeEmoji = getTypeEmoji(type);
        components.push(new TextDisplayBuilder().setContent(`${typeEmoji} **${type}** (${items.length} items)`));

        // Sort items by rarity and name
        const sortedItems = items.sort((a, b) => {
          const rarityOrder = ['COMMON', 'UNCOMMON', 'RARE', 'EPIC', 'LEGENDARY', 'MYTHIC'];
          const rarityDiff = rarityOrder.indexOf(b.item.rarity) - rarityOrder.indexOf(a.item.rarity);
          if (rarityDiff !== 0) return rarityDiff;
          return a.item.name.localeCompare(b.item.name);
        });

        for (const holding of sortedItems.slice(0, 8)) {
          // Limit to 8 items per type
          const item = holding.item;
          const rarityEmoji = getRarityEmoji(item.rarity);
          const equippedText = holding.isEquipped ? ' ⚡' : '';
          const favoriteText = holding.isFavorite ? ' ⭐' : '';
          const lockedText = holding.isLocked ? ' 🔒' : '';

          components.push(
            new TextDisplayBuilder().setContent(
              `  ${rarityEmoji} ${item.icon || getTypeEmoji(item.type)} **${item.name}** x${holding.quantity}${equippedText}${favoriteText}${lockedText}`
            )
          );
        }

        if (items.length > 8) {
          components.push(new TextDisplayBuilder().setContent(`  ... and ${items.length - 8} more items`));
        }

        components.push(new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small));
      }

      // Add legend
      components.push(new TextDisplayBuilder().setContent('**Legend:** ⚡ Equipped | ⭐ Favorite | 🔒 Locked'));

      const container = new MegamiContainer(components);
      await interaction.editReply({
        components: [container],
        flags: MessageFlags.IsComponentsV2,
      });
    } catch (error) {
      const container = new MegamiContainer([
        new TextDisplayBuilder().setContent('❌ **Error Loading Inventory**'),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent('An unexpected error occurred while loading your inventory.'),
        new TextDisplayBuilder().setContent('Please try again or contact support.'),
      ]);

      await interaction.editReply({
        components: [container],
        flags: MessageFlags.IsComponentsV2,
      });

      interaction.client.logger.error('Error loading user inventory:', error);
    }
  },
}));

function getFilterText(typeFilter: string, equippedOnly: boolean, favoritesOnly: boolean): string {
  const filters: string[] = [];

  if (typeFilter !== 'all') {
    filters.push(typeFilter.toLowerCase());
  }

  if (equippedOnly) {
    filters.push('equipped');
  }

  if (favoritesOnly) {
    filters.push('favorites');
  }

  return filters.length > 0 ? ` (${filters.join(', ')})` : '';
}

function getTypeEmoji(type: string): string {
  const emojis: Record<string, string> = {
    EQUIPMENT: '⚔️',
    CONSUMABLE: '🧪',
    MATERIAL: '🔨',
    COLLECTIBLE: '💎',
    CURRENCY: '💰',
    GIFT: '🎁',
    KEY: '🗝️',
    MISC: '📦',
  };
  return emojis[type] || '📦';
}

function getRarityEmoji(rarity: string): string {
  const emojis: Record<string, string> = {
    COMMON: '⚪',
    UNCOMMON: '🟢',
    RARE: '🔵',
    EPIC: '🟣',
    LEGENDARY: '🟡',
    MYTHIC: '🔴',
  };
  return emojis[rarity] || '⚪';
}
