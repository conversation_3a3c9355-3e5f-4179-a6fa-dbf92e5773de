import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Stellar Regeneration',
  name: 'Stellar Regeneration',
  element: 'Sirius',
  manaCost: 20,
  cooldown: 4,
  description: 'Channel stellar energy to heal yourself and boost all stats temporarily.',
  execute: (caster) => {
    const healAmount = Math.round(caster.stats.maxHealth * 0.25); // 25% max health
    caster.stats.health = Math.min(caster.stats.maxHealth, caster.stats.health + healAmount);

    return {
      appliedEffect: {
        id: 'stellar_regeneration',
        name: 'Stellar Regeneration',
        duration: 3,
        potency: 5, // +5 to all stats (implementation dependent)
        sourceId: caster.id,
      },
      log: `${caster.name} channels stellar energy, healing for ${healAmount} HP and boosting all abilities!`,
    };
  },
});
