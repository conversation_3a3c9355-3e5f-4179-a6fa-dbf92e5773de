import { getObject, translate } from '@megami/locale';
import { <PERSON><PERSON>lag<PERSON>, SeparatorBuilder, SeparatorSpacingSize, TextDisplayBuilder } from 'discord.js';
import { defineSubCommand } from '../../../handlers/command';
import { MegamiContainer } from '../../../helpers/containers';
import { defer } from '../../../helpers/defer';

export default defineSubCommand((builder) => ({
  builder: builder
    .setName(translate('commands.inventory.modules.gift.name'))
    .setNameLocalizations(getObject('commands.inventory.modules.gift.name'))
    .setDescription(translate('commands.inventory.modules.gift.description'))
    .setDescriptionLocalizations(getObject('commands.inventory.modules.gift.description'))
    .addUserOption((option) =>
      option
        .setName(translate('commands.inventory.modules.gift.options.recipient.name'))
        .setNameLocalizations(getObject('commands.inventory.modules.gift.options.recipient.name'))
        .setDescription(translate('commands.inventory.modules.gift.options.recipient.description'))
        .setDescriptionLocalizations(getObject('commands.inventory.modules.gift.options.recipient.description'))
        .setRequired(true)
    )
    .addStringOption((option) =>
      option
        .setName(translate('commands.inventory.modules.gift.options.item.name'))
        .setNameLocalizations(getObject('commands.inventory.modules.gift.options.item.name'))
        .setDescription(translate('commands.inventory.modules.gift.options.item.description'))
        .setDescriptionLocalizations(getObject('commands.inventory.modules.gift.options.item.description'))
        .setRequired(true)
        .setAutocomplete(true)
    )
    .addStringOption((option) =>
      option
        .setName(translate('commands.inventory.modules.gift.options.message.name'))
        .setNameLocalizations(getObject('commands.inventory.modules.gift.options.message.name'))
        .setDescription(translate('commands.inventory.modules.gift.options.message.description'))
        .setDescriptionLocalizations(getObject('commands.inventory.modules.gift.options.message.description'))
        .setRequired(false)
        .setDescription('Optional message to include with the gift')
        .setRequired(false)
        .setMaxLength(200)
    ),
  config: {},
  autocomplete: async (interaction) => {
    const focused = interaction.options.getFocused(true);

    if (focused.name === 'item') {
      try {
        const user = await interaction.client.database.users.get(interaction.user.id);
        if (!user) {
          await interaction.respond([]);
          return;
        }

        // Get user's tradeable items
        const inventory = await interaction.client.database.holdings.getUserInventory(user.id);

        // Filter by search term, tradeable items, and limit results
        const filteredItems = inventory
          .filter(
            (holding) =>
              holding.item.name.toLowerCase().includes(focused.value.toLowerCase()) &&
              holding.item.tradeable &&
              holding.quantity > 0 &&
              !holding.isLocked
          )
          .slice(0, 25);

        await interaction.respond(
          filteredItems.map((holding) => ({
            name: `${holding.item.icon || '📦'} ${holding.item.name} (x${holding.quantity})`,
            value: holding.item.name,
          }))
        );
      } catch (_error) {
        await interaction.respond([]);
      }
    }
  },
  execute: async (interaction) => {
    await defer(interaction, false);

    const user = await interaction.client.database.users.get(interaction.user.id);
    if (!user) {
      const container = new MegamiContainer([
        new TextDisplayBuilder().setContent('❌ **Not Registered**'),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent(translate('errors.user.unregistered.description')),
      ]);

      return await interaction.editReply({
        components: [container],
        flags: MessageFlags.IsComponentsV2,
      });
    }

    const recipient = interaction.options.getUser('recipient', true);
    const itemName = interaction.options.getString('item', true);
    const quantity = interaction.options.getInteger('quantity') || 1;
    const giftMessage = interaction.options.getString('message');

    // Check if trying to gift to self
    if (recipient.id === interaction.user.id) {
      const container = new MegamiContainer([
        new TextDisplayBuilder().setContent('❌ **Cannot Gift to Yourself**'),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent('You cannot gift items to yourself.'),
        new TextDisplayBuilder().setContent('Choose a different recipient.'),
      ]);

      return await interaction.editReply({
        components: [container],
        flags: MessageFlags.IsComponentsV2,
      });
    }

    // Check if recipient is registered
    const recipientUser = await interaction.client.database.users.get(recipient.id);
    if (!recipientUser) {
      const container = new MegamiContainer([
        new TextDisplayBuilder().setContent('❌ **Recipient Not Registered**'),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent(`${recipient.username} is not registered in the system.`),
        new TextDisplayBuilder().setContent('They need to use the bot first before receiving gifts.'),
      ]);

      return await interaction.editReply({
        components: [container],
        flags: MessageFlags.IsComponentsV2,
      });
    }

    try {
      // Find the item
      const item = await interaction.client.database.items.getByName(itemName);
      if (!item) {
        const container = new MegamiContainer([
          new TextDisplayBuilder().setContent('❌ **Item Not Found**'),
          new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
          new TextDisplayBuilder().setContent(`No item found with the name "${itemName}".`),
          new TextDisplayBuilder().setContent('Please check the spelling and try again.'),
        ]);

        return await interaction.editReply({
          components: [container],
          flags: MessageFlags.IsComponentsV2,
        });
      }

      // Check if item is tradeable
      if (!item.tradeable) {
        const container = new MegamiContainer([
          new TextDisplayBuilder().setContent('❌ **Item Not Tradeable**'),
          new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
          new TextDisplayBuilder().setContent(`"${item.name}" cannot be traded or gifted.`),
          new TextDisplayBuilder().setContent('Only tradeable items can be gifted to other users.'),
        ]);

        return await interaction.editReply({
          components: [container],
          flags: MessageFlags.IsComponentsV2,
        });
      }

      // Check if user has enough of the item
      const userQuantity = await interaction.client.database.holdings.getUserItemQuantity(user.id, item.id);
      if (userQuantity < quantity) {
        const container = new MegamiContainer([
          new TextDisplayBuilder().setContent('❌ **Insufficient Items**'),
          new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
          new TextDisplayBuilder().setContent(`You need ${quantity}x "${item.name}" but only have ${userQuantity}.`),
          new TextDisplayBuilder().setContent('Check your inventory and try again.'),
        ]);

        return await interaction.editReply({
          components: [container],
          flags: MessageFlags.IsComponentsV2,
        });
      }

      // Check if any of the user's holdings are locked
      const userHoldings = await interaction.client.database.holdings.getUserItem(user.id, item.id);
      const availableQuantity = userHoldings
        .filter((holding) => !holding.isLocked)
        .reduce((sum, holding) => sum + holding.quantity, 0);

      if (availableQuantity < quantity) {
        const container = new MegamiContainer([
          new TextDisplayBuilder().setContent('❌ **Items Locked**'),
          new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
          new TextDisplayBuilder().setContent(
            `You have ${userQuantity}x "${item.name}" but ${userQuantity - availableQuantity} are locked.`
          ),
          new TextDisplayBuilder().setContent('Unlock items before gifting them.'),
        ]);

        return await interaction.editReply({
          components: [container],
          flags: MessageFlags.IsComponentsV2,
        });
      }

      // Perform the transfer
      const success = await interaction.client.database.holdings.transferItem({
        fromUserId: user.id,
        toUserId: recipient.id,
        itemId: item.id,
        quantity,
      });

      if (!success) {
        const container = new MegamiContainer([
          new TextDisplayBuilder().setContent('❌ **Gift Failed**'),
          new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
          new TextDisplayBuilder().setContent('An error occurred while transferring the item.'),
          new TextDisplayBuilder().setContent('Please try again.'),
        ]);

        return await interaction.editReply({
          components: [container],
          flags: MessageFlags.IsComponentsV2,
        });
      }

      // Show success message
      const rarityEmoji = getRarityEmoji(item.rarity);
      const typeEmoji = getTypeEmoji(item.type);

      const components = [
        new TextDisplayBuilder().setContent('🎁 **Gift Sent Successfully**'),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent(`${rarityEmoji} ${item.icon || typeEmoji} **${item.name}** x${quantity}`),
        new TextDisplayBuilder().setContent(`**Recipient:** ${recipient.username}`),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
      ];

      if (giftMessage) {
        components.push(
          new TextDisplayBuilder().setContent('**Your Message:**'),
          new TextDisplayBuilder().setContent(`"${giftMessage}"`)
        );
      }

      const remainingQuantity = userQuantity - quantity;
      if (remainingQuantity > 0) {
        components.push(
          new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
          new TextDisplayBuilder().setContent(`**You have ${remainingQuantity}x ${item.name} remaining**`)
        );
      }

      components.push(
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent('The recipient has been notified of your gift!')
      );

      const container = new MegamiContainer(components);
      await interaction.editReply({
        components: [container],
        flags: MessageFlags.IsComponentsV2,
      });

      // TODO: Send notification to recipient (could be implemented with a notification system)
      // For now, we'll just log it
      interaction.client.logger.info(
        `Gift sent: ${interaction.user.username} -> ${recipient.username}: ${quantity}x ${item.name}`
      );
    } catch (error) {
      const container = new MegamiContainer([
        new TextDisplayBuilder().setContent('❌ **Error Sending Gift**'),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent('An unexpected error occurred while sending the gift.'),
        new TextDisplayBuilder().setContent('Please try again or contact support.'),
      ]);

      await interaction.editReply({
        components: [container],
        flags: MessageFlags.IsComponentsV2,
      });

      interaction.client.logger.error('Error sending gift:', error);
    }
  },
}));

function getTypeEmoji(type: string): string {
  const emojis: Record<string, string> = {
    EQUIPMENT: '⚔️',
    CONSUMABLE: '🧪',
    MATERIAL: '🔨',
    COLLECTIBLE: '💎',
    CURRENCY: '💰',
    GIFT: '🎁',
    KEY: '🗝️',
    MISC: '📦',
  };
  return emojis[type] || '📦';
}

function getRarityEmoji(rarity: string): string {
  const emojis: Record<string, string> = {
    COMMON: '⚪',
    UNCOMMON: '🟢',
    RARE: '🔵',
    EPIC: '🟣',
    LEGENDARY: '🟡',
    MYTHIC: '🔴',
  };
  return emojis[rarity] || '⚪';
}
