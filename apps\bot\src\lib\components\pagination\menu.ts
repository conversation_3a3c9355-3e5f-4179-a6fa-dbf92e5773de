/* biome-ignore-all lint/suspicious/useAwait: We need this to be awaited */
import {
  ActionRowBuilder,
  ComponentType,
  type MessageComponentType,
  StringSelectMenuBuilder,
  type StringSelectMenuInteraction,
} from 'discord.js';
import type { PaginationPage, SelectPaginationOptions } from '../pagination';
import { BasePaginator } from './base';
import { Page } from './page';

export class SelectPaginator extends BasePaginator {
  private placeholder: string;
  private maxValues: number;
  private minValues: number;
  private selectId: string;

  private containers: PaginationPage[];

  constructor(options: SelectPaginationOptions & { containers: PaginationPage[] }) {
    super(options);

    this.placeholder = options.placeholder || 'Select a page...';
    this.maxValues = options.maxValues || 1;
    this.minValues = options.minValues || 1;
    this.containers = options.containers;

    // Create unique custom ID
    this.selectId = `sel_pagination_${this.interaction.id}_${Date.now()}`;
  }

  getComponentType(): MessageComponentType {
    return ComponentType.StringSelect;
  }

  createComponents(disabled = false): ActionRowBuilder<StringSelectMenuBuilder>[] {
    if (this.pages.total === 1) {
      return [];
    }

    const options = this.containers.map((page, index) => {
      const isCurrentPage = index === this.pages.current;

      return {
        label: page.label || `Page ${index + 1}`,
        description: page.description || `Go to page ${index + 1}`,
        value: index.toString(),
        emoji: page.emoji,
        default: isCurrentPage,
      };
    });

    const menu = new ActionRowBuilder<StringSelectMenuBuilder>().addComponents(
      new StringSelectMenuBuilder()
        .setCustomId(this.selectId)
        .setPlaceholder(this.placeholder)
        .setMinValues(this.minValues)
        .setMaxValues(this.maxValues)
        .setDisabled(disabled)
        .addOptions(options)
    );

    return [menu];
  }

  protected override filterInteraction(interaction: StringSelectMenuInteraction): boolean {
    if (interaction.customId !== this.selectId) {
      return false;
    }

    return super.filterInteraction(interaction);
  }

  protected async handleInteraction(interaction: StringSelectMenuInteraction): Promise<void> {
    if (this.isDestroyed) return;

    const selectedValue = interaction.values[0];
    const newPage = Number.parseInt(selectedValue as string, 10);

    if (Number.isNaN(newPage) || newPage < 0 || newPage >= this.pages.total) {
      return;
    }

    if (newPage !== this.pages.current) {
      this.pages.current = newPage;
      this.emit('change', this.pages.current, new Page(this, { interaction }), interaction);
    }
  }
}
