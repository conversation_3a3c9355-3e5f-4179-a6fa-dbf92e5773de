{"name": "@megami/bot", "version": "0.0.1", "main": "src/index.ts", "module": "src/index.ts", "types": "src/index.ts", "type": "module", "private": true, "scripts": {"cluster": "bun run src/cluster.ts", "typecheck": "tsc --noEmit"}, "dependencies": {"@megami/canvas": "link:@megami/canvas", "@megami/config": "link:@megami/config", "@megami/database": "link:@megami/database", "@megami/env": "link:@megami/env", "@megami/search": "link:@megami/search", "@napi-rs/canvas": "^0.1.74", "archiver": "^7.0.1", "cron": "^4.3.2", "discord-hybrid-sharding": "^2.2.6", "discord.js": "^14.21.0", "sharp": "^0.34.3", "winston": "^3.17.0", "zod": "^4.0.14"}, "devDependencies": {"@types/bun": "^1.2.19", "typed-emitter": "^2.1.0"}}