import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Entropic Field',
  name: 'Entropic Field',
  element: 'Vortex',
  manaCost: 45,
  cooldown: 6,
  description: 'Creates a field of entropy that damages and applies a random status effect to any enemy that enters it.',
  execute: (caster, target, formulas) => {
    // This would require a system for creating and managing persistent area effects with random status effects.
    return {
      log: `${caster.name} creates an Entropic Field.`,
    };
  },
});
