export interface VotingReward {
  rifts: number;
  shards: number;
}

export interface VotingStreakTier {
  minStreak: number;
  maxStreak: number;
  bonusPercentage: number;
  description: string;
}

// Base reward per vote (every 12 hours)
export const baseVotingReward: VotingReward = {
  rifts: 20,
  shards: 200,
};

// Streak bonus tiers with percentage multipliers
export const votingStreakTiers: VotingStreakTier[] = [
  {
    minStreak: 1,
    maxStreak: 1,
    bonusPercentage: 0,
    description: 'No streak bonus',
  },
  {
    minStreak: 2,
    maxStreak: 3,
    bonusPercentage: 10,
    description: '2-3 day streak',
  },
  {
    minStreak: 4,
    maxStreak: 6,
    bonusPercentage: 25,
    description: '4-6 day streak',
  },
  {
    minStreak: 7,
    maxStreak: 13,
    bonusPercentage: 50,
    description: '7-13 day streak',
  },
  {
    minStreak: 14,
    maxStreak: 999,
    bonusPercentage: 75,
    description: '14+ day streak (max bonus)',
  },
];

export const votingConfig = {
  cooldownHours: 12, // Can vote every 12 hours
  streakResetHours: 24, // Streak resets if no vote within 24 hours
  maxBonusPercentage: 75, // Cap bonus at 75%
} as const;

/**
 * Calculate the streak bonus percentage for a given streak length
 */
export function getStreakBonusPercentage(streak: number): number {
  const tier = votingStreakTiers.find(
    (tier) => streak >= tier.minStreak && streak <= tier.maxStreak
  );
  return tier?.bonusPercentage || 0;
}

/**
 * Calculate the total reward with streak bonus applied
 */
export function calculateVotingReward(streak: number): {
  baseReward: VotingReward;
  bonusPercentage: number;
  bonusAmount: VotingReward;
  totalReward: VotingReward;
  streakDescription: string;
} {
  const bonusPercentage = getStreakBonusPercentage(streak);
  const tier = votingStreakTiers.find(
    (tier) => streak >= tier.minStreak && streak <= tier.maxStreak
  );

  const bonusAmount = {
    rifts: Math.floor((baseVotingReward.rifts * bonusPercentage) / 100),
    shards: Math.floor((baseVotingReward.shards * bonusPercentage) / 100),
  };

  const totalReward = {
    rifts: baseVotingReward.rifts + bonusAmount.rifts,
    shards: baseVotingReward.shards + bonusAmount.shards,
  };

  return {
    baseReward: baseVotingReward,
    bonusPercentage,
    bonusAmount,
    totalReward,
    streakDescription: tier?.description || 'Unknown streak',
  };
}

/**
 * Calculate new voting streak based on last vote timestamp
 */
export function calculateNewVotingStreak(
  lastVoteTimestamp: number,
  currentStreak: number
): number {
  const now = Date.now();
  const timeSinceLastVote = now - lastVoteTimestamp;
  const streakResetMs = votingConfig.streakResetHours * 60 * 60 * 1000;

  // If first vote or within streak window, continue/start streak
  if (lastVoteTimestamp === 0 || timeSinceLastVote <= streakResetMs) {
    return currentStreak + 1;
  }

  // Streak broken, start over
  return 1;
}

/**
 * Check if user can vote based on last vote timestamp
 */
export function canVote(lastVoteTimestamp: number): {
  canVote: boolean;
  reason?: string;
  nextVoteTime?: number;
} {
  const now = Date.now();
  const cooldownMs = votingConfig.cooldownHours * 60 * 60 * 1000;
  const nextVoteTime = lastVoteTimestamp + cooldownMs;

  if (now < nextVoteTime) {
    const remainingMs = nextVoteTime - now;
    const remainingHours = Math.ceil(remainingMs / (60 * 60 * 1000));
    
    return {
      canVote: false,
      reason: `You can vote again in ${remainingHours} hour${remainingHours !== 1 ? 's' : ''}`,
      nextVoteTime,
    };
  }

  return { canVote: true };
}

/**
 * Format voting streak message for display
 */
export function formatVotingMessage(
  streak: number,
  rewardData: ReturnType<typeof calculateVotingReward>
): string {
  const { baseReward, bonusPercentage, totalReward, streakDescription } = rewardData;

  let message = '🗳️ **Thanks for voting!**\n\n';
  message += '**You received:**\n';
  message += `+${baseReward.rifts} Rifts ⚡\n`;
  message += `+${baseReward.shards} Shards 💎\n\n`;

  if (bonusPercentage > 0) {
    message += `🎯 **Streak Bonus: +${bonusPercentage}%** (${streakDescription}!)\n`;
    message += `**Total:** +${totalReward.rifts} Rifts / +${totalReward.shards} Shards\n`;
    message += 'Keep voting every 12h to reach max bonuses! 🔥';
  } else {
    message += '🎯 **Start your voting streak!**\n';
    message += 'Vote again within 24h to build your streak bonus!';
  }

  return message;
}

export default {
  baseVotingReward,
  votingStreakTiers,
  votingConfig,
  getStreakBonusPercentage,
  calculateVotingReward,
  calculateNewVotingStreak,
  canVote,
  formatVotingMessage,
};
