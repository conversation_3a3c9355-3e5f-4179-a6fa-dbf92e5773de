import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Volcano',
  name: 'Volcano',
  element: 'Sirius',
  manaCost: 70,
  cooldown: 9,
  description: 'The caster summons a volcano that erupts after a short delay, dealing massive damage to all enemies in a large area.',
  execute: (caster, target, formulas) => {
    // This would require a system for creating and managing persistent area effects.
    return {
      log: `${caster.name} summons a Volcano.`,
    };
  },
});
