import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Arcane Annihilation',
  name: 'Arcane Annihilation',
  element: 'Arcane',
  manaCost: 100,
  cooldown: 12,
  description: 'An ultimate arcane spell that attempts to annihilate the target, dealing immense damage.',
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const finalDamage = Math.round(damage * 5);
    return {
      damage: finalDamage,
      isCritical,
      log: `${caster.name} attempts to annihilate ${target.name} with Arcane Annihilation for ${finalDamage} damage${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
