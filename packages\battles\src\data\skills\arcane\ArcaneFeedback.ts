import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Arcane Feedback',
  name: 'Arcane Feedback',
  element: 'Arcane',
  manaCost: 20,
  cooldown: 3,
  description: 'Curses the target with arcane feedback, causing them to take damage whenever they spend mana.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Mana Burn Effect',
        name: 'Mana Burn Effect',
        duration: 3,
        sourceId: caster.id,
      },
      log: `${caster.name} curses ${target.name} with Arcane Feedback.`,
    };
  },
});
