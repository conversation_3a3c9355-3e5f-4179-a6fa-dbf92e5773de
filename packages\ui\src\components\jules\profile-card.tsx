'use client';

import { cn } from '@megami/ui/lib/utils';
import { motion } from 'framer-motion';
import * as React from 'react';
import { GlowEffect } from '../registry/glow-effect';
import { Avatar } from './avatar';

export interface ProfileCardProps extends React.HTMLAttributes<HTMLDivElement> {
  avatarSrc: string;
  avatarAlt: string;
  name: string;
  description: string;
}

const ProfileCard = React.forwardRef<HTMLDivElement, ProfileCardProps>(
  ({ className, avatarSrc, avatarAlt, name, description, ...props }, ref) => {
    const cardVariants = {
      hidden: { opacity: 0, y: 20 },
      visible: { opacity: 1, y: 0, transition: { duration: 0.5 } },
    };

    return (
      <motion.div
        animate="visible"
        className={cn('relative w-80 rounded-lg bg-black/80 p-6 text-white backdrop-blur-sm', className)}
        initial="hidden"
        ref={ref}
        variants={cardVariants}
        {...props}
      >
        <GlowEffect
          blur="medium"
          className="opacity-30"
          colors={['#0894FF', '#C959DD', '#FF2E54', '#FF9004']}
          mode="static"
        />
        <div className="relative z-10 flex flex-col items-center">
          <Avatar alt={avatarAlt} className="mb-4" size="lg" src={avatarSrc} />
          <h3 className="font-bold text-xl">{name}</h3>
          <p className="text-gray-400 text-sm">{description}</p>
        </div>
      </motion.div>
    );
  }
);
ProfileCard.displayName = 'ProfileCard';

export { ProfileCard };
