'use client';

import { cn } from '@megami/ui/lib/utils';
import { motion } from 'framer-motion';
import * as React from 'react';
import { Button } from './button';

export interface PricingCardProps extends React.HTMLAttributes<HTMLDivElement> {
  title: string;
  price: string;
  features: string[];
  buttonText: string;
  isFeatured?: boolean;
}

const PricingCard = React.forwardRef<HTMLDivElement, PricingCardProps>(
  ({ className, title, price, features, buttonText, isFeatured, ...props }, ref) => {
    return (
      <motion.div
        className={cn(
          'relative w-full rounded-lg border-2 p-6 text-center text-white',
          isFeatured ? 'border-purple-500' : 'border-gray-700',
          className
        )}
        ref={ref}
        whileHover={{ scale: 1.05, borderColor: '#a855f7' }}
        {...props}
      >
        {isFeatured && (
          <div className="-translate-y-1/2 -translate-x-1/2 absolute top-0 left-1/2 rounded-full bg-purple-500 px-3 py-1 font-semibold text-sm">
            Most Popular
          </div>
        )}
        <h3 className="font-bold text-2xl">{title}</h3>
        <p className="mt-4 font-bold text-5xl">{price}</p>
        <ul className="mt-6 space-y-2">
          {features.map((feature) => (
            <li className="text-gray-400" key={feature}>
              {feature}
            </li>
          ))}
        </ul>
        <Button className="mt-8 w-full" size="lg">
          {buttonText}
        </Button>
      </motion.div>
    );
  }
);
PricingCard.displayName = 'PricingCard';

export { PricingCard };
