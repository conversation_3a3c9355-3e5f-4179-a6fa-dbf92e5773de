import { getRarityEmoji } from '@megami/config/lib/rewards';
import { getObject, translate } from '@megami/locale';
import {
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle,
  ComponentType,
  MessageFlags,
  SeparatorBuilder,
  SeparatorSpacingSize,
  TextDisplayBuilder,
} from 'discord.js';
import { defineSubCommand } from '../../../../../handlers/command';
import { MegamiContainer } from '../../../../../helpers/containers';
import { defer } from '../../../../../helpers/defer';

export default defineSubCommand((builder) => ({
  builder: builder
    .setName(translate('commands.management.modules.skins.modules.pending.name'))
    .setNameLocalizations(getObject('commands.management.modules.skins.modules.pending.name'))
    .setDescription(translate('commands.management.modules.skins.modules.pending.description'))
    .setDescriptionLocalizations(getObject('commands.management.modules.skins.modules.pending.description')),
  config: {},
  execute: async (interaction) => {
    await defer(interaction, false);

    try {
      const pendingSkins = await interaction.client.database.skins.getPending();

      if (pendingSkins.length === 0) {
        const container = new MegamiContainer([
          new TextDisplayBuilder().setContent('✨ **No Pending Submissions**'),
          new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
          new TextDisplayBuilder().setContent(
            'My collection is perfectly curated at the moment. No submissions await my discerning judgment.'
          ),
        ]);

        await interaction.editReply({
          components: [container],
          flags: MessageFlags.IsComponentsV2,
        });
        return;
      }

      // Paginate results (5 per page)
      const itemsPerPage = 5;
      let currentPage = 0;
      const totalPages = Math.ceil(pendingSkins.length / itemsPerPage);

      const generateEmbed = (page: number) => {
        const start = page * itemsPerPage;
        const end = start + itemsPerPage;
        const pageSkins = pendingSkins.slice(start, end);

        const components = [
          new TextDisplayBuilder().setContent(`🔄 **Pending Skin Submissions** (${pendingSkins.length} total)`),
          new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        ];

        for (const skin of pageSkins) {
          const rarityEmoji = getRarityEmoji(skin.rarity);
          const submittedDate = new Date(skin.createdAt).toLocaleDateString();

          components.push(
            new TextDisplayBuilder().setContent(
              `**${skin.name}** ${rarityEmoji}\n` +
                `Character: ${skin.character?.name || 'Unknown'}\n` +
                `Author: <@${skin.authorId}>\n` +
                `Submitted: ${submittedDate}\n` +
                `ID: \`${skin.id}\``
            ),
            new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small)
          );
        }

        // Add page info
        if (totalPages > 1) {
          components.push(new TextDisplayBuilder().setContent(`Page ${page + 1} of ${totalPages}`));
        }

        return new MegamiContainer(components);
      };

      const generateButtons = (page: number) => {
        const row = new ActionRowBuilder<ButtonBuilder>();

        if (page > 0) {
          row.addComponents(
            new ButtonBuilder().setCustomId('prev').setLabel('Previous').setStyle(ButtonStyle.Secondary).setEmoji('⬅️')
          );
        }

        if (page < totalPages - 1) {
          row.addComponents(
            new ButtonBuilder().setCustomId('next').setLabel('Next').setStyle(ButtonStyle.Secondary).setEmoji('➡️')
          );
        }

        row.addComponents(
          new ButtonBuilder().setCustomId('refresh').setLabel('Refresh').setStyle(ButtonStyle.Primary).setEmoji('🔄')
        );

        return row.components.length > 0 ? [row] : [];
      };

      const container = generateEmbed(currentPage);
      const buttons = generateButtons(currentPage);

      const response = await interaction.editReply({
        components: [container, ...buttons],
        flags: MessageFlags.IsComponentsV2,
      });

      // Handle pagination if there are multiple pages
      if (totalPages > 1 || buttons.length > 0) {
        const collector = response.createMessageComponentCollector({
          componentType: ComponentType.Button,
          time: 300_000, // 5 minutes
        });

        collector.on('collect', async (buttonInteraction) => {
          if (buttonInteraction.user.id !== interaction.user.id) {
            await buttonInteraction.reply({
              content: 'Only the command user can navigate pages.',
              ephemeral: true,
            });
            return;
          }

          switch (buttonInteraction.customId) {
            case 'prev':
              currentPage = Math.max(0, currentPage - 1);
              break;
            case 'next':
              currentPage = Math.min(totalPages - 1, currentPage + 1);
              break;
            case 'refresh': {
              // Refresh data
              const refreshedSkins = await interaction.client.database.skins.getPending();
              if (refreshedSkins.length !== pendingSkins.length) {
                await buttonInteraction.reply({
                  content: `Refreshed! Found ${refreshedSkins.length} pending submissions.`,
                  ephemeral: true,
                });
                // Update the original data
                pendingSkins.length = 0;
                pendingSkins.push(...refreshedSkins);
              } else {
                await buttonInteraction.reply({
                  content: 'No changes detected.',
                  ephemeral: true,
                });
              }
              break;
            }
          }

          const newContainer = generateEmbed(currentPage);
          const newButtons = generateButtons(currentPage);

          await buttonInteraction.update({
            components: [newContainer, ...newButtons],
            flags: MessageFlags.IsComponentsV2,
          });
        });

        collector.on('end', async () => {
          try {
            await interaction.editReply({
              components: [container],
              flags: MessageFlags.IsComponentsV2,
            });
          } catch {
            // Ignore errors when removing buttons
          }
        });
      }
    } catch (error) {
      const container = new MegamiContainer([
        new TextDisplayBuilder().setContent('❌ **Error Loading Pending Submissions**'),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent(
          'I encountered an issue while gathering pending submissions. Please try again later.'
        ),
      ]);

      await interaction.editReply({
        components: [container],
        flags: MessageFlags.IsComponentsV2,
      });

      interaction.client.logger.error('Error loading pending skins:', error);
    }
  },
}));
