import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Instability',
  name: 'Instability',
  element: 'Vortex',
  manaCost: 25,
  cooldown: 4,
  description: 'The target becomes unstable, causing them to have a chance to damage themselves whenever they use a skill.',
  execute: (caster, target, formulas) => {
    // This would require a status effect system to handle the instability.
    return {
      log: `${caster.name} makes ${target.name} unstable.`,
    };
  },
});
