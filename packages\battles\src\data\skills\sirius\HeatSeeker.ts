import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Heat Seeker',
  name: 'Heat Seeker',
  element: 'Sirius',
  manaCost: 25,
  cooldown: 4,
  description: 'Launches a heat-seeking missile that tracks the target and deals damage.',
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const finalDamage = Math.round(damage * 1.3);
    return {
      damage: finalDamage,
      isCritical,
      log: `${caster.name} launches a Heat Seeker at ${target.name}, dealing ${finalDamage} damage${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
