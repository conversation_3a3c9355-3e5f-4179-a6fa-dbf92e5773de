import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Energy Vortex',
  name: 'Energy Vortex',
  element: 'Vortex',
  manaCost: 35,
  cooldown: 5,
  description: 'Creates a vortex of energy that damages and drains mana from all enemies in an area.',
  execute: (caster, target, formulas) => {
    // This would require a more complex targeting system and a way to drain mana.
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const finalDamage = Math.round(damage * 1.2);
    return {
      damage: finalDamage,
      isCritical,
      log: `${caster.name} creates an Energy Vortex, damaging and draining mana from all nearby enemies for ${finalDamage} damage${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
