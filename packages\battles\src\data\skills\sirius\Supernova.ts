import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Supernova',
  name: 'Supernova',
  element: 'Sirius',
  manaCost: 80,
  cooldown: 10,
  description: 'The caster explodes in a supernova, dealing massive damage to all enemies but also sacrificing a large portion of their health.',
  execute: (caster, target, formulas) => {
    // This would require a more complex targeting system and a way to manage health costs.
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const finalDamage = Math.round(damage * 3);
    return {
      damage: finalDamage,
      isCritical,
      log: `${caster.name} explodes in a Supernova, dealing ${finalDamage} damage to all enemies${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
