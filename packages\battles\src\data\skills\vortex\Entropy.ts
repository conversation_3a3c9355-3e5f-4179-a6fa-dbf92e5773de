import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Entropy',
  name: 'Entropy',
  element: 'Vortex',
  manaCost: 35,
  cooldown: 5,
  description: 'The target is afflicted with entropy, causing their buffs to expire faster and their debuffs to last longer.',
  execute: (caster, target, formulas) => {
    // This would require a status effect system that can modify the duration of other status effects.
    return {
      log: `${caster.name} afflicts ${target.name} with Entropy.`,
    };
  },
});
