import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Ember',
  name: 'Ember',
  element: 'Sirius',
  manaCost: 5,
  cooldown: 0,
  description: 'A small ember that deals minor fire damage.',
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const finalDamage = Math.round(damage * 0.5);
    return {
      damage: finalDamage,
      isCritical,
      log: `${caster.name} throws an Ember at ${target.name} for ${finalDamage} damage${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
