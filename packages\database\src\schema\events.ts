import { jsonb, pgTable, text, timestamp, uniqueIndex, uuid } from 'drizzle-orm/pg-core';

export interface EventDetails {
  description: string;
  location: string;
}

export interface Event {
  id: string;
  name: string;
  startDate: Date;
  endDate: Date;
  details: EventDetails;
}

export const events = pgTable(
  'events',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    name: text('name').notNull(),
    startDate: timestamp('start_date').notNull(),
    endDate: timestamp('end_date').notNull(),
    details: jsonb('details').$type<EventDetails>().notNull(),
    createdAt: timestamp('created_at').defaultNow().notNull(),
  },
  (table) => [uniqueIndex('event_name').on(table.name)]
);
