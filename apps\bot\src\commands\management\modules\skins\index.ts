import { getObject, translate } from '@megami/locale';
import type { SlashCommandSubcommandBuilder } from 'discord.js';
import { defineSubCommandGroup } from '../../../../handlers/command';
import approve from './modules/approve';
import pending from './modules/pending';
import stats from './modules/stats';

export default defineSubCommandGroup((builder) => ({
  builder: builder
    .setName(translate('commands.management.modules.skins.name'))
    .setNameLocalizations(getObject('commands.management.modules.skins.name'))
    .setDescription(translate('commands.management.modules.skins.description'))
    .setDescriptionLocalizations(getObject('commands.management.modules.skins.description'))
    .addSubcommand(stats.builder as SlashCommandSubcommandBuilder)
    .addSubcommand(pending.builder as SlashCommandSubcommandBuilder)
    .addSubcommand(approve.builder as SlashCommandSubcommandBuilder),
  
  config: {},
  
  async execute(interaction) {
    const subcommand = interaction.options.getSubcommand();

    switch (subcommand) {
      case translate('commands.management.modules.skins.modules.stats.name'): {
        await stats.execute(interaction);
        break;
      }

      case translate('commands.management.modules.skins.modules.pending.name'): {
        await pending.execute(interaction);
        break;
      }

      case translate('commands.management.modules.skins.modules.approve.name'): {
        await approve.execute(interaction);
        break;
      }

      default: {
        await interaction.reply({
          content: 'Unknown subcommand.',
          ephemeral: true,
        });
      }
    }
  },
}));
