import { defineTask } from '../../structure/scheduler';

export default defineTask({
  id: 'search:index:characters',
  name: 'Update Characters Search Index',
  expression: '0 0 2 * * *', // Daily at 2:00 AM UTC
  enabled: true,
  execute: async (client) => {
    try {
      client.logger.info('Starting characters search index update...');

      const characters = await client.database.characters.getAll();
      const existing = Object.keys(client.search.characters.instance.toJSON().documentIds);

      const indexed = new Set(existing);
      const missing = characters.filter((character) => !indexed.has(character.id));

      if (missing.length > 0) {
        await client.search.characters.add(...missing);
        client.logger.info(`Added ${missing.length} characters to search index`);
      } else {
        client.logger.info('No new characters to add to search index');
      }

      client.logger.info('Characters search index update completed');
    } catch (error) {
      client.logger.error('Failed to update characters search index:', error);
      throw error;
    }
  },
});
