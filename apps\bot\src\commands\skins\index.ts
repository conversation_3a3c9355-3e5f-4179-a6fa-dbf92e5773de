import { getObject, translate } from '@megami/locale';
import type { SlashCommandSubcommandBuilder } from 'discord.js';
import { defineCommand } from '../../handlers/command';
import upload from './modules/upload';

export default defineCommand((builder) => ({
  builder: builder
    .setName(translate('commands.skins.name'))
    .setNameLocalizations(getObject('commands.skins.name'))
    .setDescription(translate('commands.skins.description'))
    .setDescriptionLocalizations(getObject('commands.skins.description'))
    .addSubcommand(upload.builder as SlashCommandSubcommandBuilder),

  config: {},

  async execute(interaction) {
    const subcommand = interaction.options.getSubcommand();

    switch (subcommand) {
      case translate('commands.skins.modules.upload.name'): {
        await upload.execute(interaction);
        break;
      }

      default: {
        await interaction.reply({
          content: 'Unknown subcommand.',
          ephemeral: true,
        });
      }
    }
  },
}));
