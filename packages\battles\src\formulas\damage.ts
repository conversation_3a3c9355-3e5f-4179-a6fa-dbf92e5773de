import { percentage } from '@megami/utils/lib/random.js';
import { type Element, elements } from '../data/elements';
import type { BattleEntity } from '../types';

function getElementAdvantageMultiplier(attackerElement: Element['name'], defenderElement: Element['name']): number {
  const attacker = elements.find((e) => e.name === attackerElement);
  if (!attacker) return 1;
  if (attacker.advantage === defenderElement) return 1.25;
  if (attacker.disadvantage === defenderElement) return 0.75;
  return 1;
}

export function calculateDamage(
  attacker: BattleEntity,
  defender: BattleEntity
): { damage: number; isCritical: boolean } {
  const baseDamage = attacker.stats.attack;
  const defense = defender.stats.defense;
  let finalDamage = baseDamage * (100 / (100 + defense));
  const elementMultiplier = getElementAdvantageMultiplier(attacker.element, defender.element);
  finalDamage *= elementMultiplier;
  const critChance = Math.max(5, Math.min(75, 5 + attacker.stats.luck / 10));
  const isCritical = percentage(critChance / 100);
  if (isCritical) {
    finalDamage *= 1.5;
  }
  return {
    damage: Math.round(finalDamage),
    isCritical,
  };
}
