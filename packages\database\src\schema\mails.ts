import { boolean, index, integer, pgTable, serial, text, timestamp, uniqueIndex } from 'drizzle-orm/pg-core';
import { notifications } from './notifications';

export const mails = pgTable(
  'mails',
  {
    id: serial('id').primaryKey(),
    userId: text('user_id').notNull(),
    notificationId: integer('notification_id')
      .notNull()
      .references(() => notifications.id, { onDelete: 'cascade' }),
    isRead: boolean('is_read').notNull().default(false),
    isNotified: boolean('is_notified').notNull().default(false),
    notifiedAt: timestamp('notified_at'),
    readAt: timestamp('read_at'),
    createdAt: timestamp('created_at').defaultNow().notNull(),
  },
  (table) => [
    uniqueIndex('user_notification_idx').on(table.userId, table.notificationId),
    index('user_notifications_user_id_idx').on(table.userId),
    index('notification_id_idx').on(table.notificationId),
    index('is_read_idx').on(table.isRead),
    index('is_notified_idx').on(table.isNotified),
  ]
);
