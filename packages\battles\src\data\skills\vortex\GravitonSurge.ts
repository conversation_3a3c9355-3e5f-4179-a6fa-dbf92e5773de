import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Graviton Surge',
  name: 'Graviton Surge',
  element: 'Vortex',
  manaCost: 45,
  cooldown: 6,
  description: 'Launches a graviton surge that pulls all enemies in an area together and damages them.',
  execute: (caster, target, formulas) => {
    // This would require a more complex targeting system with a pull mechanic.
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const finalDamage = Math.round(damage * 1.5);
    return {
      damage: finalDamage,
      isCritical,
      log: `${caster.name} launches a Graviton Surge, pulling all nearby enemies together and dealing ${finalDamage} damage${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
