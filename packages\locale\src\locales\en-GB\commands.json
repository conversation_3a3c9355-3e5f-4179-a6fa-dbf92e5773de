{"about": {"name": "about", "description": "Curious, are we? Very well... allow me to tell you a little about myself."}, "daily": {"name": "daily", "description": "How devoted. Here's your daily reward, don't say I never spoil you."}, "weekly": {"name": "weekly", "description": "How persistent. Your weekly reward awaits, dear one."}, "monthly": {"name": "monthly", "description": "You've earned something truly special this time."}, "register": {"name": "register", "description": "Let us begin your journey together, shall we?"}, "profile": {"name": "profile", "description": "View detailed profile information for yourself or other users.", "modules": {"view": {"name": "view", "description": "View a user's profile with detailed statistics and information.", "options": {"user": {"name": "user", "description": "The user whose profile you want to view (leave empty for your own)"}}}}}, "management": {"name": "management", "description": "Commands for managing the bot.", "modules": {"disabled": {"name": "disabled", "description": "Disable a command for all users.", "options": {"command": {"name": "command", "description": "The command to disable"}, "module": {"name": "module", "description": "The module to disable"}}}, "disable": {"name": "disable", "description": "Cutting someone off? How deliciously ruthless. I do admire decisive action.", "options": {"command": {"name": "command", "description": "Which privilege shall we revoke today? Choose your target wisely."}}}, "items": {"name": "items", "description": "My personal collection needs... curation. Only the worthy may touch my treasures.", "modules": {"create": {"name": "create", "description": "Bringing something new into existence? How creative... I hope it's worthy of my standards.", "options": {"name": {"name": "name", "description": "What shall we call this new creation? Make it memorable, darling."}, "description": {"name": "description", "description": "Tell me its story. Every treasure needs a tale worth telling."}, "type": {"name": "type", "description": "What category does this belong to? I do like things properly organized."}, "rarity": {"name": "rarity", "description": "How precious is this little thing? <PERSON><PERSON> makes everything more... desirable."}}}, "delete": {"name": "delete", "description": "Removing something from my collection? It better not be one of my favorites.", "options": {"item": {"name": "item", "description": "Which item has fallen from grace? This is permanent, so choose carefully."}, "confirm": {"name": "confirm", "description": "Are you absolutely certain? I don't appreciate hasty decisions."}}}}}, "skins": {"name": "skins", "description": "Managing my artistic collection requires... discerning taste. Let me show you what needs my attention.", "modules": {"stats": {"name": "stats", "description": "View the current state of my art collection. Numbers tell such interesting stories, don't they?"}, "pending": {"name": "pending", "description": "See what submissions await my judgment. So many hopeful artists seeking my approval..."}, "approve": {"name": "approve", "description": "Grant my blessing to worthy artwork. Choose wisely - my standards are quite... particular."}}}, "schedules": {"name": "schedules", "description": "Time is precious, and I control every second. Let me show you how a real woman manages her schedule.", "modules": {"status": {"name": "status", "description": "Checking up on my operations? I suppose transparency has its... charms."}, "list": {"name": "list", "description": "Want to see my agenda? I'm quite the busy woman, but I might spare you a moment.", "options": {"filter": {"name": "filter", "description": "Looking for something specific? I do appreciate focused attention."}}}, "enable": {"name": "enable", "description": "Bringing something back to life? How... revitalizing. I do love a good resurrection.", "options": {"task": {"name": "task", "description": "Which sleeping beauty shall we awaken? Choose your moment carefully."}}}, "disable": {"name": "disable", "description": "Putting something to rest? Sometimes the kindest thing is to let go, don't you think?", "options": {"task": {"name": "task", "description": "What needs to be silenced? I'll handle it with my usual... finesse."}, "force": {"name": "force", "description": "Feeling forceful today? I do admire someone who takes charge when necessary."}}}, "execute": {"name": "execute", "description": "Ready for immediate action? I like someone who doesn't hesitate when the moment is right.", "options": {"task": {"name": "task", "description": "What shall we set in motion? Every action has consequences, darling."}, "force": {"name": "force", "description": "Pushing through resistance? Sometimes force is the only language they understand."}}}, "restart": {"name": "restart", "description": "Starting fresh? I do love a clean slate... it's so full of possibilities.", "options": {"force": {"name": "force", "description": "No mercy for the old ways? Ruthless. I respect that kind of determination."}}}}}}, "schedules": {"name": "schedules", "description": "Manage scheduled tasks and automation", "modules": {"status": {"name": "status", "description": "Show scheduler status and running tasks"}, "list": {"name": "list", "description": "List all scheduled tasks with detailed information", "options": {"filter": {"name": "filter", "description": "Filter tasks by status"}}}, "enable": {"name": "enable", "description": "Enable a scheduled task", "options": {"task": {"name": "task", "description": "The task ID to enable"}}}, "disable": {"name": "disable", "description": "Disable a scheduled task", "options": {"task": {"name": "task", "description": "The task ID to disable"}, "force": {"name": "force", "description": "Force disable even if task is currently running"}}}, "execute": {"name": "execute", "description": "Manually execute a scheduled task", "options": {"task": {"name": "task", "description": "The task ID to execute"}, "force": {"name": "force", "description": "Force execute even if task is disabled or already running"}}}, "restart": {"name": "restart", "description": "Restart the scheduler system", "options": {"force": {"name": "force", "description": "Force restart even if tasks are currently running"}}}}}}, "inventory": {"name": "inventory", "description": "My treasures aren't for just anyone, but perhaps you've earned a glimpse... if you ask nicely.", "modules": {"list": {"name": "list", "description": "Admiring my collection, are we? I suppose I can show you what I keep close to my heart.", "options": {"type": {"name": "type", "description": "What catches your eye, sweetie? Choose wisely - I don't show everything to everyone."}}}, "shop": {"name": "shop", "description": "Want something precious? Everything has a price, darling, and I don't come cheap.", "options": {"category": {"name": "category", "description": "What type of treasure are you seeking? I have... particular tastes."}}}, "use": {"name": "use", "description": "Ready to play with my toys? Handle with care - they're more dangerous than they look.", "options": {"item": {"name": "item", "description": "Which of my precious things will you dare to touch? <PERSON>ose carefully."}, "target": {"name": "target", "description": "Who's the lucky recipient of my attention? Or should I say... unlucky?"}}}, "gift": {"name": "gift", "description": "Feeling generous? How sweet. I do love it when someone tries to win my favor.", "options": {"item": {"name": "item", "description": "What precious offering will you present? It better be worth my time."}, "recipient": {"name": "recipient", "description": "Who's the fortunate soul receiving this token? I hope they appreciate the gesture."}, "message": {"name": "message", "description": "A little note to make it personal? How thoughtful... I'm almost impressed."}}}}}, "skins": {"name": "skins", "description": "Think you can create art worthy of my collection? Let's see what you've got, darling.", "modules": {"upload": {"name": "upload", "description": "Submit your artistic vision for my approval. I do hope it meets my... exacting standards.", "options": {"character": {"name": "character", "description": "Which of my precious characters deserves this artistic tribute? Cho<PERSON> wisely."}, "name": {"name": "name", "description": "What shall we call this masterpiece? Make it memorable, sweetie."}, "image": {"name": "image", "description": "Your artistic offering. It better be stunning - I don't accept mediocrity."}, "rarity": {"name": "rarity", "description": "How precious do you think this creation is? <PERSON><PERSON> reflects true artistry."}}}}}}