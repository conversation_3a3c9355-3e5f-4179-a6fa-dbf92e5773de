import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Prismatic Spray',
  name: 'Prismatic Spray',
  element: 'Arcane',
  manaCost: 45,
  cooldown: 6,
  description: 'A spray of colored light that has a random effect on the target.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Confusion',
        name: 'Prismatic Spray',
        duration: 1,
        sourceId: caster.id,
      },
      log: `${caster.name} unleashes a Prismatic Spray on ${target.name}, with unpredictable results.`,
    };
  },
});
