import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Dimensional Lock',
  name: 'Dimensional Lock',
  element: 'Arcane',
  manaCost: 35,
  cooldown: 5,
  description: 'Prevents the target from using any movement abilities.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Root',
        name: 'Root',
        duration: 2,
        sourceId: caster.id,
      },
      log: `${caster.name} locks ${target.name} in place with a Dimensional Lock.`,
    };
  },
});
