import { SeparatorBuilder, SeparatorSpacingSize, TextDisplayBuilder } from 'discord.js';
import { defineCommand } from '../handlers/command';
import { defer } from '../helpers/defer';
import { type PaginationPage, SelectPaginator } from '../lib/components/pagination';

interface CommandInfo {
  name: string;
  description: string;
  usage?: string;
  examples?: string[];
  subcommands?: SubcommandInfo[];
}

interface SubcommandInfo {
  name: string;
  description: string;
  usage?: string;
  examples?: string[];
}

export default defineCommand((builder) => ({
  builder: builder.setName('help').setDescription('Get help with bot commands and features'),

  config: {},

  execute: async (interaction) => {
    await defer(interaction, false);

    const categories = getCommandCategories();

    const pages: PaginationPage[] = categories.map((category) => ({
      label: category.name,
      description: category.description,
      emoji: category.emoji,
      components: createCategoryPage(category),
    }));

    const paginator = new SelectPaginator({
      interaction,
      containers: pages,
      placeholder: 'Select a command category...',
      timeout: 300_000,
      pages: { current: 0, total: pages.length },
    });

    await paginator.start(pages[0]!.components);
  },
}));

function getCommandCategories() {
  return [
    {
      name: 'General Commands',
      description: 'Basic commands for getting started',
      emoji: '🏠',
      commands: [
        {
          name: 'register',
          description: 'Register your account to start playing',
          usage: '/register',
          examples: ['/register'],
        },
        {
          name: 'daily',
          description: 'Claim your daily rewards with automatic weekly and monthly bonuses',
          usage: '/daily',
          examples: ['/daily'],
        },
        {
          name: 'vote',
          description: 'Vote for the bot and claim voting rewards with streak bonuses',
          usage: '/vote',
          examples: ['/vote'],
        },
        {
          name: 'about',
          description: 'Learn about the bot and its features',
          usage: '/about',
          examples: ['/about'],
        },
        {
          name: 'shop',
          description: 'Browse and purchase drops from the Rift Shop',
          usage: '/shop <subcommand>',
          subcommands: [
            {
              name: 'view',
              description: 'Browse available drops with detailed information',
              usage: '/shop view',
              examples: ['/shop view'],
            },
            {
              name: 'buy',
              description: 'Purchase a specific drop type',
              usage: '/shop buy <type>',
              examples: ['/shop buy type:basic', '/shop buy type:celestial'],
            },
          ],
        },
      ],
    },
    {
      name: 'Profile & Stats',
      description: 'View and manage your profile',
      emoji: '👤',
      commands: [
        {
          name: 'profile',
          description: 'View detailed profile information',
          usage: '/profile <subcommand>',
          subcommands: [
            {
              name: 'view',
              description: "View your or another user's profile",
              usage: '/profile view [user]',
              examples: ['/profile view', '/profile view @username'],
            },
            {
              name: 'effects',
              description: 'View your active effects and buffs',
              usage: '/profile effects',
              examples: ['/profile effects'],
            },
          ],
        },
      ],
    },
    {
      name: 'Inventory & Items',
      description: 'Manage your items and inventory',
      emoji: '🎒',
      commands: [
        {
          name: 'inventory',
          description: 'Manage your inventory and items',
          usage: '/inventory <subcommand>',
          subcommands: [
            {
              name: 'list',
              description: 'View your inventory items',
              usage: '/inventory list [type] [rarity]',
              examples: ['/inventory list', '/inventory list type:CONSUMABLE'],
            },
            {
              name: 'use',
              description: 'Use consumable items from your inventory',
              usage: '/inventory use <item> [quantity]',
              examples: [
                '/inventory use item:"Rift Amplifier Potion"',
                '/inventory use item:"Minor Rift Essence" quantity:3',
              ],
            },

            {
              name: 'gift',
              description: 'Gift items to other users',
              usage: '/inventory gift <user> <item> [quantity]',
              examples: ['/inventory gift user:@friend item:"Experience Orb" quantity:5'],
            },
          ],
        },
      ],
    },
    {
      name: 'Characters & Series',
      description: 'Explore characters and series',
      emoji: '🎭',
      commands: [
        {
          name: 'character',
          description: 'Search and view character information',
          usage: '/character <subcommand>',
          subcommands: [
            {
              name: 'search',
              description: 'Search for characters by name or series',
              usage: '/character search <query>',
              examples: ['/character search query:"Naruto"', '/character search query:"One Piece"'],
            },
          ],
        },
        {
          name: 'series',
          description: 'Search and view series information',
          usage: '/series <subcommand>',
          subcommands: [
            {
              name: 'search',
              description: 'Search for series by name or genre',
              usage: '/series search <query>',
              examples: ['/series search query:"Attack on Titan"', '/series search query:"Demon Slayer"'],
            },
          ],
        },
      ],
    },
    {
      name: 'Skins & Customization',
      description: 'Manage your skins and customization',
      emoji: '🎨',
      commands: [
        {
          name: 'skins',
          description: 'Manage your character skins',
          usage: '/skins <subcommand>',
          subcommands: [
            {
              name: 'list',
              description: 'View your available skins',
              usage: '/skins list [character]',
              examples: ['/skins list', '/skins list character:"Naruto"'],
            },
            {
              name: 'equip',
              description: 'Equip a skin for a character',
              usage: '/skins equip <character> <skin>',
              examples: ['/skins equip character:"Naruto" skin:"Sage Mode"'],
            },
          ],
        },
      ],
    },
    {
      name: 'Management',
      description: 'Administrative commands (Manager only)',
      emoji: '⚙️',
      commands: [
        {
          name: 'management',
          description: 'Administrative tools and controls',
          usage: '/management <group> <subcommand>',
          subcommands: [
            {
              name: 'items create',
              description: 'Create new items in the database',
              usage: '/management items create <name> <type> <rarity>',
              examples: ['/management items create name:"Super Potion" type:CONSUMABLE rarity:RARE'],
            },
            {
              name: 'items delete',
              description: 'Delete items from the database',
              usage: '/management items delete <item>',
              examples: ['/management items delete item:"Old Item"'],
            },
            {
              name: 'schedules status',
              description: 'View scheduler status and statistics',
              usage: '/management schedules status',
              examples: ['/management schedules status'],
            },
            {
              name: 'schedules list',
              description: 'List all scheduled tasks',
              usage: '/management schedules list',
              examples: ['/management schedules list'],
            },
            {
              name: 'schedules enable',
              description: 'Enable a scheduled task',
              usage: '/management schedules enable <task>',
              examples: ['/management schedules enable task:"rift"'],
            },
            {
              name: 'schedules disable',
              description: 'Disable a scheduled task',
              usage: '/management schedules disable <task>',
              examples: ['/management schedules disable task:"cleanup"'],
            },
          ],
        },
      ],
    },
  ];
}

function createCategoryPage(category: { name: string; description: string; emoji: string; commands: CommandInfo[] }) {
  const components = [
    new TextDisplayBuilder().setContent(`${category.emoji} **${category.name}**`),
    new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
    new TextDisplayBuilder().setContent(category.description),
    new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Large),
  ];

  for (const command of category.commands) {
    components.push(new TextDisplayBuilder().setContent(`**/${command.name}**`));
    components.push(new TextDisplayBuilder().setContent(`${command.description}`));

    if (command.subcommands && command.subcommands.length > 0) {
      components.push(new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small));
      components.push(new TextDisplayBuilder().setContent('**Subcommands:**'));

      for (const sub of command.subcommands) {
        components.push(new TextDisplayBuilder().setContent(`  • **${sub.name}** - ${sub.description}`));
        if (sub.usage) {
          components.push(new TextDisplayBuilder().setContent(`    Usage: \`${sub.usage}\``));
        }
        if (sub.examples && sub.examples.length > 0) {
          components.push(new TextDisplayBuilder().setContent(`    Example: \`${sub.examples[0]}\``));
        }
      }
    } else {
      if (command.usage) {
        components.push(new TextDisplayBuilder().setContent(`Usage: \`${command.usage}\``));
      }
      if (command.examples && command.examples.length > 0) {
        components.push(new TextDisplayBuilder().setContent(`Example: \`${command.examples[0]}\``));
      }
    }

    components.push(new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Large));
  }

  // Remove the last separator
  if (components.at(-1) instanceof SeparatorBuilder) {
    components.pop();
  }

  return components;
}
