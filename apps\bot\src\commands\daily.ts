import { calculateDailyReward, calculateNewStreak, canClaimDaily } from '@megami/config/lib/activity';
import { getObject, translate } from '@megami/locale';
import { MessageFlags, SeparatorBuilder, SeparatorSpacingSize, TextDisplayBuilder } from 'discord.js';
import { defineCommand } from '../handlers/command';
import { MegamiContainer } from '../helpers/containers';
import { defer } from '../helpers/defer';

export default defineCommand((builder) => ({
  builder: builder
    .setName(translate('commands.daily.name'))
    .setNameLocalizations(getObject('commands.daily.name'))
    .setDescription(translate('commands.daily.description'))
    .setDescriptionLocalizations(getObject('commands.daily.description')),
  config: {},
  execute: async (interaction) => {
    await defer(interaction, false);

    const user = await interaction.client.database.users.get(interaction.user.id);
    if (!user) {
      const container = new MegamiContainer([
        new TextDisplayBuilder().setContent('❌ **Not Registered**'),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent(translate('errors.user.unregistered.description')),
      ]);

      return await interaction.editReply({
        components: [container],
        flags: MessageFlags.IsComponentsV2,
      });
    }

    const lastClaim = user.lastDailyClaim;
    const currentStreak = user.dailyStreak;

    // Check if user can claim
    const claimCheck = canClaimDaily(lastClaim);
    if (!claimCheck.canClaim) {
      const container = new MegamiContainer([
        new TextDisplayBuilder().setContent('⏰ **Daily Reward Not Ready**'),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent(claimCheck.reason || 'You cannot claim your daily reward yet.'),
      ]);

      return await interaction.editReply({
        components: [container],
        flags: MessageFlags.IsComponentsV2,
      });
    }

    try {
      const now = Date.now();

      // Calculate new streak
      const newStreak = calculateNewStreak(lastClaim, currentStreak);

      // Calculate daily reward
      const dailyReward = calculateDailyReward(newStreak);

      // Apply daily rewards
      const promises: Promise<unknown>[] = [];

      if (dailyReward.rifts > 0) {
        promises.push(interaction.client.database.users.addRifts(user.id, dailyReward.rifts));
      }
      if (dailyReward.shards > 0) {
        const shardsCurrency = await interaction.client.database.currencies.getCurrencyByName('Shards');
        if (shardsCurrency) {
          promises.push(
            interaction.client.database.currencies.addToUserBalance(user.id, shardsCurrency.id, dailyReward.shards)
          );
        }
      }

      // Update user streak
      promises.push(interaction.client.database.users.updateDailyStreak(user.id, newStreak, now));

      await Promise.all(promises);

      // Build response
      const components = [
        new TextDisplayBuilder().setContent('🎁 **Daily Reward Claimed!**'),
        new TextDisplayBuilder().setContent(`Day **${newStreak}** of your streak`),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent('📅 **Daily Reward**'),
      ];

      if (dailyReward.rifts > 0) {
        components.push(new TextDisplayBuilder().setContent(`💎 **+${dailyReward.rifts}** Rifts`));
      }
      if (dailyReward.shards > 0) {
        components.push(new TextDisplayBuilder().setContent(`💰 **+${dailyReward.shards}** Shards`));
      }

      const container = new MegamiContainer(components);
      await interaction.editReply({
        components: [container],
        flags: MessageFlags.IsComponentsV2,
      });
    } catch (error) {
      interaction.client.logger.error('Error claiming daily reward:', error);

      const container = new MegamiContainer([
        new TextDisplayBuilder().setContent('❌ **Error**'),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent('An error occurred while claiming your daily reward.'),
        new TextDisplayBuilder().setContent('Please try again or contact support.'),
      ]);

      await interaction.editReply({
        components: [container],
        flags: MessageFlags.IsComponentsV2,
      });
    }
  },
}));
