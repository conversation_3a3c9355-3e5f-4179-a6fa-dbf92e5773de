import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Rune Prison',
  name: 'Rune Prison',
  element: 'Arcane',
  manaCost: 25,
  cooldown: 4,
  description: 'Traps the target in a prison of runes, rooting them in place.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Root',
        name: 'Rune Prison',
        duration: 2,
        sourceId: caster.id,
      },
      log: `${caster.name} traps ${target.name} in a Rune Prison.`,
    };
  },
});
