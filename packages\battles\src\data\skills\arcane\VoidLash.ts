import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Void Lash',
  name: 'Void Lash',
  element: 'Arcane',
  manaCost: 16,
  cooldown: 1,
  description: "Strike with void energy, dealing damage and reducing the target's defense.",
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    return {
      damage: Math.round(damage * 1.1),
      isCritical,
      appliedEffect: {
        id: 'defense_down',
        name: 'Defense Down',
        duration: 2,
        potency: -8, // -8 defense
        sourceId: caster.id,
      },
      log: `${caster.name} lashes ${target.name} with void energy for ${Math.round(damage * 1.1)} damage${isCritical ? ' (CRIT!)' : ''} and lowers their defense!`,
    };
  },
});
