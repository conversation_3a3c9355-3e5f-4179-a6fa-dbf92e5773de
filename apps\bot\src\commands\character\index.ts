import { getObject, translate } from '@megami/locale';
import type { SlashCommandSubcommandBuilder } from 'discord.js';
import { defineCommand } from '../../handlers/command';
import search from './modules/search';

export default defineCommand((builder) => ({
  builder: builder
    .setName(translate('commands.character.name'))
    .setNameLocalizations(getObject('commands.character.name'))
    .setDescription(translate('commands.character.description'))
    .setDescriptionLocalizations(getObject('commands.character.description'))
    .addSubcommand(search.builder as SlashCommandSubcommandBuilder),

  config: {},

  async execute(interaction) {
    const subcommand = interaction.options.getSubcommand();

    switch (subcommand) {
      case translate('commands.character.modules.search.name'): {
        await search.execute(interaction);
        break;
      }

      default: {
        await interaction.reply({
          content: 'Unknown subcommand.',
          ephemeral: true,
        });
      }
    }
  },
}));
