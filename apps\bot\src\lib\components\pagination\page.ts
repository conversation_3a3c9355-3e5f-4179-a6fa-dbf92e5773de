import {
  type ButtonInteraction,
  SeparatorBuilder,
  SeparatorSpacingSize,
  type StringSelectMenuInteraction,
  TextDisplayBuilder,
} from 'discord.js';
import { type Components, MegamiContainer } from '../../../helpers/containers';
import type { BasePaginator } from './base';

export interface PageOptions {
  interaction: ButtonInteraction | StringSelectMenuInteraction;
}

export class Page {
  public instance: BasePaginator;
  public interaction: ButtonInteraction | StringSelectMenuInteraction;

  constructor(instance: BasePaginator, options: PageOptions) {
    this.instance = instance;
    this.interaction = options.interaction;
  }

  public async respond(components: Components[]) {
    const container = new MegamiContainer([
      ...components,
      new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
      ...this.instance.createComponents(false),
      ...[
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent(`Page ${this.instance.pages.current + 1} of ${this.instance.pages.total}`),
      ],
    ]);

    this.interaction.replied
      ? await this.interaction.editReply({ components: [container] })
      : await this.interaction.update({ components: [container] });
  }
}
