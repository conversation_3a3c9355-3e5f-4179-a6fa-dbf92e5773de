import type { ItemType } from '@megami/database';
import { getObject, translate } from '@megami/locale';
import { MessageFlags, SeparatorBuilder, SeparatorSpacingSize, TextDisplayBuilder } from 'discord.js';
import { defineSubCommand } from '../../../handlers/command';
import { MegamiContainer } from '../../../helpers/containers';
import { defer } from '../../../helpers/defer';

export default defineSubCommand((builder) => ({
  builder: builder
    .setName(translate('commands.inventory.modules.shop.name'))
    .setNameLocalizations(getObject('commands.inventory.modules.shop.name'))
    .setDescription(translate('commands.inventory.modules.shop.description'))
    .setDescriptionLocalizations(getObject('commands.inventory.modules.shop.description'))
    .addStringOption((option) =>
      option
        .setName(translate('commands.inventory.modules.shop.options.category.name'))
        .setNameLocalizations(getObject('commands.inventory.modules.shop.options.category.name'))
        .setDescription(translate('commands.inventory.modules.shop.options.category.description'))
        .setDescriptionLocalizations(getObject('commands.inventory.modules.shop.options.category.description'))
        .setRequired(false)
        .addChoices(
          { name: 'All Items', value: 'all' },
          { name: 'Equipment', value: 'EQUIPMENT' },
          { name: 'Consumables', value: 'CONSUMABLE' },
          { name: 'Materials', value: 'MATERIAL' },
          { name: 'Collectibles', value: 'COLLECTIBLE' },
          { name: 'Currency', value: 'CURRENCY' },
          { name: 'Gifts', value: 'GIFT' },
          { name: 'Keys', value: 'KEY' },
          { name: 'Miscellaneous', value: 'MISC' }
        )
    )
    .addStringOption((option) =>
      option.setName('search').setDescription('Search for specific items by name').setRequired(false).setMaxLength(50)
    ),
  config: {},
  execute: async (interaction) => {
    await defer(interaction, false);

    const user = await interaction.client.database.users.get(interaction.user.id);
    if (!user) {
      const container = new MegamiContainer([
        new TextDisplayBuilder().setContent('❌ **Not Registered**'),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent(translate('errors.user.unregistered.description')),
      ]);

      return await interaction.editReply({
        components: [container],
        flags: MessageFlags.IsComponentsV2,
      });
    }

    const filters = {
      type: interaction.options.getString('type') || 'all',
      search: interaction.options.getString('search'),
    };

    try {
      const userBalance = await interaction.client.database.users.getRifts(user.id);

      const params: Parameters<typeof interaction.client.database.items.search>[0] = {
        isActive: true,
        limit: 25,
      };

      if (filters.type !== 'all') {
        params.type = filters.type as ItemType;
      }

      if (filters.search) {
        params.name = filters.search;
      }

      const items = await interaction.client.database.items.search({
        ...params,
      });

      const purchasable = items.filter(
        (item) => item.metadata?.economy?.buyPrice && item.metadata.economy.buyPrice > 0
      );

      if (purchasable.length === 0) {
        const text = getFilterText(filters.type, filters.search);

        const container = new MegamiContainer([
          new TextDisplayBuilder().setContent('**Item Shop**'),
          new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
          new TextDisplayBuilder().setContent(`No purchasable items found${text}.`),
          new TextDisplayBuilder().setContent('Check back later for new items!'),
        ]);

        return await interaction.editReply({
          components: [container],
          flags: MessageFlags.IsComponentsV2,
        });
      }

      const filtered = purchasable.reduce(
        (acc, item) => {
          const type = item.type;
          if (!acc[type]) acc[type] = [];
          acc[type].push(item);
          return acc;
        },
        {} as Record<string, typeof purchasable>
      );

      const text = getFilterText(filters.type, filters.search);

      const components = [
        new TextDisplayBuilder().setContent(`**Item Shop${text}**`),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent(`**Your Rifts:** ${userBalance.toLocaleString()}`),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
      ];

      for (const [type, value] of Object.entries(filtered)) {
        const emoji = getTypeEmoji(type);
        components.push(new TextDisplayBuilder().setContent(`${emoji} **${type}** (${value.length} items)`));

        const sorted = value.sort((a, b) => {
          const priceA = a.metadata?.economy?.buyPrice || 0;
          const priceB = b.metadata?.economy?.buyPrice || 0;
          if (priceA !== priceB) return priceA - priceB;

          const rarityOrder = ['COMMON', 'UNCOMMON', 'RARE', 'EPIC', 'LEGENDARY', 'MYTHIC'];
          return rarityOrder.indexOf(a.rarity) - rarityOrder.indexOf(b.rarity);
        });

        for (const item of sorted.slice(0, 8)) {
          const price = item.metadata?.economy?.buyPrice || 0;
          const canAfford = userBalance >= price;
          const affordabilityIcon = canAfford ? '✅' : '❌';

          components.push(
            new TextDisplayBuilder().setContent(
              `${item.icon || emoji} **${item.name}** - ${price.toLocaleString()} Rifts ${affordabilityIcon}`
            )
          );

          if (item.description) {
            components.push(new TextDisplayBuilder().setContent(`    └ ${item.description}`));
          }
        }

        if (items.length > 8) {
          components.push(new TextDisplayBuilder().setContent(`  ... and ${items.length - 8} more items`));
        }

        components.push(new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small));
      }

      // Add purchase instructions
      components.push(
        new TextDisplayBuilder().setContent('**How to Purchase:**'),
        new TextDisplayBuilder().setContent('Use `/shop buy <item-name>` to purchase items.'),
        new TextDisplayBuilder().setContent('✅ = You can afford | ❌ = Insufficient rifts')
      );

      const container = new MegamiContainer(components);
      await interaction.editReply({
        components: [container],
        flags: MessageFlags.IsComponentsV2,
      });
    } catch (error) {
      const container = new MegamiContainer([
        new TextDisplayBuilder().setContent('**Error Loading Shop**'),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent('An unexpected error occurred while loading the shop.'),
        new TextDisplayBuilder().setContent('Please try again or contact support.'),
      ]);

      await interaction.editReply({
        components: [container],
        flags: MessageFlags.IsComponentsV2,
      });

      interaction.client.logger.error('Error loading shop:', error);
    }
  },
}));

function getFilterText(type: string, search: string | null): string {
  const filters: string[] = [];

  if (type !== 'all') {
    filters.push(type.toLowerCase());
  }

  if (search) {
    filters.push(`"${search}"`);
  }

  return filters.length > 0 ? ` (${filters.join(', ')})` : '';
}

function getTypeEmoji(type: string): string {
  const emojis: Record<string, string> = {
    EQUIPMENT: '⚔️',
    CONSUMABLE: '🧪',
    MATERIAL: '🔨',
    COLLECTIBLE: '💎',
    CURRENCY: '💰',
    GIFT: '🎁',
    KEY: '🗝️',
    MISC: '📦',
  };

  return emojis[type] || '📦';
}
