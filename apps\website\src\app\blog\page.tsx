'use client';

import { motion } from 'framer-motion';
import { Navbar } from '@/components/navbar';
import { BlogPostCard } from '@megami/ui/components/jules/blog-post-card';

const posts = [
  {
    title: '<PERSON><PERSON> is now live!',
    description: 'We are excited to announce that <PERSON><PERSON> is now live and ready for you to play.',
    date: 'July 31, 2025',
    href: '/blog/megami-is-now-live',
  },
  {
    title: 'New Summer Event',
    description: 'Get ready for our new summer event with exclusive cards and rewards.',
    date: 'July 30, 2025',
    href: '/blog/new-summer-event',
  },
  {
    title: 'Patch Notes 1.1.0',
    description: 'Check out the latest patch notes for version 1.1.0.',
    date: 'July 29, 2025',
    href: '/blog/patch-notes-1-1-0',
  },
];

export default function BlogPage() {
  return (
    <main className="min-h-screen w-full bg-black text-white">
      <Navbar />
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1 }}
        className="container mx-auto px-4 py-8"
      >
        <h1 className="text-4xl font-bold text-center mb-8">Blog</h1>
        <div className="space-y-6">
          {posts.map((post, i) => (
            <motion.div
              key={post.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: i * 0.1 }}
            >
              <BlogPostCard {...post} />
            </motion.div>
          ))}
        </div>
      </motion.div>
    </main>
  );
}
