import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Teleport',
  name: 'Telep<PERSON>',
  element: 'Arcane',
  manaCost: 15,
  cooldown: 3,
  description: 'Instantly teleports the caster to a target location.',
  execute: (caster, target, formulas) => {
    // This would require a system for movement and positioning.
    return {
      log: `${caster.name} teleports to a new location.`,
    };
  },
});
