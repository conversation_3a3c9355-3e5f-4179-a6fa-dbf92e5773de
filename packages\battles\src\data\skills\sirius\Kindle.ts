import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Kindle',
  name: '<PERSON>le',
  element: 'Sirius',
  manaCost: 10,
  cooldown: 2,
  description: 'The caster kindles an ally, increasing their attack speed.',
  execute: (caster, target, formulas) => {
    // This would require a targeting system that can target allies and a status effect system to handle the buff.
    return {
      log: `${caster.name} kindles an ally, increasing their attack speed.`,
    };
  },
});
