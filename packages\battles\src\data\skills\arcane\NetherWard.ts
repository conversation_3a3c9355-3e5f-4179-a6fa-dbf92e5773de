import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Nether Ward',
  name: 'Nether Ward',
  element: 'Arcane',
  manaCost: 30,
  cooldown: 5,
  description: 'Places a ward on the target that damages them whenever they cast a spell.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Damage Reflection',
        name: 'Nether Ward',
        duration: 3,
        sourceId: caster.id,
      },
      log: `${caster.name} places a Nether Ward on ${target.name}.`,
    };
  },
});
