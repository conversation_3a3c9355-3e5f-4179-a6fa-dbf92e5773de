import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Silent Stalker',
  name: 'Silent Stalker',
  element: '<PERSON><PERSON>',
  manaCost: 20,
  cooldown: 4,
  description: 'The caster becomes a silent stalker, gaining bonus damage when attacking from invisibility.',
  execute: (caster, target, formulas) => {
    // This would require a status effect system to handle invisibility and bonus damage.
    return {
      log: `${caster.name} becomes a silent stalker.`,
    };
  },
});
