import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Heat Shield',
  name: 'Heat Shield',
  element: 'Sirius',
  manaCost: 30,
  cooldown: 5,
  description: 'The caster is protected by a heat shield, increasing their defense and reducing the duration of incoming crowd control effects.',
  execute: (caster, target, formulas) => {
    // This would require a status effect system to handle the buff and CC reduction.
    return {
      log: `${caster.name} is protected by a Heat Shield.`,
    };
  },
});
