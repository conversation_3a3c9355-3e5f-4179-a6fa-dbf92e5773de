# `apps/bot`

This is the main Discord bot application for the "Megami" project.

## Installation

To install dependencies:

```bash
bun install
```

## Usage

To run the bot:

```bash
bun run index.ts
```

---

## Architecture

The bot is built with `discord.js` and uses `discord-hybrid-sharding` for clustering. It has a modular architecture with handlers for commands, events, and scheduled tasks.

### `MegamiClient`

The `MegamiClient` class extends the `discord.js` `Client` and is the main entry point for the bot. It initializes and manages all the different parts of the bot, including the database, CDN, logger, storage, cluster, scheduler, and search.

### Command Handler

The command handler in `src/handlers/command.ts` dynamically loads all command files from the `src/commands` directory. Commands are defined using the `defineCommand`, `defineSubCommand`, or `defineSubCommandGroup` functions.

### Event Handler

The event handler in `src/handlers/event.ts` dynamically loads all event files from the `src/events` directory. Events are defined using the `defineEvent` function.

### Task Scheduler

The bot has a task scheduler that is managed by the `MegamiScheduler` class. Tasks are defined in the `src/tasks` directory and are loaded by the task handler in `src/handlers/tasks.ts`.

### Clustering

The bot uses `discord-hybrid-sharding` to run in a clustered environment. The cluster manager is defined in `src/cluster.ts` and is responsible for spawning and managing the clusters.
