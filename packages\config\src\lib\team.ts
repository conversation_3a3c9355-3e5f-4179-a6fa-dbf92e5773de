export interface TeamMember {
  id: string;
  role: 'Owner' | 'Developer' | 'Manager' | 'Artist';
}

export const team: TeamMember[] = [
  {
    id: '835137181504372806',
    role: 'Owner',
  },
  {
    id: '1100158375519604826',
    role: 'Manager',
  },
  {
    id: '869947662626062457',
    role: 'Manager',
  },
  {
    id: '676857207484186635',
    role: 'Manager',
  },
];

export const server = '1391750096277995520';

const hierarchy: Record<TeamMember['role'], number> = {
  Artist: 0,
  Developer: 1,
  Manager: 2,
  Owner: 3,
};

export function is(id: string, role: TeamMember['role']): boolean {
  const member = team.find((m) => m.id === id);
  if (!member) return false;

  return hierarchy[member.role] >= hierarchy[role];
}
