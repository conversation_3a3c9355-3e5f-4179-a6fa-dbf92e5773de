import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Void Blast',
  name: 'Void Blast',
  element: 'Vortex',
  manaCost: 8,
  cooldown: 0,
  description: 'A basic blast of void energy. Deals moderate damage.',
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    return {
      damage,
      isCritical,
      log: `${caster.name} fires a Void Blast at ${target.name} for ${damage} damage${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
