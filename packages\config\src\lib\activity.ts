export interface DailyReward {
  rifts: number;
  shards: number;
  experience?: number;
}

export interface WeeklyReward {
  rifts: number;
  shards: number;
  experience: number;
}

export interface MonthlyReward {
  rifts: number;
  shards: number;
  crowns: number;
  experience: number;
}

// 7-day cycle for daily rewards
export const dailyRewards: DailyReward[] = [
  { rifts: 15, shards: 0 }, // Day 1
  { rifts: 0, shards: 250 }, // Day 2
  { rifts: 15, shards: 0 }, // Day 3
  { rifts: 25, shards: 0 }, // Day 4
  { rifts: 0, shards: 500 }, // Day 5
  { rifts: 50, shards: 0 }, // Day 6
  { rifts: 50, shards: 0 }, // Day 7
];

// Weekly rewards (claimed automatically on 7th day)
export const weeklyRewards: WeeklyReward[] = [
  { rifts: 100, shards: 750, experience: 500 }, // 1st week (7 days)
  { rifts: 100, shards: 1250, experience: 750 }, // 2nd week (14 days)
  { rifts: 150, shards: 1500, experience: 1000 }, // 3rd week (21 days)
  { rifts: 200, shards: 2000, experience: 1250 }, // 4th week (28 days)
];

// Monthly milestone rewards (based on total days in month)
export const monthlyMilestones = {
  tier1: { days: 7, reward: { rifts: 200, shards: 1000, crowns: 50, experience: 1000 } },
  tier2: { days: 15, reward: { rifts: 500, shards: 2500, crowns: 125, experience: 2500 } },
  tier3: { days: 25, reward: { rifts: 1000, shards: 5000, crowns: 250, experience: 5000 } },
} as const;

export function canClaimDaily(lastClaimTimestamp: number): {
  canClaim: boolean;
  reason?: string;
  nextClaimTime?: number;
} {
  const now = Date.now();
  const cooldownMs = 24 * 60 * 60 * 1000; // 24 hours
  const nextClaimTime = lastClaimTimestamp + cooldownMs;

  if (now < nextClaimTime) {
    return {
      canClaim: false,
      reason: `You can claim your next daily reward in ${formatTimeRemaining(nextClaimTime - now)}.`,
      nextClaimTime,
    };
  }

  return { canClaim: true };
}

export function calculateDailyReward(streak: number): DailyReward {
  const dayIndex = (streak - 1) % 7; // 0-6 for days 1-7
  return dailyRewards[dayIndex];
}

export function calculateNewStreak(lastClaimTimestamp: number, currentStreak: number): number {
  const now = Date.now();
  const cooldownMs = 24 * 60 * 60 * 1000; // 24 hours
  const gracePeriodMs = cooldownMs * 0.5; // 12 hours grace period
  const timeSinceLastClaim = now - lastClaimTimestamp;

  // If first claim or within grace period, continue/start streak
  if (lastClaimTimestamp === 0 || timeSinceLastClaim <= cooldownMs + gracePeriodMs) {
    return currentStreak + 1;
  }

  // Streak broken, start over
  return 1;
}

export function formatTimeRemaining(ms: number): string {
  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  if (days > 0) {
    return `${days}d ${hours % 24}h ${minutes % 60}m`;
  }
  if (hours > 0) {
    return `${hours}h ${minutes % 60}m`;
  }
  if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`;
  }
  return `${seconds}s`;
}

export function calculateStreak(lastClaimTimestamp: number, cooldownHours: number): number {
  const now = Date.now();
  const cooldownMs = cooldownHours * 60 * 60 * 1000;
  const gracePeriodMs = cooldownMs * 0.5;
  const timeSinceLastClaim = now - lastClaimTimestamp;

  if (timeSinceLastClaim > cooldownMs + gracePeriodMs) {
    return 0;
  }

  return 1;
}
