import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Incinerate',
  name: 'Incinerate',
  element: 'Sirius',
  manaCost: 25,
  cooldown: 3,
  description: 'Incinerates the target, dealing heavy damage and applying a burn that reduces healing received.',
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const finalDamage = Math.round(damage * 1.4);
    // This would also require a status effect system to handle the burn and healing reduction.
    return {
      damage: finalDamage,
      isCritical,
      log: `${caster.name} incinerates ${target.name} for ${finalDamage} damage${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
