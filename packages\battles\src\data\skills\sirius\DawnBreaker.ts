import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Dawn Breaker',
  name: 'Dawn Breaker',
  element: 'Sirius',
  manaCost: 15,
  cooldown: 2,
  description: 'Break the dawn with stellar light, dealing damage and removing debuffs from yourself.',
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);

    // Remove all negative effects from caster
    caster.activeEffects = caster.activeEffects.filter((effect) => effect.potency >= 0);

    return {
      damage: Math.round(damage * 1.15),
      isCritical,
      log: `${caster.name} breaks the dawn against ${target.name} for ${Math.round(damage * 1.15)} damage${isCritical ? ' (CRIT!)' : ''} and cleanses all debuffs!`,
    };
  },
});
