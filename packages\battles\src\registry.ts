import { percentage } from '@megami/utils/lib/random.js';
import * as formulas from './formulas';
import type { ActiveStatusEffect, BattleEntity } from './types';

// Skill definition type
export interface Skill {
  id: string;
  name: string;
  manaCost: number;
  cooldown: number;
  description: string;
  execute: (caster: BattleEntity, target: BattleEntity) => SkillResult;
}

export interface SkillResult {
  damage?: number;
  isCritical?: boolean;
  appliedEffect?: ActiveStatusEffect;
  log: string;
}

// Status effect definition type
export interface StatusEffectDef {
  id: string;
  name: string;
  onTurnStart?: (target: BattleEntity, effect: ActiveStatusEffect) => StatusEffectResult;
}

export interface StatusEffectResult {
  damage?: number;
  log?: string;
}

// Example Skill: Fireball
export const SKILLS: Record<string, Skill> = {
  fireball: {
    id: 'fireball',
    name: 'Fireball',
    manaCost: 20,
    cooldown: 2,
    description: 'Hurls a ball of fire, with a chance to burn the target.',
    execute: (caster, target) => {
      const { damage, isCritical } = formulas.calculateDamage(caster, target);
      // 50% base chance to burn
      const burnChance = formulas.calculateEffectChance(caster, target, 50);
      const didBurn = percentage(burnChance / 100);
      let appliedEffect: ActiveStatusEffect | undefined;
      let log = `${caster.name} casts Fireball on ${target.name} for ${damage} damage${isCritical ? ' (CRIT!)' : ''}.`;
      if (didBurn) {
        appliedEffect = {
          id: 'burn',
          name: 'Burn',
          duration: 3,
          potency: Math.round(caster.stats.attack * 0.3),
          sourceId: caster.id,
        };
        log += ` ${target.name} is burned!`;
      }
      return { damage, isCritical, appliedEffect, log };
    },
  },
  // Add more skills here
};

// Example Status Effect: Burn
export const STATUS_EFFECTS: Record<string, StatusEffectDef> = {
  burn: {
    id: 'burn',
    name: 'Burn',
    onTurnStart: (target, effect) => {
      const damage = formulas.calculateDotDamage(effect, target);
      return {
        damage,
        log: `${target.name} suffers ${damage} burn damage!`,
      };
    },
  },
  // Add more status effects here
};
