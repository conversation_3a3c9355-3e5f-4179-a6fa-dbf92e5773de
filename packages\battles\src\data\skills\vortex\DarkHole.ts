import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Dark Hole',
  name: 'Dark Hole',
  element: 'Vortex',
  manaCost: 50,
  cooldown: 7,
  description: 'Creates a dark hole that pulls all enemies towards it and silences them.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Dark Hole Effect',
        name: 'Dark Hole',
        duration: 2,
        sourceId: caster.id,
      },
      log: `${caster.name} creates a Dark Hole.`,
    };
  },
});
