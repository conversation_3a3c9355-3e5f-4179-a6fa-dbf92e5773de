import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Firestarter',
  name: 'Firestarter',
  element: 'Sirius',
  manaCost: 20,
  cooldown: 4,
  description: 'The caster becomes a firestarter, causing their basic attacks to have a chance to ignite the target.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Ignited',
        name: 'Firestarter',
        duration: 3,
        sourceId: caster.id,
      },
      log: `${caster.name} becomes a firestarter.`,
    };
  },
});
