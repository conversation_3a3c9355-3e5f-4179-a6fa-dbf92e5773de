import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Fire Spit',
  name: 'Fire Spit',
  element: 'Sirius',
  manaCost: 10,
  cooldown: 2,
  description: 'The caster spits fire at the target, dealing damage and applying a burn.',
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const finalDamage = Math.round(damage * 0.9);
    // This would also require a status effect system to handle the burn.
    return {
      damage: finalDamage,
      isCritical,
      log: `${caster.name} spits fire at ${target.name}, dealing ${finalDamage} damage${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
