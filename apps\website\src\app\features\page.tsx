'use client';

import { motion } from 'framer-motion';
import { Navbar } from '@/components/navbar';
import { Card } from '@megami/ui/components/jules/card';
import { Star } from 'lucide-react';

const features = [
  {
    name: 'Gacha System',
    description: 'Collect cards of your favorite characters from anime, manga, and games.',
    icon: Star,
  },
  {
    name: 'Trading System',
    description: 'Trade cards with other players to complete your collection.',
    icon: Star,
  },
  {
    name: 'Battle System',
    description: 'Battle with other players to test your skills and earn rewards.',
    icon: Star,
  },
  {
    name: 'Economy System',
    description: 'Earn currency, buy items, and participate in a player-driven economy.',
    icon: Star,
  },
  {
    name: 'Customization',
    description: 'Customize your profile and show off your achievements.',
    icon: Star,
  },
  {
    name: 'Regular Updates',
    description: 'New cards, features, and events are added regularly.',
    icon: Star,
  },
];

export default function FeaturesPage() {
  return (
    <main className="min-h-screen w-full bg-black text-white">
      <Navbar />
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1 }}
        className="container mx-auto px-4 py-8"
      >
        <h1 className="text-4xl font-bold text-center mb-8">Features</h1>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {features.map((feature, i) => (
            <motion.div
              key={feature.name}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: i * 0.1 }}
            >
              <Card>
                <div className="flex items-center mb-4">
                  <feature.icon className="h-6 w-6 mr-2 text-yellow-400" />
                  <h3 className="text-xl font-bold">{feature.name}</h3>
                </div>
                <p className="text-gray-400">{feature.description}</p>
              </Card>
            </motion.div>
          ))}
        </div>
      </motion.div>
    </main>
  );
}
