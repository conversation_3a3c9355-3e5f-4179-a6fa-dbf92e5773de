import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Nightmare',
  name: 'Nightmare',
  element: '<PERSON><PERSON>',
  manaCost: 35,
  cooldown: 5,
  description: 'Inflicts the target with a horrifying nightmare, causing them to take damage over time and have a chance to be feared.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Nightmare Effect',
        name: 'Nightmare',
        duration: 3,
        sourceId: caster.id,
      },
      log: `${caster.name} inflicts ${target.name} with a horrifying nightmare.`,
    };
  },
});
