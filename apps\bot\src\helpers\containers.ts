import {
  ActionRowBuilder,
  type <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ContainerBuilder,
  FileBuilder,
  MediaGalleryBuilder,
  SectionBuilder,
  SeparatorBuilder,
  SeparatorSpacingSize,
  TextDisplayBuilder,
} from 'discord.js';

const DEFAULT_FOOTER_TEXT = 'A whisper from the cosmos...';
const DEFAULT_ICON =
  'https://cdn.discordapp.com/avatars/1391709551484604526/a_cb7b34cb660cd8d50a3c4f303a5c7a66.gif?size=1024';

export type Components =
  | ActionRowBuilder
  | SectionBuilder
  | SeparatorBuilder
  | TextDisplayBuilder
  | MediaGalleryBuilder
  | FileBuilder;
export type ContainerOptions = Components[];

export class MegamiContainer extends ContainerBuilder {
  constructor(options?: ContainerOptions) {
    super();

    this.addSectionComponents((header) =>
      header
        .addTextDisplayComponents(
          new TextDisplayBuilder().setContent(
            ['# Celestial Archives', '*“The stars hold secrets beyond your imagination...”*'].join('\n')
          )
        )
        .setThumbnailAccessory((accessory) => accessory.setURL(DEFAULT_ICON))
    );

    this.addSeparatorComponents(new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small));

    if (options) {
      for (const component of options) {
        if (component instanceof SectionBuilder) {
          this.addSectionComponents(component);
        } else if (component instanceof SeparatorBuilder) {
          this.addSeparatorComponents(component);
        } else if (component instanceof TextDisplayBuilder) {
          this.addTextDisplayComponents(component);
        } else if (component instanceof MediaGalleryBuilder) {
          this.addMediaGalleryComponents(component);
        } else if (component instanceof FileBuilder) {
          this.addFileComponents(component);
        } else if (component instanceof ActionRowBuilder) {
          this.addActionRowComponents(component as ActionRowBuilder<ButtonBuilder>);
        }
      }
    }

    this.addSeparatorComponents(new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small));

    this.addTextDisplayComponents((footer) =>
      footer.setContent(`*${DEFAULT_FOOTER_TEXT}* | ${new Date().getFullYear()}`)
    );
  }
}
