/* biome-ignore-all lint/suspicious/noExplicitAny: Have to use this */
import fs from 'node:fs/promises';
import path from 'node:path';
import { createGroq } from '@ai-sdk/groq';
import { generateObject } from 'ai';
import { jsonSchemaToZod } from 'json-schema-to-zod';
import fetch from 'ky';
import convert from 'to-json-schema';

const GROQ = createGroq({
  apiKey: process.env.GROQ_API_KEY,
  fetch: fetch.create({
    timeout: 1000 * 60 * 100,
    cache: 'force-cache',
    retry: 2,
  }),
});

const models = [GROQ('llama-3.3-70b-versatile'), GROQ('moonshotai/kimi-k2-instruct')];

export async function translate<Input extends Record<string, unknown>>(
  data: Input,
  from: string,
  to: string
): Promise<Input> {
  const model = models[Math.floor(Math.random() * models.length)]!;

  const converted = convert(data, { required: true });
  const code = jsonSchemaToZod(converted, { module: 'esm' });
  const unique = `schema.output.${crypto.randomUUID()}.ts`;
  await fs.writeFile(path.join(__dirname, './output/', unique), code);

  try {
    // const schema = (await import(unique)).default as any;

    const { object } = await generateObject({
      model,
      output: 'no-schema',
      prompt: [
        'You are an expert translator.',
        `Translate the following data from ${from} to ${to}, you don't need to translate keys, only values, you need to ignore keys if they are "name" keys, you also need to ignore words wrapped in {{...}} make sure the data structure is preserved and the output is complete in all senses.`,
        JSON.stringify(data),
      ].join('\n'),
      maxTokens: 1024 * 10,
    });

    return object as Input;
  } catch (error) {
    console.error('Translation failed:', error);
    return await translate(data, from, to);
  } finally {
    await fs.unlink(path.join(__dirname, './output/', unique));
  }
}
