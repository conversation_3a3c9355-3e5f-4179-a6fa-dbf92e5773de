import { defineEffect } from '../defineEffect';

export default defineEffect({
  id: 'Mana Burn Effect',
  name: 'Mana Burn Effect',
  element: 'Arcane',
  description: 'The target\'s mana is burned each turn, dealing damage equal to the amount of mana burned.',
  onTurnStart: (target, effect, operators) => {
    const manaBurned = 10;
    // In a real implementation, this would actually burn mana.
    return {
      damage: manaBurned,
      log: `${target.name} has their mana burned for ${manaBurned} damage.`,
    };
  },
});
