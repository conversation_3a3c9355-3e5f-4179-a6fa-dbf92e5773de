import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Reverse Time',
  name: 'Reverse Time',
  element: 'Vortex',
  manaCost: 40,
  cooldown: 6,
  description: 'The caster reverses time, returning all allies to their state from a few turns ago.',
  execute: (caster, target, formulas) => {
    // This would require a complex system to track previous states and a party system.
    return {
      log: `${caster.name} reverses time for their allies.`,
    };
  },
});
