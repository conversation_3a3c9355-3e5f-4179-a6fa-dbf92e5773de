import { createSelectSchema } from 'drizzle-zod';
import type { MegamiDatabaseClient } from '../client';
import { BaseModel } from '../model';

export class Events extends BaseModel {
  public schema = createSelectSchema(this.schemas.events);

  constructor(client: MegamiDatabaseClient, data: unknown) {
    super(client);

    const validated = this.schema.safeParse(data);
    if (validated.error) {
      throw new Error(`Events Schema Parse Error: ${validated.error.issues.length}`);
    }
  }
}
