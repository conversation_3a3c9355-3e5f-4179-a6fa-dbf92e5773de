/* biome-ignore-all lint/suspicious/useAwait: We need this to be a promise */
import path from 'node:path';
import * as prompt from '@clack/prompts';
import ffmpeg from 'fluent-ffmpeg';

/**
 * Creates a boomerang (forward + reverse loop) video or GIF from input video.
 * @param input Path to the original input video (1920x1088)
 * @param output Path for final boomerang output (.mp4, .gif, .webp)
 * @param options Optional settings for output type and dimensions
 */
export async function boomerang(
  input: string,
  output: string,
  options?: { fps?: number; scale?: number; format?: 'mp4' | 'gif' | 'webp' }
): Promise<void> {
  const fps = options?.fps ?? 30;
  const scale = options?.scale ?? 480;
  const format = options?.format ?? (path.extname(output).slice(1) as 'mp4' | 'gif' | 'webp');

  const commands: Record<'output', string[]> = {
    output: [],
  };

  switch (format) {
    case 'gif':
      commands.output.push(...['-loop', '0']);
      break;
    case 'webp':
      commands.output.push(...['-loop', '0', '-an']);
      break;
    default:
      commands.output.push(...['-c:v', 'libx264', '-crf', '18', '-preset', 'veryslow', '-an']);
      break;
  }

  return new Promise((resolve, reject) => {
    ffmpeg(input)
      .inputOptions('-y') // Overwrite if exists
      .complexFilter(
        '[0:v]split[fwd][rev];[rev]reverse,setpts=PTS-STARTPTS[revout];[fwd][revout]concat=n=2:v=1:a=0,' +
          `fps=${fps},scale=${scale}:-1:flags=lanczos`
      )
      .outputOptions(commands.output)
      .on('start', (command) => {
        prompt.log.info(`Boomerang command: ${command}`);
      })
      .on('error', (err, _stdout, stderr) => {
        prompt.log.error(`Boomerang error: ${err.message}`);
        prompt.log.error(`ffmpeg stderr: ${stderr}`);
        reject(err);
      })
      .on('end', () => {
        prompt.log.info(`Boomerang output created at ${output}`);
        resolve();
      })
      .save(output);
  });
}
