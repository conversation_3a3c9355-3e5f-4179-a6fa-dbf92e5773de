import { MessageFlags, SeparatorBuilder, SeparatorSpacingSize, TextDisplayBuilder } from 'discord.js';
import { translate } from '@megami/locale';
import { dropTypes, rarityInfo } from '@megami/config/lib/drops';
import { defineSubCommand } from '../../../handlers/command';
import { MegamiContainer } from '../../../helpers/containers';
import { defer } from '../../../helpers/defer';

export default defineSubCommand((builder) => ({
  builder: builder
    .setName('buy')
    .setDescription('Purchase a drop from the Rift Shop')
    .addStringOption((option) =>
      option
        .setName('type')
        .setDescription('Select a drop type to purchase')
        .setRequired(true)
        .addChoices(
          ...dropTypes.map(drop => ({
            name: `${drop.icon} ${drop.name} (${drop.cost} Rifts)`,
            value: drop.id,
          }))
        )
    ),

  config: {},

  execute: async (interaction) => {
    await defer(interaction, false);

    const user = await interaction.client.database.users.get(interaction.user.id);
    if (!user) {
      const container = new MegamiContainer([
        new TextDisplayBuilder().setContent('❌ **Not Registered**'),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent(translate('errors.user.unregistered.description')),
      ]);

      return await interaction.editReply({
        components: [container],
        flags: MessageFlags.IsComponentsV2,
      });
    }

    const selectedType = interaction.options.getString('type', true);

    try {
      const result = await interaction.client.database.drops.attemptDrop(user.id, selectedType);
      
      if (!result.success) {
        const container = new MegamiContainer([
          new TextDisplayBuilder().setContent('❌ **Purchase Failed**'),
          new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
          new TextDisplayBuilder().setContent(result.error || 'Unknown error occurred'),
        ]);

        return await interaction.editReply({
          components: [container],
          flags: MessageFlags.IsComponentsV2,
        });
      }

      // Success! Show the drop result
      const dropResult = result.result!;
      const rarityEmoji = rarityInfo[dropResult.rarity].emoji;

      const components = [
        new TextDisplayBuilder().setContent('🎉 **Drop Successful!**'),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent(`**Cost:** ${result.riftsCost} Rifts`),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Medium),
        new TextDisplayBuilder().setContent(`${rarityEmoji} **${dropResult.rarity} Card Obtained!**`),
        new TextDisplayBuilder().setContent(`**Card:** ${dropResult.cardName}`),
      ];

      // Show penalty if applied
      if (dropResult.penaltyApplied && dropResult.penaltyDescription) {
        components.push(
          new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Medium),
          new TextDisplayBuilder().setContent('⚠️ **Penalty Applied**'),
          new TextDisplayBuilder().setContent(dropResult.penaltyDescription)
        );
      }

      // Show remaining rifts
      const remainingRifts = await interaction.client.database.users.getRifts(user.id);
      components.push(
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent(`**Remaining Rifts:** ${remainingRifts}/100`)
      );

      const container = new MegamiContainer(components);
      await interaction.editReply({
        components: [container],
        flags: MessageFlags.IsComponentsV2,
      });

    } catch (error) {
      interaction.client.logger.error('Error processing drop purchase:', error);
      
      const container = new MegamiContainer([
        new TextDisplayBuilder().setContent('❌ **Error**'),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent('An error occurred while processing your purchase.'),
        new TextDisplayBuilder().setContent('Please try again or contact support.'),
      ]);

      await interaction.editReply({
        components: [container],
        flags: MessageFlags.IsComponentsV2,
      });
    }
  },
}));
