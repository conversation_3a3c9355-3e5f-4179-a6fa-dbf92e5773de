import { createSelectSchema } from 'drizzle-zod';
import { BaseModel } from '../model';

export class Artists extends BaseModel {
  public schema = createSelectSchema(this.schemas.artists);

  public async get(name: string) {
    const artist = await this.client.instance.query.artists.findFirst({
      where: {
        name,
      },
    });

    if (artist) return artist;

    const inserted = await this.client.instance
      .insert(this.schemas.artists)
      .values({
        name,
      })
      .returning()
      .execute();

    return inserted[0]!;
  }
}
