import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Neutron Star',
  name: 'Neutron Star',
  element: 'Vortex',
  manaCost: 60,
  cooldown: 8,
  description: 'The caster becomes a neutron star, gaining immense defense but being unable to move.',
  execute: (caster, target, formulas) => {
    // This would require a status effect system to handle the buff and root.
    return {
      log: `${caster.name} becomes a neutron star.`,
    };
  },
});
