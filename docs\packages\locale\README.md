# `packages/locale`

This package handles the internationalization (i18n) of the application.

## Installation

To install dependencies:

```bash
bun install
```

## Usage

To run the translation script:

```bash
bun run translate.ts
```

---

## Architecture

The `locale` package uses `i18next` to provide a robust and type-safe i18n system.

### `i18next` Integration

The `i18n.ts` file initializes `i18next` with the available locales and provides a `translate` function for translating keys. The locales are loaded from the `src/locales` directory.

### Translation Script

The `translate.ts` script is used to automatically translate missing keys in the locale files. It compares the locale files with the base `en-US` locale and uses an AI service to translate any missing keys.

### Type Safety

The `types/i18next.d.ts` file provides type definitions for `i18next`, ensuring that all translation keys are type-safe. The `TranslationKey` type is generated from the structure of the `en-US` locale files, so any invalid key will result in a TypeScript error.

### Adding New Locales

To add a new locale, create a new directory in `src/locales` with the corresponding Discord language code. The translation script will then automatically populate it with the translated keys.
