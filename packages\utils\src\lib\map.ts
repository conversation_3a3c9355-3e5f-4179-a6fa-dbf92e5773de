/**
 * Gets the value for a key in a map.
 * @param map The map.
 * @param key The key.
 * @returns The value for the key, or undefined if the key is not in the map.
 */
export function get<K, V>(map: Map<K, V>, key: K): V | undefined {
  return map.get(key);
}

/**
 * Sets the value for a key in a map.
 * @param map The map.
 * @param key The key.
 * @param value The value.
 * @returns The map.
 */
export function set<K, V>(map: Map<K, V>, key: K, value: V): Map<K, V> {
  return map.set(key, value);
}

/**
 * Checks if a map has a key.
 * @param map The map.
 * @param key The key.
 * @returns True if the map has the key, false otherwise.
 */
export function has<K, V>(map: Map<K, V>, key: K): boolean {
  return map.has(key);
}

/**
 * Deletes a key from a map.
 * @param map The map.
 * @param key The key.
 * @returns True if the key was deleted, false otherwise.
 */
export function del<K, V>(map: Map<K, V>, key: K): boolean {
  return map.delete(key);
}

/**
 * Clears a map.
 * @param map The map.
 */
export function clear<K, V>(map: Map<K, V>): void {
  map.clear();
}

/**
 * Returns an iterator for the keys of a map.
 * @param map The map.
 * @returns An iterator for the keys of the map.
 */
export function keys<K, V>(map: Map<K, V>): IterableIterator<K> {
  return map.keys();
}

/**
 * Returns an iterator for the values of a map.
 * @param map The map.
 * @returns An iterator for the values of the map.
 */
export function values<K, V>(map: Map<K, V>): IterableIterator<V> {
  return map.values();
}

/**
 * Returns an iterator for the entries of a map.
 * @param map The map.
 * @returns An iterator for the entries of the map.
 */
export function entries<K, V>(map: Map<K, V>): IterableIterator<[K, V]> {
  return map.entries();
}
