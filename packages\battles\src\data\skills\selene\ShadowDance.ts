import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Shadow Dance',
  name: 'Shadow Dance',
  element: '<PERSON><PERSON>',
  manaCost: 30,
  cooldown: 5,
  description: 'The caster dances through the shadows, attacking multiple enemies in quick succession.',
  execute: (caster, target, formulas) => {
    // This would require a more complex targeting system.
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const finalDamage = Math.round(damage * 1.2);
    return {
      damage: finalDamage,
      isCritical,
      log: `${caster.name} performs a Shadow Dance, attacking multiple enemies for ${finalDamage} damage${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
