export type CurrencyTier = 'Stardust' | 'Moonlight' | 'Celestial';
export type CurrencyNames = 'Shards' | 'Crowns' | 'Embers' | 'Rifts';

export interface Currency {
  readonly name: CurrencyNames;
  readonly description: string;
  readonly tier: CurrencyTier;
}

export const currencies: Readonly<Currency[]> = [
  {
    name: 'Shards',
    description:
      'Glittering fragments of enchanted confections, used in everyday trade and to purchase essential items. They embody simple delight and accessible magic.',
    tier: 'Stardust',
  },
  {
    name: 'Crowns',
    description:
      'Crystallized orbs of latent energy, prized for enhancing characters, unlocking dormant abilities, and obtaining rare gear. They signify sealed power and ascension.',
    tier: 'Moonlight',
  },
  {
    name: 'Embers',
    description:
      'Sacred flames drawn from ancient rituals, used to summon mythical heroes or acquire divine relics. They radiate pure devotion and celestial force.',
    tier: 'Celestial',
  },
  {
    name: 'Rifts',
    description: 'The currency of the realm, used to purchase drops.',
    tier: 'Stardust',
  },
] as const;

export function getCurrencyByName(name: CurrencyNames): Currency | undefined {
  return currencies.find((c) => c.name === name);
}
