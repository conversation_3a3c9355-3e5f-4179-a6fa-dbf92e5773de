import type { ItemType, ItemRarity, ItemMetadata } from '@megami/database/src/schema/items';

export interface ItemConfig {
  name: string;
  description: string;
  type: ItemType;
  rarity: ItemRarity;
  icon?: string;
  image?: string;
  stackable?: boolean;
  maxStack?: number;
  tradeable?: boolean;
  sellable?: boolean;
  metadata: ItemMetadata;
  isActive?: boolean;
  isHidden?: boolean;
}

export const items: ItemConfig[] = [
  // RIFT MULTIPLIER ITEMS
  {
    name: 'Minor Rift Essence',
    description: 'A small vial of concentrated rift energy that slightly enhances rift regeneration.',
    type: 'CONSUMABLE',
    rarity: 'COMMON',
    icon: '🧪',
    stackable: true,
    maxStack: 50,
    tradeable: true,
    sellable: true,
    metadata: {
      effects: [
        {
          type: 'RIFT_MULTIPLIER',
          name: 'Minor Rift Enhancement',
          description: 'Increases rift regeneration by 25%',
          data: {
            multiplier: 1.25,
            stackable: false,
          },
          duration: 1800, // 30 minutes
          onUse: true,
        },
      ],
      restrictions: {
        cooldown: 60, // 1 minute cooldown
      },
      economy: {
        sellPrice: 25,
        buyPrice: 75,
      },
      flavor: {
        lore: 'A basic alchemical creation that novice rift mages use to enhance their abilities.',
        tags: ['consumable', 'rift', 'enhancement', 'common'],
        color: '#87CEEB',
      },
    },
  },

  {
    name: 'Rift Amplifier Potion',
    description: 'A potent elixir that significantly boosts rift regeneration for a moderate duration.',
    type: 'CONSUMABLE',
    rarity: 'UNCOMMON',
    icon: '🧪',
    stackable: true,
    maxStack: 25,
    tradeable: true,
    sellable: true,
    metadata: {
      effects: [
        {
          type: 'RIFT_MULTIPLIER',
          name: 'Rift Amplification',
          description: 'Doubles rift regeneration rate',
          data: {
            multiplier: 2.0,
            stackable: false,
          },
          duration: 3600, // 1 hour
          onUse: true,
        },
      ],
      restrictions: {
        cooldown: 300, // 5 minute cooldown
      },
      economy: {
        sellPrice: 100,
        buyPrice: 250,
      },
      flavor: {
        lore: 'Crafted by skilled alchemists, this potion temporarily amplifies one\'s natural rift absorption abilities.',
        tags: ['consumable', 'rift', 'boost', 'uncommon'],
        color: '#9D4EDD',
      },
    },
  },

  {
    name: 'Greater Rift Elixir',
    description: 'A masterfully crafted elixir that dramatically enhances rift regeneration.',
    type: 'CONSUMABLE',
    rarity: 'RARE',
    icon: '🧪',
    stackable: true,
    maxStack: 10,
    tradeable: true,
    sellable: true,
    metadata: {
      effects: [
        {
          type: 'RIFT_MULTIPLIER',
          name: 'Greater Rift Enhancement',
          description: 'Triples rift regeneration rate',
          data: {
            multiplier: 3.0,
            stackable: false,
          },
          duration: 5400, // 1.5 hours
          onUse: true,
        },
      ],
      restrictions: {
        levelRequired: 15,
        cooldown: 600, // 10 minute cooldown
      },
      economy: {
        sellPrice: 300,
        buyPrice: 750,
      },
      flavor: {
        lore: 'A rare concoction that only master alchemists can create, infused with pure rift crystals.',
        tags: ['consumable', 'rift', 'rare', 'powerful'],
        color: '#7209B7',
      },
    },
  },

  {
    name: 'Legendary Rift Catalyst',
    description: 'An extraordinary catalyst that provides unparalleled rift regeneration enhancement.',
    type: 'CONSUMABLE',
    rarity: 'LEGENDARY',
    icon: '⚗️',
    stackable: true,
    maxStack: 5,
    tradeable: true,
    sellable: true,
    metadata: {
      effects: [
        {
          type: 'RIFT_MULTIPLIER',
          name: 'Legendary Rift Mastery',
          description: 'Quintuples rift regeneration rate',
          data: {
            multiplier: 5.0,
            stackable: false,
          },
          duration: 7200, // 2 hours
          onUse: true,
        },
      ],
      restrictions: {
        levelRequired: 30,
        cooldown: 1800, // 30 minute cooldown
      },
      economy: {
        sellPrice: 1000,
        buyPrice: 2500,
      },
      flavor: {
        lore: 'Forged in the heart of a rift storm, this catalyst contains the essence of pure dimensional energy.',
        tags: ['consumable', 'rift', 'legendary', 'ultimate'],
        color: '#FFD700',
      },
    },
  },

  // RIFT BONUS ITEMS
  {
    name: 'Rift Shard',
    description: 'A crystallized fragment of rift energy that provides additional rift regeneration.',
    type: 'CONSUMABLE',
    rarity: 'COMMON',
    icon: '💎',
    stackable: true,
    maxStack: 100,
    tradeable: true,
    sellable: true,
    metadata: {
      effects: [
        {
          type: 'RIFT_BONUS',
          name: 'Rift Shard Boost',
          description: 'Grants +3 bonus rifts per regeneration cycle',
          data: {
            bonus: 3,
            stackable: true,
            maxStacks: 5,
          },
          duration: 2700, // 45 minutes
          onUse: true,
        },
      ],
      economy: {
        sellPrice: 15,
        buyPrice: 50,
      },
      flavor: {
        lore: 'Small fragments that break off from larger rift crystals, still containing residual energy.',
        tags: ['consumable', 'rift', 'bonus', 'stackable'],
        color: '#40E0D0',
      },
    },
  },

  {
    name: 'Concentrated Rift Crystal',
    description: 'A pure crystal of rift energy that significantly boosts rift regeneration.',
    type: 'CONSUMABLE',
    rarity: 'RARE',
    icon: '💎',
    stackable: true,
    maxStack: 20,
    tradeable: true,
    sellable: true,
    metadata: {
      effects: [
        {
          type: 'RIFT_BONUS',
          name: 'Crystal Rift Boost',
          description: 'Grants +8 bonus rifts per regeneration cycle',
          data: {
            bonus: 8,
            stackable: true,
            maxStacks: 3,
          },
          duration: 5400, // 1.5 hours
          onUse: true,
        },
      ],
      restrictions: {
        levelRequired: 20,
      },
      economy: {
        sellPrice: 200,
        buyPrice: 500,
      },
      flavor: {
        lore: 'A concentrated crystal formed in the depths of ancient rifts, pulsing with raw energy.',
        tags: ['consumable', 'rift', 'crystal', 'powerful'],
        color: '#4B0082',
      },
    },
  },

  // EXPERIENCE ITEMS
  {
    name: 'Wisdom Scroll',
    description: 'An ancient scroll that enhances learning and experience gain.',
    type: 'CONSUMABLE',
    rarity: 'UNCOMMON',
    icon: '📜',
    stackable: true,
    maxStack: 30,
    tradeable: true,
    sellable: true,
    metadata: {
      effects: [
        {
          type: 'EXP_MULTIPLIER',
          name: 'Enhanced Learning',
          description: 'Increases experience gains by 50%',
          data: {
            multiplier: 1.5,
            stackable: false,
          },
          duration: 3600, // 1 hour
          onUse: true,
        },
      ],
      economy: {
        sellPrice: 80,
        buyPrice: 200,
      },
      flavor: {
        lore: 'Written by ancient scholars, these scrolls contain knowledge that accelerates learning.',
        tags: ['consumable', 'experience', 'wisdom', 'scroll'],
        color: '#DAA520',
      },
    },
  },

  {
    name: 'Experience Orb',
    description: 'A glowing orb that provides a flat bonus to experience gains.',
    type: 'CONSUMABLE',
    rarity: 'COMMON',
    icon: '🔮',
    stackable: true,
    maxStack: 50,
    tradeable: true,
    sellable: true,
    metadata: {
      effects: [
        {
          type: 'EXP_BONUS',
          name: 'Experience Boost',
          description: 'Grants +50 bonus experience per gain',
          data: {
            bonus: 50,
            stackable: true,
            maxStacks: 10,
          },
          duration: 1800, // 30 minutes
          onUse: true,
        },
      ],
      economy: {
        sellPrice: 30,
        buyPrice: 100,
      },
      flavor: {
        lore: 'Crystallized knowledge that directly enhances one\'s understanding and growth.',
        tags: ['consumable', 'experience', 'orb', 'bonus'],
        color: '#FF6347',
      },
    },
  },

  // EQUIPMENT ITEMS
  {
    name: 'Ring of Rift Mastery',
    description: 'An ancient ring that grants permanent rift energy enhancement while worn.',
    type: 'EQUIPMENT',
    rarity: 'EPIC',
    icon: '💍',
    stackable: false,
    maxStack: 1,
    tradeable: true,
    sellable: true,
    metadata: {
      effects: [
        {
          type: 'RIFT_BONUS',
          name: 'Rift Mastery',
          description: 'Grants +5 bonus rifts per regeneration cycle',
          data: {
            bonus: 5,
            stackable: true,
            maxStacks: 3,
          },
          permanent: true,
          onEquip: true,
        },
      ],
      stats: {
        luck: 10,
      },
      restrictions: {
        levelRequired: 25,
      },
      economy: {
        sellPrice: 1000,
        buyPrice: 2500,
      },
      flavor: {
        lore: 'Forged in the heart of a collapsed rift, this ring pulses with residual rift energy.',
        tags: ['equipment', 'ring', 'rift', 'permanent'],
        color: '#7209B7',
      },
    },
  },

  {
    name: 'Amulet of Swift Learning',
    description: 'A mystical amulet that permanently enhances the wearer\'s ability to gain experience.',
    type: 'EQUIPMENT',
    rarity: 'EPIC',
    icon: '🔮',
    stackable: false,
    maxStack: 1,
    tradeable: true,
    sellable: true,
    metadata: {
      effects: [
        {
          type: 'EXP_MULTIPLIER',
          name: 'Swift Learning',
          description: 'Increases experience gains by 25%',
          data: {
            multiplier: 1.25,
            stackable: true,
            maxStacks: 2,
          },
          permanent: true,
          onEquip: true,
        },
      ],
      stats: {
        luck: 5,
        speed: 10,
      },
      restrictions: {
        levelRequired: 20,
      },
      economy: {
        sellPrice: 800,
        buyPrice: 2000,
      },
      flavor: {
        lore: 'Blessed by ancient teachers, this amulet accelerates the learning process.',
        tags: ['equipment', 'amulet', 'experience', 'permanent'],
        color: '#32CD32',
      },
    },
  },
];

export default items;
