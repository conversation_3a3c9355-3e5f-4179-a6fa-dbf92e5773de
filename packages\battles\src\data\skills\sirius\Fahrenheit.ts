import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Fahrenheit',
  name: 'Fahrenheit',
  element: 'Sirius',
  manaCost: 30,
  cooldown: 5,
  description: 'The caster raises the temperature to extreme levels, causing all enemies to take damage over time.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Burn',
        name: 'Fahrenheit',
        duration: 3,
        sourceId: caster.id,
      },
      log: `${caster.name} raises the temperature to extreme levels.`,
    };
  },
});
