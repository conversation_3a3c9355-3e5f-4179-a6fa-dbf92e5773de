import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Midnight Embrace',
  name: 'Midnight Embrace',
  element: '<PERSON><PERSON>',
  manaCost: 40,
  cooldown: 6,
  description: 'The caster embraces the midnight, healing themselves and becoming invisible.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Midnight Embrace Effect',
        name: 'Midnight Embrace',
        duration: 3,
        sourceId: caster.id,
      },
      log: `${caster.name} embraces the midnight.`,
    };
  },
});
