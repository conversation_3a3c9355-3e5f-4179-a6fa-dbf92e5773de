import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Starfall',
  name: 'Starfall',
  element: 'Selene',
  manaCost: 45,
  cooldown: 6,
  description: 'Calls down a shower of stars, damaging all enemies in an area over time.',
  execute: (caster, target, formulas) => {
    // This would require a more complex targeting and status effect system.
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const finalDamage = Math.round(damage * 1.5);
    return {
      damage: finalDamage,
      isCritical,
      log: `${caster.name} calls down a Starfall, damaging all enemies for ${finalDamage} damage${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
