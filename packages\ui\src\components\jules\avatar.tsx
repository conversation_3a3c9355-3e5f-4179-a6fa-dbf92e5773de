import { cn } from '@megami/ui/lib/utils';
import { cva, type VariantProps } from 'class-variance-authority';
import * as React from 'react';

const avatarVariants = cva('relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full', {
  variants: {
    size: {
      sm: 'h-8 w-8',
      md: 'h-10 w-10',
      lg: 'h-16 w-16',
    },
  },
  defaultVariants: {
    size: 'md',
  },
});

export interface AvatarProps extends React.HTMLAttributes<HTMLDivElement>, VariantProps<typeof avatarVariants> {
  src: string;
  alt: string;
}

const Avatar = React.forwardRef<HTMLDivElement, AvatarProps>(({ className, size, src, alt, ...props }, ref) => (
  <div className={cn(avatarVariants({ size }), className)} ref={ref} {...props}>
    <img alt={alt} className="aspect-square h-full w-full" src={src} />
  </div>
));
Avatar.displayName = 'Avatar';

export { Avatar };
