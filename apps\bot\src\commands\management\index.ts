import { getObject, translate } from '@megami/locale';
import type { SlashCommandSubcommandBuilder, SlashCommandSubcommandGroupBuilder } from 'discord.js';
import { defineCommand } from '../../handlers/command';
import { check } from '../../helpers/permissions';
import disable from './modules/disable';
import items from './modules/items';
import schedules from './modules/schedules';
import skins from './modules/skins';

export default defineCommand((builder) => ({
  builder: builder
    .setName(translate('commands.management.name'))
    .setNameLocalizations(getObject('commands.management.name'))
    .setDescription(translate('commands.management.description'))
    .setDescriptionLocalizations(getObject('commands.management.description'))
    .addSubcommandGroup(items.builder as SlashCommandSubcommandGroupBuilder)
    .addSubcommandGroup(schedules.builder as SlashCommandSubcommandGroupBuilder)
    .addSubcommandGroup(skins.builder as SlashCommandSubcommandGroupBuilder)
    .addSubcommand(disable.builder as <PERSON><PERSON><PERSON>ommandSubcommandBuilder),
  config: {
    only: 'manager',
  },
  execute: async (interaction) => {
    const subcommand = interaction.options.getSubcommand();
    const subcommandGroup = interaction.options.getSubcommandGroup();

    if (subcommandGroup) {
      switch (subcommandGroup) {
        case items.builder.name_localizations![interaction.locale]: {
          const verify = await check(interaction, items);
          if (!verify) return;

          await items.execute(interaction);
          break;
        }

        case schedules.builder.name_localizations![interaction.locale]: {
          const verify = await check(interaction, schedules);
          if (!verify) return;

          await schedules.execute(interaction);
          break;
        }

        case skins.builder.name_localizations![interaction.locale]: {
          const verify = await check(interaction, skins);
          if (!verify) return;

          await skins.execute(interaction);
          break;
        }

        default: {
          await interaction.reply({
            content: translate('errors.general.unknown.description'),
            ephemeral: true,
          });
        }
      }

      return;
    }

    switch (subcommand) {
      case disable.builder.name_localizations![interaction.locale]: {
        const verify = await check(interaction, disable);
        if (!verify) return;

        await disable.execute(interaction);
        break;
      }

      default: {
        await interaction.reply({
          content: translate('errors.general.unknown.description'),
          ephemeral: true,
        });
      }
    }
  },
}));
