import { type ElementNames, elements } from '@megami/config/lib/elements';
import { generateObject } from 'ai';
import { z } from 'zod';
import type { MegamiAI } from './client';

export interface CharacterAnalysisInput {
  name: string;
  description: string;
}

export interface ElementAnalysisResult {
  element: ElementNames;
  confidence: number;
  reasoning: string;
  alternativeElements?: {
    element: ElementNames;
    confidence: number;
    reasoning: string;
  }[];
}

const ElementAnalysisSchema = z.object({
  element: z.enum(['Arcane', 'Vortex', 'Sirius', 'Selene']),
  confidence: z.number().min(0).max(1),
  reasoning: z.string(),
  primaryTraits: z.array(z.string()),
  elementalAffinities: z.array(z.string()),
  thematicAlignment: z.string(),
  alternativeElements: z
    .array(
      z.object({
        element: z.enum(['Arcane', 'Vortex', 'Sirius', 'Selene']),
        confidence: z.number().min(0).max(1),
        reasoning: z.string(),
      })
    )
    .optional(),
});

export class MegamiCharacterAI {
  private client: MegamiAI;

  constructor(client: MegamiAI) {
    this.client = client;
  }

  public async element(input: CharacterAnalysisInput) {
    const prompts = this.prompts(input);

    const response = await generateObject({
      model: this.client.providers.google('gemini-2.5-pro'),
      // @ts-expect-error - ignore this
      schema: ElementAnalysisSchema,
      messages: [
        { role: 'system', content: prompts.system },
        { role: 'user', content: prompts.user },
      ],
      temperature: 0.3,
      maxTokens: 800,
    });

    const analysis = response.object as ElementAnalysisResult;

    return {
      character: input,
      ...analysis,
    };
  }

  public prompts(input: CharacterAnalysisInput) {
    const descriptions = elements.map((el) => `**${el.name}**: ${el.description}`).join('\n\n');

    return {
      system: `You are an expert character analyst specializing in elemental affinity assessment. Your task is to analyze character descriptions and assign the most fitting elemental type based on their traits, abilities, and thematic elements.

**Available Elements:**

${descriptions}

**Analysis Guidelines:**
1. **Arcane**: Characters who manipulate fate, reality, cosmic forces, magic, or probability
2. **Vortex**: Characters associated with void, darkness, time manipulation, gravity, or consuming forces
3. **Sirius**: Characters embodying light, nobility, celestial power, leadership, or piercing truth
4. **Selene**: Characters using illusion, dreams, chaos, misdirection, or lunar/mystical powers

**Instructions:**
- Analyze the character's description for thematic elements, abilities, and personality traits
- Consider their powers, background, and narrative role
- Assign confidence based on how clearly the character fits the element (0.0-1.0)
- Provide clear reasoning for your choice
- Identify primary traits that influenced your decision
- List specific elemental affinities found in the description
- Explain the thematic alignment

Respond with structured data only. Be thorough but concise in your analysis.`,
      user: `Analyze this character and assign the most fitting element:

**Character Name:** ${input.name}

**Character Description:** ${input.description}

Please provide a detailed analysis including:
1. The assigned element with confidence score
2. Primary traits that influenced the decision
3. Specific elemental affinities found
4. Thematic alignment explanation
5. Clear reasoning for the choice

Consider all aspects of the character including their abilities, personality, background, and thematic elements when making your assessment.`,
    };
  }
}
