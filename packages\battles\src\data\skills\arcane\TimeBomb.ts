import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Time Bomb',
  name: 'Time Bomb',
  element: 'Arcane',
  manaCost: 35,
  cooldown: 5,
  description: 'Places a time bomb on the target that explodes after a few turns, dealing massive damage.',
  execute: (caster, target, formulas) => {
    // This would require a status effect system to handle the bomb.
    return {
      log: `${caster.name} places a Time Bomb on ${target.name}.`,
    };
  },
});
