import { integer, pgTable, text, timestamp, uniqueIndex, uuid } from 'drizzle-orm/pg-core';

export interface Guild {
  id: string;
  name: string;
  ownerId: string;
}

export interface GuildMember {
  userId: string;
  guildId: string;
  role: string;
}

export const guilds = pgTable(
  'guilds',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    name: text('name').notNull(),
    ownerId: uuid('owner_id').notNull(),
    createdAt: timestamp('created_at').defaultNow().notNull(),
    level: integer('level').default(1).notNull(),
    experience: integer('experience').default(0).notNull(),
  },
  (table) => [uniqueIndex('guild_name').on(table.name)]
);
