import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Astral Barrier',
  name: 'Astral Barrier',
  element: 'Arcane',
  manaCost: 12,
  cooldown: 3,
  description: 'Raise a barrier of astral energy, boosting your defense for 2 turns.',
  execute: (caster) => {
    return {
      log: `${caster.name} conjures an Astral Barrier, boosting their defense!`,
      appliedEffect: {
        id: 'Astral Barrier',
        name: 'Astral Barrier',
        duration: 2,
        potency: 0.2,
        sourceId: caster.id,
      },
    };
  },
});
