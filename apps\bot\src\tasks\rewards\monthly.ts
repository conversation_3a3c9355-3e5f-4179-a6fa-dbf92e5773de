import { monthlyMilestones } from '@megami/config/lib/activity';
import { gt, users as table } from '@megami/database';
import { defineTask } from '../../structure/scheduler';

export default defineTask({
  id: 'monthly:rewards',
  name: 'Monthly Rewards & Reset',
  expression: '0 0 0 1 * *', // First day of every month at midnight
  enabled: true,
  execute: async (client) => {
    try {
      // Get all users with daily streaks
      const users = await client.database.instance
        .select()
        .from(client.database.users.schemas.users)
        .where(gt(table.dailyStreak, 0));

      let rewardsGiven = 0;
      let streaksReset = 0;

      for await (const user of users) {
        const streak = user.dailyStreak;
        const promises: Promise<unknown>[] = [];

        // Calculate monthly milestone rewards based on streak
        const monthlyRewards: (typeof monthlyMilestones.tier1.reward)[] = [];

        if (streak >= monthlyMilestones.tier1.days) {
          monthlyRewards.push(monthlyMilestones.tier1.reward);
        }

        if (streak >= monthlyMilestones.tier2.days) {
          monthlyRewards.push(monthlyMilestones.tier2.reward);
        }

        if (streak >= monthlyMilestones.tier3.days) {
          monthlyRewards.push(monthlyMilestones.tier3.reward);
        }

        // Apply monthly rewards
        if (monthlyRewards.length > 0) {
          const shardsCurrency = await client.database.currencies.getCurrencyByName('Shards');
          const crownsCurrency = await client.database.currencies.getCurrencyByName('Crowns');

          for (const reward of monthlyRewards) {
            // Add rifts
            promises.push(client.database.users.addRifts(user.id, reward.rifts));

            // Add shards
            if (shardsCurrency) {
              promises.push(client.database.currencies.addToUserBalance(user.id, shardsCurrency.id, reward.shards));
            }

            // Add crowns
            if (crownsCurrency) {
              promises.push(client.database.currencies.addToUserBalance(user.id, crownsCurrency.id, reward.crowns));
            }

            // Add experience
            if (reward.experience > 0) {
              promises.push(client.database.users.addExperience(user.id, reward.experience));
            }
          }

          rewardsGiven++;
          client.logger.info(
            `Gave monthly rewards to user ${user.id} (${monthlyRewards.length} tiers, ${streak} days)`
          );
        }

        // Reset daily streak for the new month
        promises.push(client.database.users.resetDailyStreak(user.id));

        // Reset voting streak for the new month
        promises.push(client.database.users.resetVotingStreak(user.id));
        streaksReset++;

        await Promise.all(promises);
      }

      client.logger.info(
        `Monthly task completed: ${rewardsGiven} users received rewards, ${streaksReset} daily/voting streaks reset`
      );
    } catch (error) {
      client.logger.error('Error during monthly rewards task:', error);
    }
  },
});
