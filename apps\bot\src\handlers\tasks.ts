import fs from 'node:fs';
import path from 'node:path';
import type { MegamiClient } from '../structure/client';
import type { ScheduledTask } from '../structure/scheduler';

export async function defineTasks(client: MegamiClient) {
  const folder = path.join(process.cwd(), 'src', 'tasks');
  const files = fs.readdirSync(folder, {
    withFileTypes: true,
    recursive: true,
  });

  const tasks: ScheduledTask[] = [];

  for await (const file of files) {
    if (file.isFile() && file.name.endsWith('.ts')) {
      const task: ScheduledTask | undefined = (await import(path.join(file.parentPath, file.name))).default;
      if (typeof task !== 'undefined') {
        tasks.push(task);
      }
    }
  }

  for (const task of tasks) {
    client.scheduler.addTask(task);
  }
}
