import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Gravity Crush',
  name: 'Gravity Crush',
  element: 'Vortex',
  manaCost: 25,
  cooldown: 4,
  description: 'Crushes the target with immense gravity, dealing damage and stunning them.',
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const finalDamage = Math.round(damage * 1.3);
    // This would also require a status effect system to handle the stun.
    return {
      damage: finalDamage,
      isCritical,
      log: `${caster.name} crushes ${target.name} with Gravity Crush for ${finalDamage} damage${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
