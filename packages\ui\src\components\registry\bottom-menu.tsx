'use client';

import type { Variants } from 'framer-motion';
import { AnimatePresence, motion } from 'framer-motion';
import type { LucideIcon } from 'lucide-react';
import * as React from 'react';

import { cn } from '../../lib/utils';

export interface MenuBarItem {
  icon: LucideIcon;
  label: string;
  action: React.MouseEventHandler<HTMLButtonElement>;
}

interface MenuBarProps extends React.HTMLAttributes<HTMLDivElement> {
  items: MenuBarItem[];
}

const springConfig = {
  duration: 0.3,
  ease: 'easeInOut',
} as unknown as Variants;

export function MenuBar({ items, className, ...props }: MenuBarProps) {
  const [activeIndex, setActiveIndex] = React.useState<number | null>(null);
  const menuRef = React.useRef<HTMLDivElement>(null);
  const [tooltipPosition, setTooltipPosition] = React.useState({
    left: 0,
    width: 0,
  });
  const tooltipRef = React.useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    if (activeIndex !== null && menuRef.current && tooltipRef.current) {
      const menuItem = menuRef.current.children[activeIndex] as HTMLElement;
      const menuRect = menuRef.current.getBoundingClientRect();
      const itemRect = menuItem.getBoundingClientRect();
      const tooltipRect = tooltipRef.current.getBoundingClientRect();

      const left = itemRect.left - menuRect.left + (itemRect.width - tooltipRect.width) / 2;

      setTooltipPosition({
        left: Math.max(0, Math.min(left, menuRect.width - tooltipRect.width)),
        width: tooltipRect.width,
      });
    }
  }, [activeIndex]);

  return (
    <div className={cn('relative', className)} {...props}>
      <AnimatePresence>
        {activeIndex !== null && (
          <motion.div
            animate={{ opacity: 1, y: 0 }}
            className="-top-[31px] pointer-events-none absolute right-0 left-0 z-50"
            exit={{ opacity: 0, y: 5 }}
            initial={{ opacity: 0, y: 5 }}
            transition={springConfig}
          >
            <motion.div
              animate={{ x: tooltipPosition.left }}
              className={cn(
                'inline-flex h-7 items-center justify-center overflow-hidden rounded-lg px-3',
                'bg-background/95 backdrop-blur',
                'border border-border/50',
                'shadow-[0_0_0_1px_rgba(0,0,0,0.08)]',
                'dark:border-border/50 dark:shadow-[0_0_0_1px_rgba(255,255,255,0.08)]'
              )}
              initial={{ x: tooltipPosition.left }}
              ref={tooltipRef}
              style={{ width: 'auto' }}
              transition={springConfig}
            >
              <p className="whitespace-nowrap font-medium text-[13px] leading-tight">{items[activeIndex].label}</p>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      <div
        className={cn(
          'z-10 inline-flex h-10 items-center justify-center gap-[3px] overflow-hidden px-1.5',
          'rounded-full bg-background/95 backdrop-blur',
          'border border-border/50',
          'shadow-[0_0_0_1px_rgba(0,0,0,0.08),0_8px_16px_-4px_rgba(0,0,0,0.1)]',
          'dark:border-border/50 dark:shadow-[0_0_0_1px_rgba(255,255,255,0.08),0_8px_16px_-4px_rgba(0,0,0,0.2)]'
        )}
        ref={menuRef}
      >
        {items.map((item, index) => (
          <button
            className="flex h-8 w-8 items-center justify-center gap-2 rounded-full px-3 py-1 transition-colors hover:bg-muted/80"
            key={`${item.label}-${index}`}
            onClick={item.action}
            onMouseEnter={() => setActiveIndex(index)}
            onMouseLeave={() => setActiveIndex(null)}
            type="button"
          >
            <div className="flex items-center justify-center">
              <div className="flex h-[18px] w-[18px] items-center justify-center overflow-hidden">
                <item.icon className="h-full w-full" />
              </div>
            </div>
            <span className="sr-only">{item.label}</span>
          </button>
        ))}
      </div>
    </div>
  );
}
