import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Flameburst',
  name: 'Flameburst',
  element: 'Sirius',
  manaCost: 20,
  cooldown: 3,
  description: 'A burst of flame that damages all enemies in a small area.',
  execute: (caster, target, formulas) => {
    // This would require a more complex targeting system.
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const finalDamage = Math.round(damage * 1.1);
    return {
      damage: finalDamage,
      isCritical,
      log: `${caster.name} creates a Flameburst, damaging all nearby enemies for ${finalDamage} damage${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
