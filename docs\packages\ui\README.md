# `packages/ui`

This package is a UI component library for the "Megami" project.

## Getting Started

First, run the development server:

```bash
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Architecture

The `ui` package is a Next.js project that serves as a component library. It provides a variety of reusable components, hooks, icons, and themes.

### Components

The components are organized into three main directories:

-   **`src/components/jules`**: This directory contains the core design system components, such as `Button`, `Card`, and `Avatar`.
-   **`src/components/registry`**: This directory contains more complex and specialized components, such as `AnimatedGradientBackground` and `MorphingDialog`.
-   **`src/components/blocks`**: This directory contains larger components that are composed of smaller components, such as `SettingsDialog`.

### Hooks

The `src/hooks` directory contains custom React hooks, such as `useMobile` for detecting mobile devices.

### Icons

The `src/icons` directory contains a set of icons from different libraries, such as Lucide and Radix.

### Themes

The `src/styles/themes` directory contains a variety of themes that can be applied to the application. Each theme is a CSS file that defines a set of CSS variables.
