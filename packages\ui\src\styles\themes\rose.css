.rose {
  --background: oklch(0.9692 0.0192 343.9344);
  --foreground: oklch(0.4426 0.1653 352.3762);
  --card: oklch(0.9837 0.0107 339.3288);
  --card-foreground: oklch(0.4426 0.1653 352.3762);
  --popover: oklch(0.9837 0.0107 339.3288);
  --popover-foreground: oklch(0.4426 0.1653 352.3762);
  --primary: oklch(0.6002 0.2414 0.1348);
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.923 0.0701 326.1273);
  --secondary-foreground: oklch(0.4426 0.1653 352.3762);
  --muted: oklch(0.9429 0.0363 344.2604);
  --muted-foreground: oklch(0.574 0.1732 352.0544);
  --accent: oklch(0.8766 0.0828 344.8849);
  --accent-foreground: oklch(0.4426 0.1653 352.3762);
  --destructive: oklch(0.5831 0.1911 6.341);
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.8881 0.0747 344.3866);
  --input: oklch(0.923 0.0701 326.1273);
  --ring: oklch(0.6002 0.2414 0.1348);
  --chart-1: oklch(0.6002 0.2414 0.1348);
  --chart-2: oklch(0.5979 0.175 345.0378);
  --chart-3: oklch(0.6009 0.1243 311.7958);
  --chart-4: oklch(0.5849 0.1178 283.2937);
  --chart-5: oklch(0.6479 0.1871 267.9684);
  --sidebar: oklch(0.9629 0.0227 345.7485);
  --sidebar-foreground: oklch(0.4426 0.1653 352.3762);
  --sidebar-primary: oklch(0.6002 0.2414 0.1348);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.8766 0.0828 344.8849);
  --sidebar-accent-foreground: oklch(0.4426 0.1653 352.3762);
  --sidebar-border: oklch(0.9311 0.0448 343.3135);
  --sidebar-ring: oklch(0.6002 0.2414 0.1348);
  --font-sans: Poppins, sans-serif;
  --font-serif: Playfair Display, serif;
  --font-mono: Space Mono, monospace;
  --radius: 0.5rem;
  --shadow-2xs: 0px 3px 0px 0px hsl(330 70% 30% / 0.09);
  --shadow-xs: 0px 3px 0px 0px hsl(330 70% 30% / 0.09);
  --shadow-sm:
    0px 3px 0px 0px hsl(330 70% 30% / 0.18), 0px 1px 2px -1px
    hsl(330 70% 30% / 0.18);
  --shadow:
    0px 3px 0px 0px hsl(330 70% 30% / 0.18), 0px 1px 2px -1px
    hsl(330 70% 30% / 0.18);
  --shadow-md:
    0px 3px 0px 0px hsl(330 70% 30% / 0.18), 0px 2px 4px -1px
    hsl(330 70% 30% / 0.18);
  --shadow-lg:
    0px 3px 0px 0px hsl(330 70% 30% / 0.18), 0px 4px 6px -1px
    hsl(330 70% 30% / 0.18);
  --shadow-xl:
    0px 3px 0px 0px hsl(330 70% 30% / 0.18), 0px 8px 10px -1px
    hsl(330 70% 30% / 0.18);
  --shadow-2xl: 0px 3px 0px 0px hsl(330 70% 30% / 0.45);
}

.rose-dark {
  --background: oklch(0.1808 0.0535 313.7159);
  --foreground: oklch(0.8624 0.1307 326.6356);
  --card: oklch(0.2398 0.0661 313.2337);
  --card-foreground: oklch(0.8624 0.1307 326.6356);
  --popover: oklch(0.2398 0.0661 313.2337);
  --popover-foreground: oklch(0.8624 0.1307 326.6356);
  --primary: oklch(0.7543 0.2319 332.0212);
  --primary-foreground: oklch(0.1608 0.0493 327.5673);
  --secondary: oklch(0.3184 0.0915 319.6465);
  --secondary-foreground: oklch(0.8624 0.1307 326.6356);
  --muted: oklch(0.2701 0.077 312.3525);
  --muted-foreground: oklch(0.7116 0.1623 327.1132);
  --accent: oklch(0.3558 0.1201 325.7655);
  --accent-foreground: oklch(0.8624 0.1307 326.6356);
  --destructive: oklch(0.6539 0.2441 7.174);
  --destructive-foreground: oklch(0.9821 0 0);
  --border: oklch(0.328 0.1202 313.5393);
  --input: oklch(0.3184 0.0915 319.6465);
  --ring: oklch(0.7543 0.2319 332.0212);
  --chart-1: oklch(0.7543 0.2319 332.0212);
  --chart-2: oklch(0.6508 0.2159 317.6331);
  --chart-3: oklch(0.6249 0.2233 292.7656);
  --chart-4: oklch(0.6067 0.1649 278.7172);
  --chart-5: oklch(0.6235 0.2019 268.0521);
  --sidebar: oklch(0.1941 0.0504 311.3983);
  --sidebar-foreground: oklch(0.8624 0.1307 326.6356);
  --sidebar-primary: oklch(0.7543 0.2319 332.0212);
  --sidebar-primary-foreground: oklch(0.1608 0.0493 327.5673);
  --sidebar-accent: oklch(0.3558 0.1201 325.7655);
  --sidebar-accent-foreground: oklch(0.8624 0.1307 326.6356);
  --sidebar-border: oklch(0.328 0.1202 313.5393);
  --sidebar-ring: oklch(0.7543 0.2319 332.0212);
  --font-sans: Quicksand, sans-serif;
  --font-serif: Playfair Display, serif;
  --font-mono: Space Mono, monospace;
  --radius: 0.5rem;
  --shadow-2xs: 0px 3px 0px 0px hsl(300 80% 50% / 0.09);
  --shadow-xs: 0px 3px 0px 0px hsl(300 80% 50% / 0.09);
  --shadow-sm:
    0px 3px 0px 0px hsl(300 80% 50% / 0.18), 0px 1px 2px -1px
    hsl(300 80% 50% / 0.18);
  --shadow:
    0px 3px 0px 0px hsl(300 80% 50% / 0.18), 0px 1px 2px -1px
    hsl(300 80% 50% / 0.18);
  --shadow-md:
    0px 3px 0px 0px hsl(300 80% 50% / 0.18), 0px 2px 4px -1px
    hsl(300 80% 50% / 0.18);
  --shadow-lg:
    0px 3px 0px 0px hsl(300 80% 50% / 0.18), 0px 4px 6px -1px
    hsl(300 80% 50% / 0.18);
  --shadow-xl:
    0px 3px 0px 0px hsl(300 80% 50% / 0.18), 0px 8px 10px -1px
    hsl(300 80% 50% / 0.18);
  --shadow-2xl: 0px 3px 0px 0px hsl(300 80% 50% / 0.45);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
}
