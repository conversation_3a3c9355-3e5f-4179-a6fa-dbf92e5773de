'use client';

import { cn } from '@megami/ui/lib/utils';
import { motion } from 'framer-motion';
import Link from 'next/link';
import * as React from 'react';

export interface BlogPostCardProps extends React.HTMLAttributes<HTMLDivElement> {
  title: string;
  description: string;
  date: string;
  href: string;
}

const BlogPostCard = React.forwardRef<HTMLDivElement, BlogPostCardProps>(
  ({ className, title, description, date, href, ...props }, ref) => {
    const cardVariants = {
      hidden: { opacity: 0, y: 20 },
      visible: { opacity: 1, y: 0, transition: { duration: 0.5 } },
    };

    return (
      <motion.div
        animate="visible"
        className={cn('relative w-full', className)}
        initial="hidden"
        ref={ref}
        variants={cardVariants}
        {...props}
      >
        <Link href={href}>
          <div className="rounded-lg bg-gray-900/50 p-6 text-white backdrop-blur-sm transition-colors hover:bg-gray-800/50">
            <h3 className="font-bold text-xl">{title}</h3>
            <p className="mt-2 text-gray-400">{description}</p>
            <p className="mt-4 text-gray-500 text-sm">{date}</p>
          </div>
        </Link>
      </motion.div>
    );
  }
);
BlogPostCard.displayName = 'BlogPostCard';

export { BlogPostCard };
