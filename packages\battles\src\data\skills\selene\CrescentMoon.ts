import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Crescent Moon',
  name: 'Crescent Moon',
  element: '<PERSON><PERSON>',
  manaCost: 20,
  cooldown: 3,
  description: 'The caster\'s attacks become empowered by the crescent moon, dealing bonus magic damage.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Attack Up',
        name: 'Crescent Moon',
        duration: 2,
        sourceId: caster.id,
      },
      log: `${caster.name}'s attacks are empowered by the crescent moon.`,
    };
  },
});
