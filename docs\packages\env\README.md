# `packages/env`

This package is responsible for fetching and validating environment variables from <PERSON><PERSON><PERSON>.

## Installation

To install dependencies:

```bash
bun install
```

## Usage

To run:

```bash
bun run index.ts
```

---

## Architecture

The `env` package provides a simple and type-safe way to load environment variables from <PERSON><PERSON>ler.

### `defineVariables`

The `defineVariables` function fetches secrets from the Doppler API and validates them against a Zod schema.

**`defineVariables(token: string)`**

-   `token`: Your Doppler API token.
-   **Returns:** A promise that resolves to the parsed environment variables.

**Example:**

```typescript
import { defineVariables } from './index';

const env = await defineVariables('your-doppler-token');

console.log(env.DATABASE_URL.computed);
```

### Environment Schema

The environment variables are validated against a Zod schema defined in `src/lib/schema.ts`. This ensures that all required environment variables are present and have the correct shape. The schema is defined using the `DOPPLER_SECRET` schema, which expects an object with `computed`, `raw`, and optional `note` properties.
