import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Dance of the Blade',
  name: 'Dance of the Blade',
  element: 'Selene',
  manaCost: 30,
  cooldown: 5,
  description: 'The caster performs a deadly dance, striking all nearby enemies with their blade.',
  execute: (caster, target, formulas) => {
    // This would require a more complex targeting system.
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const finalDamage = Math.round(damage * 1.3);
    return {
      damage: finalDamage,
      isCritical,
      log: `${caster.name} performs a Dance of the Blade, striking all nearby enemies for ${finalDamage} damage${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
