import { build } from 'esbuild';

const NODE_BUILT_INS = [
  'assert',
  'async_hooks',
  'buffer',
  'child_process',
  'cluster',
  'console',
  'constants',
  'crypto',
  'dgram',
  'diagnostics_channel',
  'dns',
  'domain',
  'events',
  'fs',
  'http',
  'http2',
  'https',
  'inspector',
  'module',
  'net',
  'os',
  'path',
  'perf_hooks',
  'process',
  'punycode',
  'querystring',
  'readline',
  'repl',
  'stream',
  'string_decoder',
  'sys',
  'timers',
  'tls',
  'trace_events',
  'tty',
  'url',
  'util',
  'v8',
  'vm',
  'wasi',
  'worker_threads',
  'zlib',
  // Node.js 14+ modules
  'fs/promises',
  'stream/promises',
  'timers/promises',
  // Node.js 16+ modules
  'stream/web',
  // Node.js 18+ modules
  'node:test',
  'test',
  // Additional built-ins
  'assert/strict',
  'dns/promises',
  'fs/promises',
  'stream/consumers',
  'stream/promises',
  'stream/web',
  'timers/promises',
  'util/types',
  'v8/serialize',
  'worker_threads',
];

const BUILT_INS = [...NODE_BUILT_INS.map((pkg) => `node:${pkg}`), ...NODE_BUILT_INS].flat();

const _results = await build({
  entryPoints: ['./src/index.ts'],
  format: 'esm',
  minify: true,
  outdir: 'dist',
  splitting: false,
  target: 'ES2024',
  platform: 'browser',
  bundle: true,
  external: ['sharp', '@napi-rs/canvas', ...BUILT_INS],
});
