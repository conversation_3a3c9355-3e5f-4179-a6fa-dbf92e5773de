import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Pyre',
  name: '<PERSON><PERSON>',
  element: 'Sirius',
  manaCost: 35,
  cooldown: 5,
  description: 'The caster builds a pyre, which can be ignited to deal massive damage to all enemies.',
  execute: (caster, target, formulas) => {
    // This would require a system for creating and interacting with objects on the battlefield.
    return {
      log: `${caster.name} builds a pyre.`,
    };
  },
});
