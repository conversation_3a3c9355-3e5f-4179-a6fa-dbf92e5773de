import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Meteor Shower',
  name: 'Meteor Shower',
  element: 'Sirius',
  manaCost: 60,
  cooldown: 8,
  description: 'Calls down a shower of meteors, damaging all enemies in an area over time.',
  execute: (caster, target, formulas) => {
    // This would require a more complex targeting and status effect system.
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const finalDamage = Math.round(damage * 1.8);
    return {
      damage: finalDamage,
      isCritical,
      log: `${caster.name} calls down a Meteor Shower, damaging all enemies for ${finalDamage} damage${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
