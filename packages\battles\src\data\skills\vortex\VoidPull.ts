import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Void Pull',
  name: 'Void Pull',
  element: 'Vortex',
  manaCost: 12,
  cooldown: 1,
  description: 'Pull the target into the void, dealing damage and reducing their speed.',
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    return {
      damage: Math.round(damage * 1.1),
      isCritical,
      appliedEffect: {
        id: 'void_pull',
        name: 'Void Pull',
        duration: 2,
        potency: -6, // -6 speed
        sourceId: caster.id,
      },
      log: `${caster.name} pulls ${target.name} into the void for ${Math.round(damage * 1.1)} damage${isCritical ? ' (CRIT!)' : ''} and slows them down!`,
    };
  },
});
