import { getObject, translate } from '@megami/locale';
import { Message<PERSON>lags, SeparatorBuilder, SeparatorSpacingSize, TextDisplayBuilder } from 'discord.js';
import { defineCommand } from '../handlers/command';
import { MegamiContainer } from '../helpers/containers';
import { defer } from '../helpers/defer';
import { MegamiEmbed } from '../helpers/embed';
import { formatters } from '../helpers/formatters';

export default defineCommand((builder) => ({
  builder: builder
    .setName(translate('commands.register.name'))
    .setNameLocalizations(getObject('commands.register.name'))
    .setDescription(translate('commands.register.description'))
    .setDescriptionLocalizations(getObject('commands.register.description')),
  config: {},
  execute: async (interaction) => {
    await defer(interaction, false);

    // Check if user is already registered
    const exists = await interaction.client.database.users.get(interaction.user.id);
    if (exists) {
      const info: Record<string, string> = {};

      info.status = formatters.lists([
        `**Level:** ${exists.level}`,
        `**Experience:** ${exists.experience.toLocaleString()} XP`,
        `**Joined:** <t:${Math.floor(exists.createdAt.getTime() / 1000)}:R>`,
      ]);

      const riftsAmount = await interaction.client.database.users.getRifts(exists.id);
      info.rifts = formatters.lists([`**Rifts:** ${riftsAmount}`]);

      info.streaks = formatters.lists([
        `**Daily:** ${exists.activity.daily.streak} day${exists.activity.daily.streak !== 1 ? 's' : ''}`,
        `**Weekly:** ${exists.activity.weekly.streak} week${exists.activity.weekly.streak !== 1 ? 's' : ''}`,
        `**Monthly:** ${exists.activity.monthly.streak} month${exists.activity.monthly.streak !== 1 ? 's' : ''}`,
      ]);

      const container = new MegamiContainer([
        new TextDisplayBuilder().setContent(translate('errors.user.registered.title')),
        new TextDisplayBuilder().setContent(translate('errors.user.registered.description')),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent('Your Current Status'),
        new TextDisplayBuilder().setContent(info.status),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent('Your Current Rift Levels'),
        new TextDisplayBuilder().setContent(info.rifts),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent('Your Current Activity Streaks'),
        new TextDisplayBuilder().setContent(info.streaks),
      ]);

      return await interaction.editReply({
        components: [container],
        flags: MessageFlags.IsComponentsV2,
      });
    }

    try {
      const newUser = await interaction.client.database.users.create(interaction.user.id);
      const riftsAmount = await interaction.client.database.users.getRifts(newUser.id);

      const container = new MegamiContainer([
        new TextDisplayBuilder().setContent('Welcome to the Realm of Megami!'),
        new TextDisplayBuilder().setContent(
          `Ah, ${interaction.user.displayName}... how delightful. You've stepped into my domain, and I must say, you have exquisite timing.`
        ),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent('Your Journey Begins'),
        new TextDisplayBuilder().setContent(
          `**Level:** ${newUser.level}\n**Experience:** ${newUser.experience.toLocaleString()} XP\n**Starting Rift Levels:** ${riftsAmount} Rift`
        ),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent('What Awaits You'),
        new TextDisplayBuilder().setContent(
          '• **Daily Rewards** - Return each day for gifts\n• **Character Collection** - Summon powerful allies\n• **Elemental Battles** - Master the four elements\n• **Progressive Growth** - Level up and unlock features'
        ),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent('Next Steps'),
        new TextDisplayBuilder().setContent(
          '• Use `/daily` to claim your first reward\n• Explore other commands to discover more\n• Build your collection and grow stronger\n• Unlock weekly rewards at **Level 3**\n• Unlock monthly rewards at **Level 5**'
        ),
      ]);

      await interaction.editReply({ components: [container], flags: MessageFlags.IsComponentsV2 });
    } catch (error) {
      interaction.client.logger.info('Error creating the user', error);
      await interaction.editReply({
        embeds: [
          new MegamiEmbed()
            .setTitle(translate('errors.general.unknown.title'))
            .setDescription(translate('errors.general.unknown.description')),
        ],
      });
    }
  },
}));
