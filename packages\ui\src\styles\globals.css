@import "tailwindcss";
@import "./themes.css";

@source "../../../apps/**/*.{ts,tsx}";
@source "../../../components/**/*.{ts,tsx}";
@source "../**/*.{ts,tsx}";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);

  --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
  --tracking-tight: calc(var(--tracking-normal) - 0.025em);
  --tracking-normal: var(--tracking-normal);
  --tracking-wide: calc(var(--tracking-normal) + 0.025em);
  --tracking-wider: calc(var(--tracking-normal) + 0.05em);
  --tracking-widest: calc(var(--tracking-normal) + 0.1em);
  --animate-rainbow: rainbow var(--speed, 2s) infinite linear;
  --color-color-5: var(----color-5);
  --color-color-4: var(----color-4);
  --color-color-3: var(----color-3);
  --color-color-2: var(----color-2);
  --color-color-1: var(----color-1);
  --component-active-color-default: var(----component-active-color-default);
  --component-line-inactive-color: var(----component-line-inactive-color);
  --component-active-bg: var(----component-active-bg);
  --component-shadow: var(----component-shadow);
  --component-bg: var(----component-bg);
  --component-inactive-color: var(----component-inactive-color);

  @keyframes rainbow {
    0% {
      background-position: 0%;
    }

    100% {
      background-position: 200%;
    }
  }

  @keyframes rainbow {
    0% {
      background-position: 0%;
    }

    100% {
      background-position: 200%;
    }
  }
  @keyframes iconBounce {
    0%,
    100% {
      transform: translateY(0);
    }
    20% {
      transform: translateY(-0.3em);
    }
    40% {
      transform: translateY(0);
    }
    60% {
      transform: translateY(-0.1em);
    }
    80% {
      transform: translateY(0);
    }
  }
}

@theme inline {
  --animate-rainbow: rainbow var(--speed, 2s) infinite linear;
  --color-color-5: var(----color-5);
  --color-color-4: var(----color-4);
  --color-color-3: var(----color-3);
  --color-color-2: var(----color-2);
  --color-color-1: var(----color-1);

  @keyframes rainbow {
    0% {
      background-position: 0%;
    }

    100% {
      background-position: 200%;
    }
  }

  @keyframes rainbow {
    0% {
      background-position: 0%;
    }

    100% {
      background-position: 200%;
    }
  }

  --animate-shiny-text: shiny-text 8s infinite;

  @keyframes shiny-text {
    0%,
    90%,
    100% {
      background-position: calc(-100% - var(--shiny-width)) 0;
    }

    30%,
    60% {
      background-position: calc(100% + var(--shiny-width)) 0;
    }
  }

  @keyframes shiny-text {
    0%,
    90%,
    100% {
      background-position: calc(-100% - var(--shiny-width)) 0;
    }

    30%,
    60% {
      background-position: calc(100% + var(--shiny-width)) 0;
    }
  }

  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;

  @keyframes accordion-down {
    from {
      height: 0;
    }

    to {
      height: var(--radix-accordion-content-height);
    }
  }

  @keyframes accordion-up {
    from {
      height: var(--radix-accordion-content-height);
    }

    to {
      height: 0;
    }
  }

  @keyframes accordion-down {
    from {
      height: 0;
    }

    to {
      height: var(--radix-accordion-content-height);
    }
  }

  @keyframes accordion-up {
    from {
      height: var(--radix-accordion-content-height);
    }

    to {
      height: 0;
    }
  }

  --animate-star-movement-bottom: star-movement-bottom linear infinite alternate;
  --animate-star-movement-top: star-movement-top linear infinite alternate;

  @keyframes star-movement-bottom {
    0% {
      transform: translate(0%, 0%);
      opacity: 1;
    }

    100% {
      transform: translate(-100%, 0%);
      opacity: 0;
    }
  }

  @keyframes star-movement-top {
    0% {
      transform: translate(0%, 0%);
      opacity: 1;
    }

    100% {
      transform: translate(100%, 0%);
      opacity: 0;
    }
  }

  @keyframes star-movement-bottom {
    0% {
      transform: translate(0%, 0%);
      opacity: 1;
    }

    100% {
      transform: translate(-100%, 0%);
      opacity: 0;
    }
  }

  @keyframes star-movement-top {
    0% {
      transform: translate(0%, 0%);
      opacity: 1;
    }

    100% {
      transform: translate(100%, 0%);
      opacity: 0;
    }
  }
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

:root {
  --component-inactive-color: var(--muted-foreground);
  --component-bg: var(--card);
  --component-shadow: var(--border);
  --component-active-bg: var(--secondary);
  --component-line-inactive-color: var(--border);
  --component-active-color-default: var(--accent-foreground);
  --sidebar: hsl(0 0% 98%);
  --sidebar-foreground: hsl(240 5.3% 26.1%);
  --sidebar-primary: hsl(240 5.9% 10%);
  --sidebar-primary-foreground: hsl(0 0% 98%);
  --sidebar-accent: hsl(240 4.8% 95.9%);
  --sidebar-accent-foreground: hsl(240 5.9% 10%);
  --sidebar-border: hsl(220 13% 91%);
  --sidebar-ring: hsl(217.2 91.2% 59.8%);
}

.dark {
  --component-inactive-color: var(--muted-foreground);
  --component-bg: var(--card);
  --component-shadow: var(--border);
  --component-active-bg: var(--secondary);
  --component-line-inactive-color: var(--muted-foreground);
  --component-active-color-default: var(--accent-foreground);
  --sidebar: hsl(240 5.9% 10%);
  --sidebar-foreground: hsl(240 4.8% 95.9%);
  --sidebar-primary: hsl(224.3 76.3% 48%);
  --sidebar-primary-foreground: hsl(0 0% 100%);
  --sidebar-accent: hsl(240 3.7% 15.9%);
  --sidebar-accent-foreground: hsl(240 4.8% 95.9%);
  --sidebar-border: hsl(240 3.7% 15.9%);
  --sidebar-ring: hsl(217.2 91.2% 59.8%);
}
