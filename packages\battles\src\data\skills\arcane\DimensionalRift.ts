import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Dimensional Rift',
  name: 'Dimensional Rift',
  element: 'Arcane',
  manaCost: 40,
  cooldown: 5,
  description: 'Opens a rift to another dimension, dealing massive damage to a single target.',
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const finalDamage = Math.round(damage * 2.5);
    return {
      damage: finalDamage,
      isCritical,
      log: `${caster.name} opens a Dimensional Rift, blasting ${target.name} for ${finalDamage} damage${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
