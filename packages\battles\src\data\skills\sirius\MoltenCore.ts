import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Molten Core',
  name: 'Molten Core',
  element: 'Sirius',
  manaCost: 40,
  cooldown: 6,
  description: 'The caster becomes a molten core, increasing their defense and burning any enemy that attacks them.',
  execute: (caster, target, formulas) => {
    // This would require a status effect system to handle the buff and burn.
    return {
      log: `${caster.name} becomes a molten core.`,
    };
  },
});
