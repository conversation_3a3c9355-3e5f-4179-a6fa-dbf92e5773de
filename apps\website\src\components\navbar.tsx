'use client';

import { MenuBar } from '@megami/ui/components/registry/bottom-menu';
import { MorphingDialog, useMorphingDialog } from '@megami/ui/components/registry/morphing-dialog';
import { Book, Code, FileClock, Home, SettingsIcon, Star, Users } from '@megami/ui/icons/lucide';
import { useRouter } from 'next/navigation';

import { Settings } from './settings';

export function NavbarOuter({ children }: { children: React.ReactNode }) {
  return (
    <MorphingDialog
      transition={{
        type: 'spring',
        bounce: 0,
        duration: 0.3,
      }}
    >
      {children}
    </MorphingDialog>
  );
}

export function NavbarInner() {
  const { isOpen, setIsOpen } = useMorphingDialog();
  const router = useRouter();

  const ITEMS = [
    { label: 'Home', icon: Home, action: () => router.push('/') },
    { label: 'Commands', icon: Code, action: () => router.push('/commands') },
    { label: 'Features', icon: Star, action: () => router.push('/features') },
    { label: 'Blog', icon: Book, action: () => router.push('/blog') },
    { label: 'Changelog', icon: FileClock, action: () => router.push('/changelog') },
    { label: 'Team', icon: Users, action: () => router.push('/team') },
    {
      label: 'Settings',
      icon: SettingsIcon,
      action: () => setIsOpen((open) => !open),
    },
  ];

  return (
    <>
      <div className="fixed right-0 bottom-0 left-0 flex items-center justify-center p-6">
        <MenuBar items={ITEMS} />
      </div>

      <Settings isOpen={isOpen} />
    </>
  );
}

export function Navbar() {
  return (
    <NavbarOuter>
      <NavbarInner />
    </NavbarOuter>
  );
}
