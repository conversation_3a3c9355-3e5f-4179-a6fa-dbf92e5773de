import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Antimatter',
  name: 'Antimatter',
  element: 'Vortex',
  manaCost: 40,
  cooldown: 6,
  description: 'The target is infused with antimatter, causing them to take increased damage from all sources.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Damage Amp',
        name: 'Antimatter',
        duration: 3,
        sourceId: caster.id,
      },
      log: `${caster.name} infuses ${target.name} with antimatter.`,
    };
  },
});
