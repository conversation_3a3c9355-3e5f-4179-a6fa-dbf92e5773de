import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Pocket Dimension',
  name: 'Pocket Dimension',
  element: 'Vortex',
  manaCost: 30,
  cooldown: 5,
  description: 'The caster banishes the target to a pocket dimension for a short time.',
  execute: (caster, target, formulas) => {
    // This would require a status effect system to handle the banish.
    return {
      log: `${caster.name} banishes ${target.name} to a pocket dimension.`,
    };
  },
});
