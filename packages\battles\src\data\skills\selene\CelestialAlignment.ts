import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Celestial Alignment',
  name: 'Celestial Alignment',
  element: 'Selene',
  manaCost: 50,
  cooldown: 7,
  description: 'The caster aligns with the celestial bodies, increasing the power of all their Selene skills.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Crit Chance Up',
        name: 'Celestial Alignment',
        duration: 4,
        sourceId: caster.id,
      },
      log: `${caster.name} aligns with the celestial bodies.`,
    };
  },
});
