/* biome-ignore-all lint/suspicious/useAwait: We need this to be a promise */
import * as prompt from '@clack/prompts';
import ffmpeg from 'fluent-ffmpeg';

/**
 * Removes solid black background from a video using FFmpeg colorkey filter.
 * Outputs a `.mov` file with alpha transparency.
 *
 * @param input Path to the input video (must have black background)
 * @param output Output path (recommended: `.mov` or `.webm` for transparency)
 * @param options Similarity and blend thresholds for the colorkey filter
 */
export async function background(
  input: string,
  output: string,
  options: { similarity?: number; blend?: number } = {}
): Promise<void> {
  const similarity = options.similarity ?? 0.0005;
  const blend = options.blend ?? 0.2;

  return new Promise((resolve, reject) => {
    ffmpeg(input)
      .videoFilters([`chromakey=0x000000:${similarity}:${blend}`, 'format=rgba'])
      .outputOptions([
        '-c:v prores_ks',
        '-profile:v 4',
        '-an', // No audio
        '-pix_fmt yuva444p10le',
        '-r 30',
      ])
      .format('mov')
      .on('start', (commandLine) => {
        prompt.log.info(`Running: ${commandLine}`);
      })
      .on('error', (err, _stdout, stderr) => {
        prompt.log.error(`FFmpeg error: ${err.message}`);
        prompt.log.error(`FFmpeg stderr: ${stderr}`);
        reject(err);
      })
      .on('end', () => {
        prompt.log.info(`Black background removed: ${output}`);
        resolve();
      })
      .save(output);
  });
}
