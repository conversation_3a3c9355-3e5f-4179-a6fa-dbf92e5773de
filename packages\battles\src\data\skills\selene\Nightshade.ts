import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Nightshade',
  name: 'Nightshad<PERSON>',
  element: 'Selene',
  manaCost: 20,
  cooldown: 3,
  description: 'The caster\'s attacks are coated in nightshade, causing their basic attacks to deal bonus magic damage and apply a poison that reduces the target\'s healing received.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Nightshade Effect',
        name: 'Nightshade',
        duration: 3,
        sourceId: caster.id,
      },
      log: `${caster.name}'s attacks are coated in nightshade.`,
    };
  },
});
