/* biome-ignore-all lint/suspicious/noAssignInExpressions: We need to assign the environment variables to process.env */

import { MegamiCanvasClient } from '@megami/canvas';
import { CDN } from '@megami/cdn';
import { defineVariables } from '@megami/env';
import type { ClientOptions } from 'discord.js';
import type { ClusterManager } from 'discord-hybrid-sharding';
import type { Logger } from 'winston';
import { MegamiClient } from './client';
import { logger } from './logger';

export interface ServerOptions {
  secrets: {
    DOPPLER_TOKEN: string;
  };
  client?: {
    options?: ClientOptions;
  };
}

export class Server {
  public options: ServerOptions;
  public logger: Logger = logger;
  public client: MegamiClient;
  public cdn: CDN;
  public canvas: MegamiCanvasClient;

  // @ts-expect-error, we define this on a later stage
  public cluster: ClusterManager;

  constructor(options: ServerOptions) {
    this.options = options;
    this.client = new MegamiClient(options.client?.options);
    this.cdn = new CDN({
      cloud_name: process.env.CLOUDINARY_CLOUD_NAME || '',
      api_key: process.env.CLOUDINARY_API_KEY || '',
      api_secret: process.env.CLOUDINARY_API_SECRET || '',
    });
    this.canvas = new MegamiCanvasClient();
  }

  async start() {
    const { DOPPLER_TOKEN } = this.options.secrets;

    if (!DOPPLER_TOKEN) {
      throw new Error('DOPPLER_TOKEN is required');
    }

    const environment = await defineVariables(DOPPLER_TOKEN);

    for (const [key, value] of Object.entries(environment)) {
      value.computed ? (process.env[key] = value.computed) : (process.env[key] = value.raw);
    }

    this.cdn.configure({
      cloud_name: process.env.CLOUDINARY_CLOUD_NAME || '',
      api_key: process.env.CLOUDINARY_API_KEY || '',
      api_secret: process.env.CLOUDINARY_API_SECRET || '',
    });

    // Pass CDN to client
    this.client.cdn = this.cdn;

    await this.client.start(process.env.DISCORD_CLIENT_TOKEN || '');
  }
}
