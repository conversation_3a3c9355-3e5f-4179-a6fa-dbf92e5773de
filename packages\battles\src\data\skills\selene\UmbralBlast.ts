import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Umbral Blast',
  name: 'Umbral Blast',
  element: 'Selene',
  manaCost: 35,
  cooldown: 4,
  description: 'A blast of dark energy that damages and confuses the target.',
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const finalDamage = Math.round(damage * 1.4);
    // This would also require a status effect system to handle confusion.
    return {
      damage: finalDamage,
      isCritical,
      log: `${caster.name} blasts ${target.name} with Umbral Blast for ${finalDamage} damage${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
