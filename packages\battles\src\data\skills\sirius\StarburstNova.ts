import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Starburst Nova',
  name: 'Starburst Nova',
  element: 'Sirius',
  manaCost: 32,
  cooldown: 5,
  description: 'Explode with stellar energy, dealing massive damage but consuming health.',
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const novaDamage = Math.round(damage * 2.3);
    const selfDamage = Math.round(caster.stats.maxHealth * 0.15); // 15% of max health
    caster.stats.health = Math.max(0, caster.stats.health - selfDamage);

    return {
      damage: novaDamage,
      isCritical,
      log: `${caster.name} explodes with Starburst Nova, dealing ${novaDamage} damage${isCritical ? ' (CRIT!)' : ''} to ${target.name} but taking ${selfDamage} recoil damage!`,
    };
  },
});
