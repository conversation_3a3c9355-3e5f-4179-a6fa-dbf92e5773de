import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Spirit Venom',
  name: 'Spirit Venom',
  element: 'Selene',
  manaCost: 20,
  cooldown: 3,
  description: 'The caster\'s attacks are coated in spirit venom, causing their basic attacks to deal bonus magic damage and apply a poison that damages the target over time.',
  execute: (caster, target, formulas) => {
    // This would require a way to modify basic attacks and a status effect system to handle the poison.
    return {
      log: `${caster.name}'s attacks are coated in spirit venom.`,
    };
  },
});
