import { boolean, index, integer, pgTable, text, timestamp, uniqueIndex, uuid } from 'drizzle-orm/pg-core';
import { characters } from './characters';
import { skins } from './skins';
import { users } from './users';

export const vessels = pgTable(
  'vessels',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    userId: text('user_id')
      .references(() => users.id, { onDelete: 'cascade' })
      .notNull(),
    characterId: uuid('character_id')
      .references(() => characters.id)
      .notNull(),
    skinId: uuid('skin_id')
      .references(() => skins.id)
      .notNull(),
    level: integer('level').default(1).notNull(),
    experience: integer('experience').default(0).notNull(),
    constellation: integer('constellation').default(0).notNull(), // Dupes/merges
    isFavorite: boolean('is_favorite').default(false).notNull(),
    obtainedAt: timestamp('obtained_at').defaultNow().notNull(),
  },
  (table) => [
    index('user_characters_user_idx').on(table.userId),
    index('user_characters_character_idx').on(table.characterId),
    uniqueIndex('user_characters_unique_idx').on(table.userId, table.characterId),
  ]
);
