import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Runic Blade',
  name: 'Runic Blade',
  element: 'Arcane',
  manaCost: 10,
  cooldown: 1,
  description: 'The caster\'s weapon is empowered with arcane energy, causing their next basic attack to deal bonus magic damage.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Attack Up',
        name: 'Runic Blade',
        duration: 1,
        sourceId: caster.id,
      },
      log: `${caster.name} empowers their weapon with a Runic Blade.`,
    };
  },
});
