import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Spacial Rend',
  name: 'Spacial Rend',
  element: 'Vortex',
  manaCost: 30,
  cooldown: 5,
  description: 'Rends the fabric of space, dealing damage and silencing the target.',
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const finalDamage = Math.round(damage * 1.4);
    // This would also require a status effect system to handle the silence.
    return {
      damage: finalDamage,
      isCritical,
      log: `${caster.name} rends the fabric of space, dealing ${finalDamage} damage to ${target.name} and silencing them${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
