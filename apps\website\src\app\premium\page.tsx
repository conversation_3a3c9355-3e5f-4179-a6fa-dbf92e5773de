'use client';

import { motion } from 'framer-motion';
import { Navbar } from '@/components/navbar';
import { PremiumCard } from '@megami/ui/components/jules/premium-card';
import { Gem, Package, Crown } from 'lucide-react';

const premiumOptions = [
  {
    title: 'Crown Packs',
    description: 'Purchase Crowns to use in the game.',
    href: '/premium/crown-packs',
    icon: <Gem size={48} />,
  },
  {
    title: 'Special Packs',
    description: 'Get a head start with our one-time special packs.',
    href: '/premium/special-packs',
    icon: <Package size={48} />,
  },
  {
    title: 'Subscriptions',
    description: 'Subscribe to get exclusive benefits and a steady supply of Crowns.',
    href: '/premium/subscriptions',
    icon: <Crown size={48} />,
  },
];

export default function PremiumPage() {
  return (
    <main className="min-h-screen w-full bg-black text-white">
      <Navbar />
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1 }}
        className="container mx-auto px-4 py-8"
      >
        <h1 className="text-5xl font-bold text-center mb-4">Premium Shop</h1>
        <p className="text-lg text-gray-400 text-center mb-12">
          Support the development of Megami and get some nice perks in return!
        </p>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {premiumOptions.map((option, i) => (
            <motion.div
              key={option.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: i * 0.1 }}
            >
              <PremiumCard {...option} />
            </motion.div>
          ))}
        </div>
      </motion.div>
    </main>
  );
}
