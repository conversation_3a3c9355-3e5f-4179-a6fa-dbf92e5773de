import { existsSync, readdirSync, readFileSync, statSync } from 'node:fs';
import { join, resolve } from 'node:path';
import i18next from 'i18next';
import { DISCORD_LANGUAGE_CODES } from './discord';
import commands from './locales/en-US/commands.json' with { type: 'json' };
import errors from './locales/en-US/errors.json' with { type: 'json' };
import messages from './locales/en-US/messages.json' with { type: 'json' };

export type Commands = typeof commands;
export type Errors = typeof errors;
export type Messages = typeof messages;

export type LoadedResource = {
  commands: Commands;
  errors: Errors;
  messages: Messages;
};

export type DotNestedKeys<T> = {
  [K in keyof T & string]: T[K] extends object ? `${K}` | `${K}.${DotNestedKeys<T[K]>}` : `${K}`;
}[keyof T & string];

export type TranslationKey = DotNestedKeys<LoadedResource>;

export type DiscordLanguageCode = (typeof DISCORD_LANGUAGE_CODES)[number];

const LOCALES_DIR = resolve(__dirname, 'locales');

function loadLoadedResources() {
  // @ts-expect-error - ignore the error over here
  const resources: Record<DiscordLanguageCode, LoadedResource> = {};

  for (const code of DISCORD_LANGUAGE_CODES) {
    const LANG_DIR = join(LOCALES_DIR, code);
    if (!(existsSync(LANG_DIR) && statSync(LANG_DIR).isDirectory())) continue;
    const files = readdirSync(LANG_DIR).filter((f) => f.endsWith('.json'));

    let merged: Record<'commands' | 'messages' | 'errors', Record<string, unknown>> = {
      commands: {},
      messages: {},
      errors: {},
    };

    for (const file of files) {
      const FILE_PATH = join(LANG_DIR, file);
      try {
        const data = JSON.parse(readFileSync(FILE_PATH, 'utf8'));
        merged = { ...merged, [file.split('.')[0] as string]: data };
      } catch (_error) {
        // Do nothing.
      }
    }

    if (Object.keys(merged).length > 0) {
      // @ts-expect-error - ignore this.
      resources[code] = { translation: { ...merged } };
    }
  }

  return resources;
}

const resources = loadLoadedResources();

export type LoadedLoadedResource = ReturnType<typeof loadLoadedResources>;

const _locale = await i18next.init({
  lng: 'en-US',
  fallbackLng: 'en-US',
  resources,
  interpolation: { escapeValue: false },
});

export const translate = (key: TranslationKey, options?: Parameters<typeof i18next.t>[1]) => {
  // @ts-expect-error - ignore this shit
  const output: string = i18next.t(key, options);
  if (output.length > 100 && key.includes('description')) {
    return `${output.substring(0, 97)}...`;
  }

  return output;
};
