import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Corona Blast',
  name: 'Corona Blast',
  element: 'Sirius',
  manaCost: 24,
  cooldown: 3,
  description: 'Blast the target with corona energy, dealing damage and applying a burning DoT effect.',
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);

    return {
      damage: Math.round(damage * 1.3),
      isCritical,
      appliedEffect: {
        id: 'corona_burn',
        name: 'Corona Burn',
        duration: 3,
        potency: Math.round(caster.stats.attack * 0.35),
        sourceId: caster.id,
      },
      log: `${caster.name} blasts ${target.name} with Corona energy for ${Math.round(damage * 1.3)} damage${isCritical ? ' (CRIT!)' : ''} and ignites them!`,
    };
  },
});
