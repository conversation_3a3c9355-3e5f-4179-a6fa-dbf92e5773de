import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Wormhole',
  name: 'Wormhole',
  element: 'Vortex',
  manaCost: 25,
  cooldown: 4,
  description: 'Creates a wormhole that teleports the caster to a target location.',
  execute: (caster, target, formulas) => {
    // This would require a system for movement and positioning.
    return {
      log: `${caster.name} creates a Wormhole and teleports.`,
    };
  },
});
