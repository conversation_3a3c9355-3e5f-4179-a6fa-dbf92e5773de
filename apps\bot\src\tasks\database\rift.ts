import { sql } from '@megami/database';
import { defineTask } from '../../structure/scheduler';

export default defineTask({
  id: 'rift',
  name: 'Rift Regain',
  expression: '0 */5 * * * *',
  enabled: true,
  execute: async (client) => {
    const currency = await client.database.currencies.getCurrencyByName('Rifts');
    if (!currency) {
      client.logger.error('Rifts currency not found in database');
      return;
    }

    // Update rifts with effects applied using a single SQL query
    // This calculates effects for each user and applies them concurrently
    await client.database.instance.execute(sql`
      WITH user_effects AS (
        SELECT
          b.user_id,
          b.amount as current_rifts,
          -- Calculate multiplier from RIFT_MULTIPLIER effects
          COALESCE(
            GREATEST(1,
              SUM(CASE
                WHEN e.type = 'RIFT_MULTIPLIER' AND (e.data->>'stackable')::boolean = true
                THEN (COALESCE((e.data->>'multiplier')::numeric, 1) - 1) * e.stacks
                WHEN e.type = 'RIFT_MULTIPLIER' AND (e.data->>'stackable')::boolean != true
                THEN GREATEST(0, (COALESCE((e.data->>'multiplier')::numeric, 1) - 1))
                ELSE 0
              END) + 1
            ), 1
          ) as multiplier,
          -- Calculate bonus from RIFT_BONUS effects
          COALESCE(
            SUM(CASE
              WHEN e.type = 'RIFT_BONUS'
              THEN COALESCE((e.data->>'bonus')::numeric, 0) * e.stacks
              ELSE 0
            END), 0
          ) as bonus
        FROM balances b
        LEFT JOIN effects e ON b.user_id = e.user_id
          AND e.type IN ('RIFT_MULTIPLIER', 'RIFT_BONUS')
          AND (e.expires_at IS NULL OR e.expires_at > NOW())
        WHERE b.currency_id = ${currency.id} AND b.amount < 100
        GROUP BY b.user_id, b.amount
      )
      UPDATE balances
      SET
        amount = LEAST(
          amount + FLOOR((10 * ue.multiplier) + ue.bonus)::integer,
          100
        ),
        updated_at = NOW()
      FROM user_effects ue
      WHERE balances.user_id = ue.user_id
        AND balances.currency_id = ${currency.id}
        AND balances.amount < 100;
    `);
  },
});
