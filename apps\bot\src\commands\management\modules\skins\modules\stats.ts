import { getRarityEmoji } from '@megami/config/lib/rewards';
import { getObject, translate } from '@megami/locale';
import { MessageFlags, SeparatorBuilder, SeparatorSpacingSize, TextDisplayBuilder } from 'discord.js';
import { defineSubCommand } from '../../../../../handlers/command';
import { MegamiContainer } from '../../../../../helpers/containers';
import { defer } from '../../../../../helpers/defer';

export default defineSubCommand((builder) => ({
  builder: builder
    .setName(translate('commands.management.modules.skins.modules.stats.name'))
    .setNameLocalizations(getObject('commands.management.modules.skins.modules.stats.name'))
    .setDescription(translate('commands.management.modules.skins.modules.stats.description'))
    .setDescriptionLocalizations(getObject('commands.management.modules.skins.modules.stats.description')),
  config: {},

  execute: async (interaction) => {
    await defer(interaction, true);

    try {
      // Get all skins statistics
      const [approvedSkins, pendingSkins] = await Promise.all([
        interaction.client.database.skins.getApproved(),
        interaction.client.database.skins.getPending(),
      ]);

      // Calculate statistics by rarity
      const approvedStats = {
        total: approvedSkins.length,
        Stardust: approvedSkins.filter((s) => s.rarity === 'Stardust').length,
        Moonlight: approvedSkins.filter((s) => s.rarity === 'Moonlight').length,
        Celestial: approvedSkins.filter((s) => s.rarity === 'Celestial').length,
      };

      const pendingStats = {
        total: pendingSkins.length,
        Stardust: pendingSkins.filter((s) => s.rarity === 'Stardust').length,
        Moonlight: pendingSkins.filter((s) => s.rarity === 'Moonlight').length,
        Celestial: pendingSkins.filter((s) => s.rarity === 'Celestial').length,
      };

      // Get unique contributors
      const allSkins = [...approvedSkins, ...pendingSkins];
      const uniqueContributors = new Set(allSkins.map((s) => s.authorId)).size;

      // Get top contributors (approved skins only)
      const contributorCounts = new Map<string, number>();
      for (const skin of approvedSkins) {
        contributorCounts.set(skin.authorId, (contributorCounts.get(skin.authorId) || 0) + 1);
      }

      const topContributors = Array.from(contributorCounts.entries())
        .sort(([, a], [, b]) => b - a)
        .slice(0, 5);

      // Build response
      const components = [
        new TextDisplayBuilder().setContent('📊 **Skin Collection Statistics**'),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),

        new TextDisplayBuilder().setContent('**✅ Approved Skins**'),
        new TextDisplayBuilder().setContent(`**Total:** ${approvedStats.total} skins`),
        new TextDisplayBuilder().setContent(
          `${getRarityEmoji('Stardust')} **Stardust:** ${approvedStats.Stardust} • ` +
            `${getRarityEmoji('Moonlight')} **Moonlight:** ${approvedStats.Moonlight} • ` +
            `${getRarityEmoji('Celestial')} **Celestial:** ${approvedStats.Celestial}`
        ),

        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),

        new TextDisplayBuilder().setContent('**🔄 Pending Approval**'),
        new TextDisplayBuilder().setContent(`**Total:** ${pendingStats.total} skins`),
        new TextDisplayBuilder().setContent(
          `${getRarityEmoji('Stardust')} **Stardust:** ${pendingStats.Stardust} • ` +
            `${getRarityEmoji('Moonlight')} **Moonlight:** ${pendingStats.Moonlight} • ` +
            `${getRarityEmoji('Celestial')} **Celestial:** ${pendingStats.Celestial}`
        ),

        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),

        new TextDisplayBuilder().setContent('**👥 Community Stats**'),
        new TextDisplayBuilder().setContent(`**Total Contributors:** ${uniqueContributors} artists`),
      ];

      // Add top contributors if any exist
      if (topContributors.length > 0) {
        components.push(
          new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
          new TextDisplayBuilder().setContent('**🏆 Top Contributors (Approved)**')
        );

        for (let i = 0; i < topContributors.length; i++) {
          const [authorId, count] = topContributors[i]!;
          const medal = ['🥇', '🥈', '🥉', '🏅', '🏅'][i] || '🏅';
          components.push(new TextDisplayBuilder().setContent(`${medal} <@${authorId}>: ${count} skins`));
        }
      }

      // Add summary message
      components.push(
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent(
          pendingStats.total > 0
            ? `My collection grows beautifully, but ${pendingStats.total} submissions still await my discerning eye.`
            : 'My collection is perfectly curated - no submissions await my attention at the moment.'
        )
      );

      const container = new MegamiContainer(components);

      await interaction.editReply({
        components: [container],
        flags: MessageFlags.IsComponentsV2,
      });
    } catch (error) {
      const container = new MegamiContainer([
        new TextDisplayBuilder().setContent('❌ **Error Loading Statistics**'),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent(
          'I encountered an issue while gathering my collection statistics. Please try again later.'
        ),
      ]);

      await interaction.editReply({
        components: [container],
        flags: MessageFlags.IsComponentsV2,
      });

      interaction.client.logger.error('Error loading skin statistics:', error);
    }
  },
}));
