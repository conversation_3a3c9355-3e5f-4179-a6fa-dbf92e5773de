import { canVote } from '@megami/config/lib/voting';
import { translate } from '@megami/locale';
import { MessageFlags, SeparatorBuilder, SeparatorSpacingSize, TextDisplayBuilder } from 'discord.js';
import { defineCommand } from '../handlers/command';
import { MegamiContainer } from '../helpers/containers';
import { defer } from '../helpers/defer';

export default defineCommand((builder) => ({
  builder: builder.setName('vote').setDescription('Vote for the bot and claim your voting rewards'),

  config: {},

  execute: async (interaction) => {
    await defer(interaction, false);

    const user = await interaction.client.database.users.get(interaction.user.id);
    if (!user) {
      const container = new MegamiContainer([
        new TextDisplayBuilder().setContent('❌ **Not Registered**'),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent(translate('errors.user.unregistered.description')),
      ]);

      return await interaction.editReply({
        components: [container],
        flags: MessageFlags.IsComponentsV2,
      });
    }

    const check = canVote(user.lastVote);
    const container = new MegamiContainer([
      new TextDisplayBuilder().setContent(check.canVote ? '**You Can Vote!**' : '**Cannot Vote**'),
      new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
      new TextDisplayBuilder().setContent(
        check.canVote ? 'You can vote for the bot and claim your rewards!' : check.reason || 'You cannot vote yet.'
      ),
      new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Large),
      new TextDisplayBuilder().setContent('🗳️ **Vote Links**'),
      new TextDisplayBuilder().setContent('• [Top.gg](https://top.gg/bot/YOUR_BOT_ID/vote)'),
      new TextDisplayBuilder().setContent('• [Discord Bot List](https://discordbotlist.com/bots/YOUR_BOT_ID/upvote)'),
    ]);

    return await interaction.editReply({
      components: [container],
      flags: MessageFlags.IsComponentsV2,
    });
  },
}));
