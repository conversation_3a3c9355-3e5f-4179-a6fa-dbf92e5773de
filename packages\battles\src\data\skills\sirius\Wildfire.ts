import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Wildfire',
  name: 'Wildfire',
  element: 'Sirius',
  manaCost: 50,
  cooldown: 7,
  description: 'A wildfire spreads across the battlefield, damaging all enemies over time.',
  execute: (caster, target, formulas) => {
    // This would require a status effect system to handle the DoT.
    return {
      log: `${caster.name} starts a Wildfire.`,
    };
  },
});
