/* biome-ignore-all lint/suspicious/noConsole: Don't Need This */
/* biome-ignore-all lint/nursery/noAwaitInLoop: Don't Need This */
/* biome-ignore-all lint/suspicious/useAwait: Don't Need This */
import { Buffer } from 'node:buffer';
import fs from 'node:fs/promises';
import { createCanvas, type ImageData } from '@napi-rs/canvas';
import GIFEncoder from 'gifencoder';
import { decompressFrames, parseGIF } from 'gifuct-js';
import sharp from 'sharp';

interface FrameData {
  imageData: ImageData;
  delay: number;
  width: number;
  height: number;
}

interface ProcessedImage {
  frames: FrameData[];
  width: number;
  height: number;
  isAnimated: boolean;
}

interface CompositionOptions {
  outputFormat: 'gif' | 'webp';
  quality?: number;
  fps?: number;
  overlaySize?: number;
}

export class ProductionGifOverlayCompositor {
  private readonly OVERLAY_SIZE = 128;
  private readonly DEFAULT_DELAY = 100;
  private readonly MAX_FRAMES = 300;

  async composeWithOverlays(
    baseImagePath: string | Buffer,
    topLeftGifPath: string | Buffer,
    topRightGifPath: string | Buffer,
    options: CompositionOptions = { outputFormat: 'gif' }
  ): Promise<Buffer> {
    try {
      console.log('Starting composition process...');

      const [baseData, leftGifData, rightGifData] = await Promise.all([
        this.parseBaseImage(baseImagePath),
        this.parseGifInput(topLeftGifPath),
        this.parseGifInput(topRightGifPath),
      ]);

      console.log(
        `Base: ${baseData.frames.length} frames, Left GIF: ${leftGifData.frames.length} frames, Right GIF: ${rightGifData.frames.length} frames`
      );

      this.validateDimensions(baseData);

      const composedFrames = await this.composeFrames(baseData, leftGifData, rightGifData);

      console.log(`Composed ${composedFrames.length} frames`);

      if (options.outputFormat === 'webp') {
        return await this.createAnimatedWebP(composedFrames, baseData.width, baseData.height, options);
      }

      return await this.createAnimatedGif(composedFrames, baseData.width, baseData.height, options);
    } catch (error) {
      console.error('Composition failed:', error);
      throw new Error(`Composition failed: ${(error as Error).message}`);
    }
  }

  private async parseBaseImage(input: string | Buffer): Promise<ProcessedImage> {
    const buffer = typeof input === 'string' ? await fs.readFile(input) : input;

    if (this.isAnimatedWebP(buffer)) {
      return await this.parseAnimatedWebP(buffer);
    }

    return await this.parseStaticImage(buffer);
  }

  private async parseStaticImage(buffer: Buffer): Promise<ProcessedImage> {
    const image = await sharp(buffer);
    const metadata = await image.metadata();

    if (!(metadata.width && metadata.height)) {
      throw new Error('Could not determine image dimensions');
    }

    const { data, info } = await image.ensureAlpha().raw().toBuffer({ resolveWithObject: true });

    const canvas = createCanvas(info.width, info.height);
    const ctx = canvas.getContext('2d');
    const imageData = ctx.createImageData(info.width, info.height);
    imageData.data.set(data);

    return {
      frames: [
        {
          imageData,
          delay: this.DEFAULT_DELAY,
          width: info.width,
          height: info.height,
        },
      ],
      width: info.width,
      height: info.height,
      isAnimated: false,
    };
  }

  private async parseAnimatedWebP(buffer: Buffer): Promise<ProcessedImage> {
    try {
      const image = sharp(buffer, { animated: true });
      const metadata = await image.metadata();

      if (!(metadata.width && metadata.height)) {
        throw new Error('Could not determine WebP dimensions');
      }

      const { data } = await image.gif({ delay: [100] }).toBuffer({ resolveWithObject: true });

      return await this.parseGifBuffer(data);
    } catch (error) {
      console.warn('Failed to parse as animated WebP, treating as static:', (error as Error).message);
      return await this.parseStaticImage(buffer);
    }
  }

  private async parseGifInput(input: string | Buffer): Promise<ProcessedImage> {
    const buffer = typeof input === 'string' ? await fs.readFile(input) : input;
    return await this.parseGifBuffer(buffer);
  }

  private async parseGifBuffer(buffer: Buffer): Promise<ProcessedImage> {
    try {
      const gif = parseGIF(buffer as unknown as ArrayBuffer);
      const frames = decompressFrames(gif, true);

      if (frames.length === 0) {
        throw new Error('No frames found in GIF');
      }

      const limitedFrames = frames.slice(0, this.MAX_FRAMES);

      const processedFrames: FrameData[] = [];

      for (const frame of limitedFrames) {
        const canvas = createCanvas(frame.dims.width, frame.dims.height);
        const ctx = canvas.getContext('2d');
        const imageData = ctx.createImageData(frame.dims.width, frame.dims.height);

        // Copy frame data
        imageData.data.set(frame.patch);

        processedFrames.push({
          imageData,
          delay: frame.delay || this.DEFAULT_DELAY,
          width: frame.dims.width,
          height: frame.dims.height,
        });
      }

      return {
        frames: processedFrames,
        width: gif.lsd.width,
        height: gif.lsd.height,
        isAnimated: frames.length > 1,
      };
    } catch (error) {
      throw new Error(`Failed to parse GIF: ${(error as Error).message}`);
    }
  }

  private async composeFrames(
    baseData: ProcessedImage,
    leftGifData: ProcessedImage,
    rightGifData: ProcessedImage
  ): Promise<FrameData[]> {
    const maxFrames = Math.max(baseData.frames.length, leftGifData.frames.length, rightGifData.frames.length);

    const composedFrames: FrameData[] = [];
    const canvas = createCanvas(baseData.width, baseData.height);
    const ctx = canvas.getContext('2d');

    for (let i = 0; i < Math.min(maxFrames, this.MAX_FRAMES); i++) {
      const baseFrame = baseData.frames[i % baseData.frames.length];
      const leftFrame = leftGifData.frames[i % leftGifData.frames.length];
      const rightFrame = rightGifData.frames[i % rightGifData.frames.length];

      // Clear canvas
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // Draw base frame
      ctx.putImageData(baseFrame!.imageData, 0, 0);

      // Create and draw resized left overlay
      const leftOverlay = await this.resizeFrame(leftFrame!, this.OVERLAY_SIZE, this.OVERLAY_SIZE);
      ctx.putImageData(leftOverlay, 0, 0);

      // Create and draw resized right overlay
      const rightOverlay = await this.resizeFrame(rightFrame!, this.OVERLAY_SIZE, this.OVERLAY_SIZE);
      ctx.putImageData(rightOverlay, baseData.width - this.OVERLAY_SIZE, 0);

      // Get composed frame
      const composedImageData = ctx.getImageData(0, 0, canvas.width, canvas.height);

      composedFrames.push({
        imageData: composedImageData,
        delay: Math.max(baseFrame!.delay, leftFrame!.delay, rightFrame!.delay),
        width: canvas.width,
        height: canvas.height,
      });
    }

    return composedFrames;
  }

  private async resizeFrame(frame: FrameData, targetWidth: number, targetHeight: number): Promise<ImageData> {
    const sourceCanvas = createCanvas(frame.width, frame.height);
    const sourceCtx = sourceCanvas.getContext('2d');
    sourceCtx.putImageData(frame.imageData, 0, 0);

    const targetCanvas = createCanvas(targetWidth, targetHeight);
    const targetCtx = targetCanvas.getContext('2d');

    targetCtx.imageSmoothingEnabled = true;
    targetCtx.imageSmoothingQuality = 'high';
    targetCtx.drawImage(sourceCanvas, 0, 0, targetWidth, targetHeight);

    return targetCtx.getImageData(0, 0, targetWidth, targetHeight);
  }

  private async createAnimatedGif(
    frames: FrameData[],
    width: number,
    height: number,
    options: CompositionOptions
  ): Promise<Buffer> {
    return new Promise((resolve, reject) => {
      const encoder = new GIFEncoder(width, height);
      const buffers: Buffer[] = [];

      encoder.createReadStream().on('data', (chunk: Buffer) => {
        buffers.push(chunk);
      });

      encoder.createReadStream().on('end', () => {
        resolve(Buffer.concat(buffers));
      });

      encoder.createReadStream().on('error', reject);

      encoder.start();
      encoder.setRepeat(0);
      encoder.setQuality(options.quality || 10);

      const canvas = createCanvas(width, height);
      const ctx = canvas.getContext('2d');

      for (const frame of frames) {
        encoder.setDelay(frame.delay);
        ctx.putImageData(frame.imageData, 0, 0);
        encoder.addFrame(ctx);
      }

      encoder.finish();
    });
  }

  private async createAnimatedWebP(
    frames: FrameData[],
    width: number,
    height: number,
    options: CompositionOptions
  ): Promise<Buffer> {
    const frameBuffers: Buffer[] = [];
    const canvas = createCanvas(width, height);
    const ctx = canvas.getContext('2d');

    for (const frame of frames) {
      ctx.putImageData(frame.imageData, 0, 0);
      frameBuffers.push(canvas.toBuffer('image/png'));
    }

    const delays = frames.map((f) => f.delay);

    return await sharp(frameBuffers[0], { animated: true })
      .webp({
        quality: options.quality || 80,
        delay: delays,
        loop: 0,
      })
      .toBuffer();
  }

  private isAnimatedWebP(buffer: Buffer): boolean {
    const webpSignature = buffer.subarray(0, 4).toString() === 'RIFF' && buffer.subarray(8, 12).toString() === 'WEBP';
    if (!webpSignature) return false;

    return buffer.includes(Buffer.from('ANIM'));
  }

  private validateDimensions(baseData: ProcessedImage): void {
    const aspectRatio = baseData.width / baseData.height;
    const expectedRatio = 9 / 16;
    const tolerance = 0.1;

    if (Math.abs(aspectRatio - expectedRatio) > tolerance) {
      console.warn(`Warning: Image aspect ratio ${aspectRatio.toFixed(2)} differs from expected 9:16 ratio`);
    }

    if (baseData.width < this.OVERLAY_SIZE * 2) {
      throw new Error(`Image width ${baseData.width} is too small for overlays`);
    }
  }
}

export class GifOverlayService {
  private compositor: ProductionGifOverlayCompositor;

  constructor() {
    this.compositor = new ProductionGifOverlayCompositor();
  }

  async processFiles(
    baseImagePath: string | Buffer,
    topLeftGifPath: string | Buffer,
    topRightGifPath: string | Buffer,
    options: CompositionOptions = { outputFormat: 'gif' }
  ): Promise<Buffer> {
    return await this.compositor.composeWithOverlays(baseImagePath, topLeftGifPath, topRightGifPath, options);
  }

  async processAndSave(
    baseImagePath: string | Buffer,
    topLeftGifPath: string | Buffer,
    topRightGifPath: string | Buffer,
    outputPath: string,
    options: CompositionOptions = { outputFormat: 'gif' }
  ): Promise<void> {
    const result = await this.processFiles(baseImagePath, topLeftGifPath, topRightGifPath, options);
    await fs.writeFile(outputPath, result);
    console.log(`Output saved to: ${outputPath}`);
  }
}
