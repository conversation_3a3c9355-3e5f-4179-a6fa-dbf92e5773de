import { getObject, translate } from '@megami/locale';
import type { SlashCommandSubcommandBuilder } from 'discord.js';
import { defineCommand } from '../../handlers/command';
import gift from './modules/gift';
import list from './modules/list';
import shop from './modules/shop';
import use from './modules/use';

export default defineCommand((builder) => ({
  builder: builder
    .setName(translate('commands.inventory.name'))
    .setNameLocalizations(getObject('commands.inventory.name'))
    .setDescription(translate('commands.inventory.description'))
    .setDescriptionLocalizations(getObject('commands.inventory.description'))
    .addSubcommand(list.builder as SlashCommandSubcommandBuilder)
    .addSubcommand(shop.builder as SlashCommandSubcommandBuilder)
    .addSubcommand(use.builder as SlashCommandSubcommandBuilder)
    .addSubcommand(gift.builder as SlashCommandSubcommandBuilder),
  config: {},
  execute: async (interaction) => {
    const subcommand = interaction.options.getSubcommand();

    switch (subcommand) {
      case 'list': {
        await list.execute(interaction);
        break;
      }

      case 'shop': {
        await shop.execute(interaction);
        break;
      }

      case 'use': {
        await use.execute(interaction);
        break;
      }

      case 'gift': {
        await gift.execute(interaction);
        break;
      }

      default: {
        await interaction.reply({
          content: translate('errors.general.unknown.description'),
          ephemeral: true,
        });
      }
    }
  },
}));
