import fs from 'node:fs';
import path from 'node:path';
import ffmpeg from 'fluent-ffmpeg';

export async function process(input: string, dir: string) {
  const filename = path.basename(input, path.extname(input));
  const tempPath = path.join(dir, `${filename}_transparent.mov`);
  const croppedPath = path.join(dir, `${filename}_cropped.mov`);
  //   const webpPath = path.join(dir, `${filename}_boomerang.webp`);

  fs.mkdirSync(dir, { recursive: true });

  const inputWidth = 1280;
  const inputHeight = 720;
  const targetAspectRatio = 10 / 16;
  const targetWidth = Math.floor(inputHeight * targetAspectRatio); // 612
  const cropX = Math.floor((inputWidth - targetWidth) / 2); // crop from center

  await new Promise<void>((resolve, reject) => {
    const command = ffmpeg(input)
      .videoFilters(['chromakey=0x000000:0.0001:0.2', 'format=rgba'])
      .outputOptions([
        '-c:v prores_ks',
        '-profile:v 4',
        '-an', // No audio
        '-pix_fmt yuva444p10le',
        '-r 30',
      ])
      .format('mov')
      .output(tempPath)
      .on('end', resolve as () => void)
      .on('error', reject);

    command.run();
  });

  await new Promise<void>((resolve, reject) => {
    const command = ffmpeg(tempPath)
      .videoFilters([`crop=${targetWidth}:${inputHeight}:${cropX}:0`])
      .outputOptions([
        '-c:v prores_ks',
        '-profile:v 4',
        '-an', // No audio
        '-pix_fmt yuva444p10le',
        '-r 30',
      ])
      .output(croppedPath)
      .on('end', resolve as () => void)
      .on('error', reject);

    // if (trim) {
    //   command = command.setStartTime(trim.start).setDuration(trim.end - trim.start);
    // }

    command.run();
  });
}
