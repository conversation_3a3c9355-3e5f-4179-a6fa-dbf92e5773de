/**
 * Returns the union of two sets.
 * @param a The first set.
 * @param b The second set.
 * @returns A new set containing all elements from both sets.
 */
export function union<T>(a: Set<T>, b: Set<T>): Set<T> {
  return new Set([...a, ...b]);
}

/**
 * Returns the intersection of two sets.
 * @param a The first set.
 * @param b The second set.
 * @returns A new set containing only the elements that are in both sets.
 */
export function intersection<T>(a: Set<T>, b: Set<T>): Set<T> {
  return new Set([...a].filter((x) => b.has(x)));
}

/**
 * Returns the difference between two sets.
 * @param a The first set.
 * @param b The second set.
 * @returns A new set containing only the elements that are in the first set but not in the second.
 */
export function difference<T>(a: Set<T>, b: Set<T>): Set<T> {
  return new Set([...a].filter((x) => !b.has(x)));
}

/**
 * Returns the symmetric difference between two sets.
 * @param a The first set.
 * @param b The second set.
 * @returns A new set containing only the elements that are in one of the sets but not both.
 */
export function symmetric<T>(a: Set<T>, b: Set<T>): Set<T> {
  return new Set([...a].filter((x) => !b.has(x)).concat([...b].filter((x) => !a.has(x))));
}
