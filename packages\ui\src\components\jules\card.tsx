'use client';

import { cn } from '@megami/ui/lib/utils';
import { motion } from 'framer-motion';
import * as React from 'react';

export interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
}

const Card = React.forwardRef<HTMLDivElement, CardProps>(({ className, children, ...props }, ref) => {
  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5 } },
  };

  return (
    <motion.div
      animate="visible"
      className={cn('relative w-full rounded-lg bg-gray-900/50 p-6 text-white backdrop-blur-sm', className)}
      initial="hidden"
      ref={ref}
      variants={cardVariants}
      {...props}
    >
      {children}
    </motion.div>
  );
});
Card.displayName = 'Card';

export { Card };
