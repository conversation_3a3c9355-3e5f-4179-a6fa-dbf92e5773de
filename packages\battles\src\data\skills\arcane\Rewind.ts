import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: '<PERSON><PERSON>',
  name: '<PERSON><PERSON>',
  element: '<PERSON>ane',
  manaCost: 30,
  cooldown: 5,
  description: 'Rewinds time for the target, returning them to their state from a few turns ago.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Health Regen Up',
        name: 'Rewind',
        duration: 3,
        sourceId: caster.id,
      },
      log: `${caster.name} rewinds time for ${target.name}.`,
    };
  },
});
