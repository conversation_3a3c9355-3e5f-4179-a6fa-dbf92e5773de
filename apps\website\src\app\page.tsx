'use client';

import { motion } from 'framer-motion';
import { Navbar } from '@/components/navbar';
import { Button } from '@megami/ui/components/jules/button';
import { Card } from '@megami/ui/components/jules/card';
import { TestimonialCard } from '@megami/ui/components/jules/testimonial-card';
import { Star } from 'lucide-react';

const features = [
  {
    name: 'Gacha System',
    description: 'Collect cards of your favorite characters from anime, manga, and games.',
    icon: Star,
  },
  {
    name: 'Trading System',
    description: 'Trade cards with other players to complete your collection.',
    icon: Star,
  },
  {
    name: 'Battle System',
    description: 'Battle with other players to test your skills and earn rewards.',
    icon: Star,
  },
];

const testimonials = [
  {
    name: '<PERSON><PERSON>',
    role: 'Beta Tester',
    avatarSrc: 'https://placehold.co/128x128/000000/FFFFFF/png?text=K',
    testimonial: '<PERSON><PERSON> is the best bot I have ever used. I love collecting my favorite characters!',
  },
  {
    name: '<PERSON><PERSON>',
    role: 'Gamer',
    avatarSrc: 'https://placehold.co/128x128/FF0000/FFFFFF/png?text=A',
    testimonial: 'The trading system is amazing. I was able to complete my collection in no time.',
  },
  {
    name: 'Saitama',
    role: 'Hero for Fun',
    avatarSrc: 'https://placehold.co/128x128/FFFF00/000000/png?text=S',
    testimonial: 'One punch is all it takes to see how great this bot is. It\'s okay.',
  },
];

export default function Home() {
  return (
    <main className="min-h-screen w-full bg-black text-white">
      <Navbar />
      <div
        className="relative h-screen w-full bg-cover bg-center"
        style={{
          backgroundImage:
            "url('https://placehold.co/1920x1080/000000/8A2BE2/png?text=Anime+Art')",
        }}
      >
        <div className="absolute inset-0 bg-black/60" />
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1 }}
          className="relative z-10 flex flex-col items-center justify-center h-full text-center px-4"
        >
          <h1 className="text-5xl md:text-7xl font-bold text-white">
            Your Waifu Collecting Journey Begins Here
          </h1>
          <p className="text-lg md:text-xl text-gray-300 mt-4 max-w-2xl">
            Megami is the ultimate Discord bot for collecting, trading, and battling with your favorite characters from anime, manga, and games.
          </p>
          <div className="mt-8 flex flex-wrap justify-center gap-4">
            <Button size="lg">Invite Megami</Button>
            <Button size="lg" variant="secondary">Vote for Megami</Button>
            <Button size="lg" variant="outline">Join Community</Button>
          </div>
        </motion.div>
      </div>
      <section className="py-20 px-4 bg-gray-950/50">
        <div className="container mx-auto">
          <h2 className="text-4xl font-bold text-center mb-12">Why You'll Love Megami</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {features.map((feature, i) => (
              <motion.div
                key={feature.name}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: i * 0.1 }}
                viewport={{ once: true }}
              >
                <Card>
                  <div className="flex items-center mb-4">
                    <feature.icon className="h-6 w-6 mr-2 text-yellow-400" />
                    <h3 className="text-xl font-bold">{feature.name}</h3>
                  </div>
                  <p className="text-gray-400">{feature.description}</p>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>
      <section className="py-20 px-4">
        <div className="container mx-auto">
          <h2 className="text-4xl font-bold text-center mb-12">What Our Users Say</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, i) => (
              <motion.div
                key={testimonial.name}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: i * 0.1 }}
                viewport={{ once: true }}
              >
                <TestimonialCard {...testimonial} />
              </motion.div>
            ))}
          </div>
        </div>
      </section>
    </main>
  );
}
