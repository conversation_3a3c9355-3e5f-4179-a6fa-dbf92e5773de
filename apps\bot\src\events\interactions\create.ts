import { defineEvent } from '../../handlers/event';
import { concurrently } from '../../helpers/concurrency';

export default defineEvent({
  name: 'interactionCreate',
  once: false,
  execute: async (interaction) => {
    if (interaction.isChatInputCommand()) {
      const command = interaction.client.storage.commands.get(interaction.commandName);
      if (!command) return;

      // const approval = await check(interaction, command);
      // if (!approval) return;

      await concurrently(interaction, command);
    }
  },
});
