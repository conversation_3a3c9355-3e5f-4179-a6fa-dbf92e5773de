import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Arcane Empowerment',
  name: 'Arcane Empowerment',
  element: 'Arcane',
  manaCost: 30,
  cooldown: 5,
  description: 'Empowers the caster, increasing their spell power and reducing mana costs.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Spell Power Up',
        name: 'Spell Power Up',
        duration: 3,
        sourceId: caster.id,
      },
      log: `${caster.name} becomes empowered with arcane energy, increasing their spell power.`,
    };
  },
});
