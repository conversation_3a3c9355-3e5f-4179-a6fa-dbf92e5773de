import { EmbedBuilder, type EmbedData } from 'discord.js';

const DEFAULT_COLOR = 0x2f_31_36; // Discord's dark UI background color
export const DEFAULT_AUTHOR_TEXT = 'Project Megami • Celestial Archives';
export const DEFAULT_FOOTER_TEXT = 'A whisper from the cosmos...';
const DEFAULT_ICON =
  'https://cdn.discordapp.com/avatars/1391709551484604526/a_cb7b34cb660cd8d50a3c4f303a5c7a66.gif?size=1024';

export interface MegamiEmbedOptions extends EmbedData {
  avatarText?: string;
  avatarURL?: string;
}

export class MegamiEmbed extends EmbedBuilder {
  constructor(options: MegamiEmbedOptions = {}) {
    super({
      ...options,
      color: options.color ?? DEFAULT_COLOR,
      footer: {
        text: DEFAULT_AUTHOR_TEXT,
        icon_url: DEFAULT_ICON,
      },
      author: {
        name: DEFAULT_FOOTER_TEXT,
        icon_url: DEFAULT_ICON,
      },
      timestamp: options.timestamp ?? new Date(),
    });
  }
}
