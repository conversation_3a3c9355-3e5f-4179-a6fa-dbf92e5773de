import {
  type ChatInputCommandInteraction,
  MessageFlags,
  SeparatorBuilder,
  SeparatorSpacingSize,
  TextDisplayBuilder,
} from 'discord.js';
import type { Command } from '../handlers/command';
import { MegamiContainer } from './containers';

export async function concurrently(interaction: ChatInputCommandInteraction, command: Command) {
  const { executing, cooldowns } = interaction.client.storage;
  if (executing.has(interaction.user.id)) {
    return await interaction.reply({
      components: [
        new MegamiContainer([
          new TextDisplayBuilder().setContent('**Busy**'),
          new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
          new TextDisplayBuilder().setContent('Please wait for your previous command to finish.'),
        ]),
      ],
      flags: MessageFlags.IsComponentsV2,
      ephemeral: true,
    });
  }

  const cooldown = cooldowns.get(`${interaction.user.id}:${interaction.commandName}`);
  if (cooldown) {
    const remaining = cooldown - Date.now();
    if (remaining > 0) {
      return await interaction.reply({
        components: [
          new MegamiContainer([
            new TextDisplayBuilder().setContent('**Cooldown**'),
            new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
            new TextDisplayBuilder().setContent(
              `Please wait ${Math.ceil(remaining / 1000)} seconds before using this command again.`
            ),
          ]),
        ],
        flags: MessageFlags.IsComponentsV2,
        ephemeral: true,
      });
    }
  }

  executing.add(interaction.user.id);

  try {
    await command.execute(interaction);
    const ratelimit = 3;
    cooldowns.set(`${interaction.user.id}:${interaction.commandName}`, Date.now() + ratelimit * 1000);
    setTimeout(() => cooldowns.delete(`${interaction.user.id}:${interaction.commandName}`), ratelimit * 1000);
  } finally {
    executing.delete(interaction.user.id);
  }
}
