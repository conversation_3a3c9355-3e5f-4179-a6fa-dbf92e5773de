import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Singularity Point',
  name: 'Singularity Point',
  element: 'Vortex',
  manaCost: 80,
  cooldown: 10,
  description: 'Creates a singularity point that pulls all enemies towards it and then explodes, dealing massive damage.',
  execute: (caster, target, formulas) => {
    // This would require a system for creating and managing persistent area effects with a pull and explosion mechanic.
    return {
      log: `${caster.name} creates a Singularity Point.`,
    };
  },
});
