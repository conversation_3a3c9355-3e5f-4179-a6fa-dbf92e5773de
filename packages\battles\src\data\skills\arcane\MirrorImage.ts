import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Mirror Image',
  name: 'Mirror Image',
  element: 'Arcane',
  manaCost: 25,
  cooldown: 4,
  description: 'Creates several duplicates of the caster to confuse enemies.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Evasion Up',
        name: 'Mirror Image',
        duration: 3,
        sourceId: caster.id,
      },
      log: `${caster.name} creates several mirror images to confuse enemies.`,
    };
  },
});
