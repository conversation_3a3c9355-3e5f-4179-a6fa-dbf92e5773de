import { integer, jsonb, pgTable, serial, text, timestamp, uniqueIndex } from 'drizzle-orm/pg-core';
import { users } from './users';

export type DropRarity = 'Stardust' | 'Moonlight' | 'Celestial';
export type DropStatus = 'SUCCESS' | 'PENALTY_APPLIED';

export interface DropResult {
  rarity: DropRarity;
  cardId?: string;
  cardName?: string;
  penaltyApplied?: boolean;
  penaltyType?: string;
  penaltyDescription?: string;
}

// Table to track user drop history and statistics
export const dropHistory = pgTable(
  'drop_history',
  {
    id: serial('id').primaryKey(),
    userId: text('user_id')
      .notNull()
      .references(() => users.id, { onDelete: 'cascade' }),
    
    // Drop details
    dropType: text('drop_type').notNull(), // basic, enhanced, moonlight, etc.
    riftsCost: integer('rifts_cost').notNull(),
    
    // Result
    status: text('status').$type<DropStatus>().notNull(),
    result: jsonb('result').$type<DropResult>().notNull(),
    
    // Timestamps
    createdAt: timestamp('created_at').defaultNow().notNull(),
  },
  (table) => [
    uniqueIndex('drop_history_user_created_idx').on(table.userId, table.createdAt),
  ]
);

// Table to track active penalties and cooldowns
export const dropPenalties = pgTable(
  'drop_penalties',
  {
    id: serial('id').primaryKey(),
    userId: text('user_id')
      .notNull()
      .references(() => users.id, { onDelete: 'cascade' }),
    
    // Penalty details
    penaltyType: text('penalty_type').notNull(), // COOLDOWN_LOCK, etc.
    description: text('description').notNull(),
    
    // Timing
    expiresAt: timestamp('expires_at').notNull(),
    
    // Metadata
    createdAt: timestamp('created_at').defaultNow().notNull(),
  },
  (table) => [
    uniqueIndex('drop_penalties_user_type_idx').on(table.userId, table.penaltyType),
  ]
);

// Table to track daily drop limits
export const dropLimits = pgTable(
  'drop_limits',
  {
    id: serial('id').primaryKey(),
    userId: text('user_id')
      .notNull()
      .references(() => users.id, { onDelete: 'cascade' }),
    
    // Limit tracking
    dropType: text('drop_type').notNull(),
    count: integer('count').default(0).notNull(),
    date: text('date').notNull(), // YYYY-MM-DD format
    
    // Timestamps
    createdAt: timestamp('created_at').defaultNow().notNull(),
    updatedAt: timestamp('updated_at').defaultNow().notNull(),
  },
  (table) => [
    uniqueIndex('drop_limits_user_type_date_idx').on(table.userId, table.dropType, table.date),
  ]
);
