/* biome-ignore-all lint/style/useConst: We are gonna re-assign later */
/** biome-ignore-all lint/suspicious/noFocusedTests: Not relevant for this scope */
import { createCanvas, type Image, loadImage } from '@napi-rs/canvas';
import { Vibrant } from 'node-vibrant/node';
import type { Metadata } from 'sharp';
import sharp from 'sharp';

export class MegamiFrame {
  public source: Buffer;
  public metadata: Metadata;

  constructor(source: <PERSON><PERSON><PERSON>, metadata: Metadata) {
    this.source = source;
    this.metadata = metadata;
  }

  public fit(margin: number) {
    const frameWidth = this.metadata.width!;
    const frameHeight = this.metadata.height!;

    const safeWidth = frameWidth * (1 - margin * 2);
    const safeHeight = frameHeight * (1 - margin * 2);

    return { width: Math.round(safeWidth), height: Math.round(safeHeight) };
  }

  public async colors(data: Buffer) {
    return await Vibrant.from(data).getPalette();
  }

  public async around(character: <PERSON><PERSON><PERSON>) {
    const palette = await this.colors(character);
    const dominant = palette.Vibrant!;
    const r = dominant.rgb[0];
    const g = dominant.rgb[1];
    const b = dominant.rgb[2];

    const tinted = await sharp(this.source)
      .tint({
        r: Math.min(255, r * 1.1),
        g: Math.min(255, g * 1.1),
        b: Math.min(255, b * 1.1),
      })
      .modulate({ saturation: 1.2, brightness: 1.05 })
      .png()
      .toBuffer();

    const targetW = Math.round(this.metadata.width * 0.9);
    const targetH = Math.round(this.metadata.height * 0.9);

    const canvas = createCanvas(this.metadata.width, this.metadata.height);
    const context = canvas.getContext('2d');

    context.imageSmoothingEnabled = true;
    context.imageSmoothingQuality = 'high';

    const canvases: Record<string, Image> = {};

    canvases.character = await loadImage(
      await sharp(character)
        .resize(targetW, targetH, {
          fit: 'fill',
        })
        .sharpen()
        .png()
        .toBuffer()
    );

    canvases.frame = await loadImage(tinted);

    const marginX = Math.round(this.metadata.width * 0.1);
    const marginY = Math.round(this.metadata.height * 0.05);

    // const characterWidth = targetW * 0.92;
    // const characterHeight = targetH * 1.02;

    // // Calculate 10% margins (5% on each side)
    // const marginOffset = characterWidth * 0.05; // 5% margin
    // const fillX = marginX - marginOffset * 1.35; // Start 5% to the left
    // const fillY = marginY - marginOffset; // Start 5% above
    // const fillWidth = characterWidth + marginOffset * 2; // Add 10% total (5% each side)
    // const fillHeight = characterHeight + marginOffset * 1.2; // Add 10% total (5% each side)

    // context.fillStyle = `rgba(${r}, ${g}, ${b}`;
    // context.fillRect(fillX, fillY, fillWidth, fillHeight);

    // Add subtle inner shadow for depth
    context.save();
    context.shadowColor = 'rgba(0, 0, 0, 0.3)';
    context.shadowBlur = 15;
    context.shadowOffsetX = 0;
    context.shadowOffsetY = 5;

    context.drawImage(canvases.character, marginX, marginY, targetW * 0.92, targetH * 1.02);
    context.restore();

    // @ts-expect-error - ignore this operation
    context.globalCompositeOperation = 'normal';
    context.drawImage(canvases.frame, 0, 0, this.metadata.width, this.metadata.height);

    // Add subtle warm color overlay for better atmosphere
    context.save();
    context.globalCompositeOperation = 'overlay';
    context.globalAlpha = 0.08;
    context.fillStyle = `;rgba(${Math.min(255, r + 20)}, ${Math.min(255, g + 10)}, ${Math.max(0, b - 10)}, 1)`;
    context.fillRect(0, 0, this.metadata.width, this.metadata.height);
    context.restore();

    return canvas.toBuffer('image/png');
  }
}

export class MegamiFrameClient {
  public async fromURL(url: string) {
    const source = await fetch(url).then((response) => response.arrayBuffer());
    const metadata = await sharp(source).metadata();
    return new MegamiFrame(Buffer.from(source), metadata);
  }
}
