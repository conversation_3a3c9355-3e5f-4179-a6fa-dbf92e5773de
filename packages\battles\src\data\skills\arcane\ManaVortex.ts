import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Mana Vortex',
  name: 'Mana Vortex',
  element: 'Arcane',
  manaCost: 35,
  cooldown: 4,
  description: 'Creates a vortex of mana that drains mana from the target.',
  execute: (caster, target, formulas) => {
    const manaDrained = 20;
    // In a real implementation, this would actually drain mana.
    // For now, we'll just log the effect.
    return {
      log: `${caster.name} creates a Mana Vortex, draining ${manaDrained} mana from ${target.name}.`,
    };
  },
});
