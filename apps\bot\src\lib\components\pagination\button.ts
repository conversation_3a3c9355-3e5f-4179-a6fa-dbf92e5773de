import {
  ActionRowBuilder,
  ButtonBuilder,
  type ButtonInteraction,
  ButtonStyle,
  ComponentType,
  type MessageComponentType,
} from 'discord.js';
import type { ButtonPaginationOptions } from '../pagination';
import { BasePaginator } from './base';
import { Page } from './page';

export class ButtonPaginator extends BasePaginator {
  private emojis: Required<NonNullable<ButtonPaginationOptions['emojis']>>;
  private buttonIDs: Record<string, string>;

  constructor(options: ButtonPaginationOptions) {
    super(options);

    // Default emojis
    const defaultEmojis = {
      start: '⏮️',
      prev: '◀️',
      stop: '⏹️',
      next: '▶️',
      end: '⏭️',
    };

    this.emojis = { ...defaultEmojis, ...options.emojis };

    // Create unique custom IDs
    const baseId = `btn_pagination_${this.interaction.id}_${Date.now()}`;
    this.buttonIDs = {
      start: `${baseId}_start`,
      prev: `${baseId}_prev`,
      stop: `${baseId}_stop`,
      next: `${baseId}_next`,
      end: `${baseId}_end`,
    };
  }

  getComponentType(): MessageComponentType {
    return ComponentType.Button;
  }

  createComponents(disabled = false): ActionRowBuilder<ButtonBuilder>[] {
    const isFirstPage = this.pages.current === 0;
    const isLastPage = this.pages.current === this.pages.total - 1;
    const isSinglePage = this.pages.total === 1;

    const buttons = new ActionRowBuilder<ButtonBuilder>().addComponents(
      new ButtonBuilder()
        .setCustomId(this.buttonIDs.start as string)
        .setEmoji(this.emojis.start)
        .setStyle(ButtonStyle.Primary)
        .setDisabled(disabled || isFirstPage || isSinglePage),

      new ButtonBuilder()
        .setCustomId(this.buttonIDs.prev as string)
        .setEmoji(this.emojis.prev)
        .setStyle(ButtonStyle.Primary)
        .setDisabled(disabled || isFirstPage || isSinglePage),

      new ButtonBuilder()
        .setCustomId(this.buttonIDs.stop as string)
        .setEmoji(this.emojis.stop)
        .setStyle(ButtonStyle.Danger)
        .setDisabled(disabled),

      new ButtonBuilder()
        .setCustomId(this.buttonIDs.next as string)
        .setEmoji(this.emojis.next)
        .setStyle(ButtonStyle.Primary)
        .setDisabled(disabled || isLastPage || isSinglePage),

      new ButtonBuilder()
        .setCustomId(this.buttonIDs.end as string)
        .setEmoji(this.emojis.end)
        .setStyle(ButtonStyle.Primary)
        .setDisabled(disabled || isLastPage || isSinglePage)
    );

    return [buttons];
  }

  protected override filterInteraction(interaction: ButtonInteraction): boolean {
    if (!Object.values(this.buttonIDs).includes(interaction.customId)) {
      return false;
    }

    return super.filterInteraction(interaction);
  }

  protected async handleInteraction(interaction: ButtonInteraction): Promise<void> {
    if (this.isDestroyed) return;

    let newPage = this.pages.current;

    // Handle button actions
    switch (interaction.customId) {
      case this.buttonIDs.start:
        newPage = 0;
        break;
      case this.buttonIDs.prev:
        newPage = Math.max(0, this.pages.current - 1);
        break;
      case this.buttonIDs.next:
        newPage = Math.min(this.pages.total - 1, this.pages.current + 1);
        break;
      case this.buttonIDs.end:
        newPage = this.pages.total - 1;
        break;
      case this.buttonIDs.stop:
        await this.stop(interaction);
        return;
      default:
        return;
    }

    if (newPage !== this.pages.current) {
      this.pages.current = newPage;
      this.emit('change', this.pages.current, new Page(this, { interaction }), interaction);
    }
  }
}
