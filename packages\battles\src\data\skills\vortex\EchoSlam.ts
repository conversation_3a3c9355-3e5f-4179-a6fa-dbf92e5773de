import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Echo Slam',
  name: 'Echo Slam',
  element: 'Vortex',
  manaCost: 40,
  cooldown: 6,
  description: 'The caster slams the ground, creating an echo that damages all enemies in a line.',
  execute: (caster, target, formulas) => {
    // This would require a more complex targeting system.
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const finalDamage = Math.round(damage * 1.5);
    return {
      damage: finalDamage,
      isCritical,
      log: `${caster.name} slams the ground, creating an echo that damages all enemies in a line for ${finalDamage} damage${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
