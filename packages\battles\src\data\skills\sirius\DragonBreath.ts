import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Dragon Breath',
  name: 'Dragon Breath',
  element: 'Sirius',
  manaCost: 35,
  cooldown: 4,
  description: 'The caster breathes a cone of fire, damaging all enemies in front of them.',
  execute: (caster, target, formulas) => {
    // This would require a more complex targeting system.
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const finalDamage = Math.round(damage * 1.4);
    return {
      damage: finalDamage,
      isCritical,
      log: `${caster.name} breathes a cone of fire, damaging all enemies for ${finalDamage} damage${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
