import { MessageFlags, SeparatorBuilder, SeparatorSpacingSize, TextDisplayBuilder } from 'discord.js';
import { translate } from '@megami/locale';
import { defineSubCommand } from '../../../handlers/command';
import { defer } from '../../../helpers/defer';
import { MegamiContainer } from '../../../helpers/containers';

export default defineSubCommand((builder) => ({
  builder: builder
    .setName('effects')
    .setDescription('View your active effects and buffs'),

  config: {},

  execute: async (interaction) => {
    await defer(interaction, false);

    const user = await interaction.client.database.users.get(interaction.user.id);
    if (!user) {
      const container = new MegamiContainer([
        new TextDisplayBuilder().setContent('❌ **Not Registered**'),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent(translate('errors.user.unregistered.description')),
      ]);

      return await interaction.editReply({
        components: [container],
        flags: MessageFlags.IsComponentsV2,
      });
    }

    try {
      // Get all active effects for the user
      const effects = await interaction.client.database.effects.getUserEffects(user.id);

      if (effects.length === 0) {
        const container = new MegamiContainer([
          new TextDisplayBuilder().setContent('✨ **Active Effects**'),
          new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
          new TextDisplayBuilder().setContent('You currently have no active effects.'),
          new TextDisplayBuilder().setContent('Use consumable items or equip gear to gain effects!'),
        ]);

        return await interaction.editReply({
          components: [container],
          flags: MessageFlags.IsComponentsV2,
        });
      }

      // Group effects by type for better organization
      const effectsByType: Record<string, typeof effects> = {};
      for (const effect of effects) {
        if (!effectsByType[effect.type]) {
          effectsByType[effect.type] = [];
        }
        effectsByType[effect.type]?.push(effect);
      }

      const components = [
        new TextDisplayBuilder().setContent('✨ **Active Effects**'),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
      ];

      // Display effects grouped by type
      for (const [type, typeEffects] of Object.entries(effectsByType)) {
        const typeEmoji = getEffectTypeEmoji(type);
        const typeName = getEffectTypeName(type);
        
        components.push(new TextDisplayBuilder().setContent(`${typeEmoji} **${typeName}**`));

        for (const effect of typeEffects) {
          const stacks = effect.stacks > 1 ? ` (x${effect.stacks})` : '';
          const expires = effect.expiresAt 
            ? ` - Expires <t:${Math.floor(effect.expiresAt.getTime() / 1000)}:R>`
            : ' - Permanent';
          const source = effect.source === 'ITEM' ? ' 📦' : '';
          
          components.push(
            new TextDisplayBuilder().setContent(
              `  • **${effect.name}**${stacks}${source}`
            )
          );
          components.push(
            new TextDisplayBuilder().setContent(
              `    ${effect.description}${expires}`
            )
          );
        }

        components.push(new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small));
      }

      // Show effect calculations for key types
      const riftEffects = await interaction.client.database.effects.calculateRiftEffects(user.id);
      const expEffects = await interaction.client.database.effects.calculateExpEffects(user.id);

      if (riftEffects.multiplier !== 1 || riftEffects.bonus !== 0) {
        components.push(new TextDisplayBuilder().setContent('⚡ **Rift Regeneration**'));
        components.push(
          new TextDisplayBuilder().setContent(
            `  Base: 10 rifts → Enhanced: ${Math.floor((10 * riftEffects.multiplier) + riftEffects.bonus)} rifts`
          )
        );
        components.push(new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small));
      }

      if (expEffects.multiplier !== 1 || expEffects.bonus !== 0) {
        components.push(new TextDisplayBuilder().setContent('⭐ **Experience Gains**'));
        components.push(
          new TextDisplayBuilder().setContent(
            `  Multiplier: ${expEffects.multiplier.toFixed(2)}x | Bonus: +${expEffects.bonus}`
          )
        );
        components.push(new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small));
      }

      // Remove the last separator
      if (components[components.length - 1] instanceof SeparatorBuilder) {
        components.pop();
      }

      const container = new MegamiContainer(components);
      await interaction.editReply({
        components: [container],
        flags: MessageFlags.IsComponentsV2,
      });

    } catch (error) {
      console.error('Error fetching effects:', error);
      
      const container = new MegamiContainer([
        new TextDisplayBuilder().setContent('❌ **Error**'),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent('An error occurred while fetching your effects.'),
        new TextDisplayBuilder().setContent('Please try again or contact support.'),
      ]);

      await interaction.editReply({
        components: [container],
        flags: MessageFlags.IsComponentsV2,
      });
    }
  },
}));

function getEffectTypeEmoji(type: string): string {
  switch (type) {
    case 'RIFT_MULTIPLIER':
    case 'RIFT_BONUS':
      return '⚡';
    case 'EXP_MULTIPLIER':
    case 'EXP_BONUS':
      return '⭐';
    case 'CURRENCY_MULTIPLIER':
    case 'CURRENCY_BONUS':
      return '💰';
    case 'COOLDOWN_REDUCTION':
      return '⏰';
    case 'DROP_RATE_BONUS':
      return '🎁';
    case 'CUSTOM':
      return '🔮';
    default:
      return '✨';
  }
}

function getEffectTypeName(type: string): string {
  switch (type) {
    case 'RIFT_MULTIPLIER':
      return 'Rift Multipliers';
    case 'RIFT_BONUS':
      return 'Rift Bonuses';
    case 'EXP_MULTIPLIER':
      return 'Experience Multipliers';
    case 'EXP_BONUS':
      return 'Experience Bonuses';
    case 'CURRENCY_MULTIPLIER':
      return 'Currency Multipliers';
    case 'CURRENCY_BONUS':
      return 'Currency Bonuses';
    case 'COOLDOWN_REDUCTION':
      return 'Cooldown Reductions';
    case 'DROP_RATE_BONUS':
      return 'Drop Rate Bonuses';
    case 'CUSTOM':
      return 'Special Effects';
    default:
      return 'Unknown Effects';
  }
}
