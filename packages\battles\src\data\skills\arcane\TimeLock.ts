import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Time Lock',
  name: 'Time Lock',
  element: 'Arcane',
  manaCost: 40,
  cooldown: 6,
  description: 'Locks the target in a time bubble, preventing them from taking any actions.',
  execute: (caster, target, formulas) => {
    // This would require a status effect system to handle the time lock.
    return {
      log: `${caster.name} locks ${target.name} in a time bubble.`,
    };
  },
});
