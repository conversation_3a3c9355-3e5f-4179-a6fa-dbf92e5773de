import { jsonb, pgTable, text, timestamp, uniqueIndex, uuid } from 'drizzle-orm/pg-core';
import { series } from './series';

export type StatKey = 'health' | 'attack' | 'defense' | 'speed' | 'luck';
export interface Stats extends Record<1 | 2 | 3, Record<StatKey, number>> {}

export const characters = pgTable(
  'characters',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    name: text('name').notNull(),
    description: text('description'),
    skill: text('skill').notNull(),
    element: text('element').notNull(),
    seriesId: uuid('series_id')
      .references(() => series.id)
      .notNull(),
    stats: jsonb('stats').$type<Stats>().notNull(),
    createdAt: timestamp('created_at').defaultNow().notNull(),
  },
  (table) => [uniqueIndex('character_name').on(table.name), uniqueIndex('character_id').on(table.id)]
);
