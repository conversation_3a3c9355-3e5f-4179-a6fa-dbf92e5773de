import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Aether Theft',
  name: 'Aether Theft',
  element: 'Arcane',
  manaCost: 20,
  cooldown: 3,
  description: 'Steals a beneficial status effect from the target and applies it to the caster, dealing minor damage.',
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const finalDamage = Math.round(damage * 0.5);
    // This would require a status effect system to handle stealing and applying buffs.
    return {
      damage: finalDamage,
      isCritical,
      log: `${caster.name} steals a beneficial effect from ${target.name} and deals ${finalDamage} damage${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
