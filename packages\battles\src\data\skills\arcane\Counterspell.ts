import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Counterspell',
  name: 'Counterspell',
  element: 'Arcane',
  manaCost: 20,
  cooldown: 4,
  description: 'Counters the next spell cast by the target.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Silence',
        name: 'Silence',
        duration: 1,
        sourceId: caster.id,
      },
      log: `${caster.name} counters the next spell cast by ${target.name}, silencing them.`,
    };
  },
});
