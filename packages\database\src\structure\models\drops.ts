import {
  calculateDropRarity,
  type DropPenalty,
  type DropRarity,
  type DropTypeConfig,
  dropTypes,
  getDropTypeById,
  shouldApplyPenalty,
} from '@megami/config/lib/drops';
import type { InferSelectModel } from 'drizzle-orm';
import { and, eq, gte, lt, sql } from 'drizzle-orm';
import { createSelectSchema } from 'drizzle-zod';
import type { DropResult, DropStatus } from '../../schema/drops';
import { BaseModel } from '../model';

export type DropHistorySelect = InferSelectModel<typeof import('../../schema/drops').dropHistory>;
export type DropPenaltySelect = InferSelectModel<typeof import('../../schema/drops').dropPenalties>;
export type DropLimitSelect = InferSelectModel<typeof import('../../schema/drops').dropLimits>;

export interface DropAttemptResult {
  success: boolean;
  result?: DropResult;
  error?: string;
  riftsCost: number;
}

export class Drops extends BaseModel {
  /**
   * Seed initial drop data (cleanup expired penalties)
   */
  public override async seed(): Promise<void> {
    await this.cleanupExpiredPenalties();
  }

  /**
   * Attempt to make a drop purchase
   */
  public async attemptDrop(userId: string, dropTypeId: string): Promise<DropAttemptResult> {
    const dropType = getDropTypeById(dropTypeId);
    if (!dropType) {
      return { success: false, error: 'Invalid drop type', riftsCost: 0 };
    }

    // Check if user exists and has enough rifts
    const user = await this.client.users.get(userId);
    if (!user) {
      return { success: false, error: 'User not found', riftsCost: dropType.cost };
    }

    const userRifts = await this.client.users.getRifts(userId);
    if (userRifts < dropType.cost) {
      return {
        success: false,
        error: `Insufficient rifts. Need ${dropType.cost}, have ${userRifts}`,
        riftsCost: dropType.cost,
      };
    }

    // Check requirements
    const requirementCheck = await this.checkRequirements(userId, dropType);
    if (!requirementCheck.allowed) {
      return {
        success: false,
        error: requirementCheck.reason,
        riftsCost: dropType.cost,
      };
    }

    // Deduct rifts first
    await this.client.users.addRifts(userId, -dropType.cost);

    // Calculate drop result
    const rarity = calculateDropRarity(dropType);
    const penaltyApplied = shouldApplyPenalty(dropType);

    // Create drop result
    const result: DropResult = {
      rarity,
      cardId: `card_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`, // Temporary card ID
      cardName: `${rarity} Card`, // Temporary card name
      penaltyApplied,
    };

    // Apply penalty if triggered
    if (penaltyApplied && dropType.penalty) {
      await this.applyPenalty(userId, dropType.penalty);
      result.penaltyType = dropType.penalty.type;
      result.penaltyDescription = dropType.penalty.description;
    }

    // Record drop in history
    await this.client.instance.insert(this.schemas.dropHistory).values({
      userId,
      dropType: dropTypeId,
      riftsCost: dropType.cost,
      status: penaltyApplied ? 'PENALTY_APPLIED' : 'SUCCESS',
      result,
    });

    // Update daily limits if applicable
    if (dropType.requirements?.maxPerDay) {
      await this.updateDailyLimit(userId, dropTypeId);
    }

    return {
      success: true,
      result,
      riftsCost: dropType.cost,
    };
  }

  /**
   * Check if user meets requirements for a drop type
   */
  private async checkRequirements(
    userId: string,
    dropType: DropTypeConfig
  ): Promise<{ allowed: boolean; reason?: string }> {
    const user = await this.client.users.get(userId);
    if (!user) {
      return { allowed: false, reason: 'User not found' };
    }

    // Check level requirement
    if (dropType.requirements?.minLevel && user.level < dropType.requirements.minLevel) {
      return {
        allowed: false,
        reason: `Requires level ${dropType.requirements.minLevel}. You are level ${user.level}`,
      };
    }

    // Check for active penalties (cooldown locks)
    const activePenalties = await this.getActivePenalties(userId);
    const cooldownPenalty = activePenalties.find((p) => p.penaltyType === 'COOLDOWN_LOCK');
    if (cooldownPenalty) {
      const expiresIn = Math.ceil((cooldownPenalty.expiresAt.getTime() - Date.now()) / 60_000);
      return {
        allowed: false,
        reason: `Drop cooldown active. ${expiresIn} minutes remaining`,
      };
    }

    // Check daily limits
    if (dropType.requirements?.maxPerDay) {
      const todayCount = await this.getDailyDropCount(userId, dropType.id);
      if (todayCount >= dropType.requirements.maxPerDay) {
        return {
          allowed: false,
          reason: `Daily limit reached (${dropType.requirements.maxPerDay}/${dropType.requirements.maxPerDay})`,
        };
      }
    }

    // Check individual cooldown
    if (dropType.requirements?.cooldownMinutes) {
      const lastDrop = await this.getLastDropOfType(userId, dropType.id);
      if (lastDrop) {
        const cooldownEnd = lastDrop.createdAt.getTime() + dropType.requirements.cooldownMinutes * 60_000;
        if (Date.now() < cooldownEnd) {
          const remainingMinutes = Math.ceil((cooldownEnd - Date.now()) / 60_000);
          return {
            allowed: false,
            reason: `${dropType.name} cooldown active. ${remainingMinutes} minutes remaining`,
          };
        }
      }
    }

    return { allowed: true };
  }

  /**
   * Apply a penalty to the user
   */
  private async applyPenalty(userId: string, penalty: DropPenalty): Promise<void> {
    switch (penalty.type) {
      case 'SHARD_LOSS': {
        if (penalty.effect.shardAmount) {
          // Get shards currency and deduct
          const shardsCurrency = await this.client.currencies.getCurrencyByName('Shards');
          if (shardsCurrency) {
            await this.client.currencies.addToUserBalance(userId, shardsCurrency.id, -penalty.effect.shardAmount);
          }
        }
        break;
      }

      case 'COOLDOWN_LOCK': {
        if (penalty.effect.cooldownMinutes) {
          const expiresAt = new Date(Date.now() + penalty.effect.cooldownMinutes * 60_000);
          await this.client.instance
            .insert(this.schemas.dropPenalties)
            .values({
              userId,
              penaltyType: penalty.type,
              description: penalty.description,
              expiresAt,
            })
            .onConflictDoUpdate({
              target: [this.schemas.dropPenalties.userId, this.schemas.dropPenalties.penaltyType],
              set: {
                expiresAt,
                createdAt: new Date(),
              },
            });
        }
        break;
      }

      default: {
        console.warn(`Unknown penalty type: ${penalty.type}`);
        break;
      }
    }
  }

  /**
   * Get active penalties for a user
   */
  public async getActivePenalties(userId: string): Promise<DropPenaltySelect[]> {
    return await this.client.instance
      .select()
      .from(this.schemas.dropPenalties)
      .where(and(eq(this.schemas.dropPenalties.userId, userId), gte(this.schemas.dropPenalties.expiresAt, new Date())))
      .orderBy(this.schemas.dropPenalties.expiresAt);
  }

  /**
   * Get drop history for a user
   */
  public async getUserDropHistory(userId: string, limit = 50): Promise<DropHistorySelect[]> {
    return await this.client.instance
      .select()
      .from(this.schemas.dropHistory)
      .where(eq(this.schemas.dropHistory.userId, userId))
      .orderBy(sql`${this.schemas.dropHistory.createdAt} DESC`)
      .limit(limit);
  }

  /**
   * Get daily drop count for a specific drop type
   */
  private async getDailyDropCount(_userId: string, _dropType: string): Promise<number> {
    // For now, return 0 to disable daily limits until schema is fixed
    return 0;
  }

  /**
   * Update daily limit counter
   */
  private async updateDailyLimit(_userId: string, _dropType: string): Promise<void> {
    // For now, do nothing until schema is fixed
  }

  /**
   * Get last drop of a specific type
   */
  private async getLastDropOfType(userId: string, dropType: string): Promise<DropHistorySelect | undefined> {
    const results = await this.client.instance
      .select()
      .from(this.schemas.dropHistory)
      .where(and(eq(this.schemas.dropHistory.userId, userId), eq(this.schemas.dropHistory.dropType, dropType)))
      .orderBy(sql`${this.schemas.dropHistory.createdAt} DESC`)
      .limit(1);

    return results[0];
  }

  /**
   * Clean up expired penalties
   */
  public async cleanupExpiredPenalties(): Promise<number> {
    const result = await this.client.instance
      .delete(this.schemas.dropPenalties)
      .where(lt(this.schemas.dropPenalties.expiresAt, new Date()));

    return result.length || 0;
  }

  /**
   * Get available drop types for a user
   */
  public async getAvailableDropTypes(userId: string): Promise<DropTypeConfig[]> {
    const user = await this.client.users.get(userId);
    if (!user) return [];

    // Check requirements for all drop types in parallel
    const requirementChecks = await Promise.all(
      dropTypes.map(async (dropType) => ({
        dropType,
        check: await this.checkRequirements(userId, dropType),
      }))
    );

    return requirementChecks.filter(({ check }) => check.allowed).map(({ dropType }) => dropType);
  }
}
