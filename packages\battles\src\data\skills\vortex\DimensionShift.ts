import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Dimension Shift',
  name: 'Dimension Shift',
  element: 'Vortex',
  manaCost: 30,
  cooldown: 5,
  description: 'The caster shifts to another dimension, becoming untargetable for a short time and dealing damage to any enemy they pass through.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Evasion Up',
        name: 'Dimension Shift',
        duration: 2,
        sourceId: caster.id,
      },
      log: `${caster.name} shifts to another dimension.`,
    };
  },
});
