import { DISCORD_LANGUAGE_CODES } from './discord';
import { type DiscordLanguageCode, translate } from './i18n';

export * from './i18n';

export function getObject(key: Parameters<typeof translate>[0]): Record<DiscordLanguageCode, string> {
  const object = Object.fromEntries(
    DISCORD_LANGUAGE_CODES.map((locale) => [locale, translate(key, { lng: locale }) as string])
  ) as Record<DiscordLanguageCode, string>;

  if (key.includes('description')) {
    for (const k of Object.keys(object)) {
      if (object[k as keyof typeof object].length > 100) {
        object[k as keyof typeof object] = `${object[k as keyof typeof object].substring(0, 97)}...`;
      }
    }
  }

  return object;
}
