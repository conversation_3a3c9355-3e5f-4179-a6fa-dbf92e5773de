import * as prompt from '@clack/prompts';
import { $ } from 'bun';

const regex = /(\.\w+)$/;

export async function animate() {
  prompt.intro('Megami Scripts - MP4 To Animated Webp');

  const input = (await prompt.text({
    message: 'Input file path',
    placeholder: 'e.g. O:/My Drive/Megami/Cards/MP4s/Hanabi 2.mp4',
    validate: (value) => {
      if (!value) return 'Required';
    },
  })) as string;

  const path = input
    .trim()
    .replace(/^["']|["']$/g, '')
    .replaceAll('\\', '/');

  const output = path.replace(regex, '_output$1').replace('mp4', 'webp');

  await prompt.tasks([
    {
      title: 'Processing video',
      task: async (_message) => {
        await $`ffmpeg -i ${path} -filter_complex "[0:v]fps=30,scale=iw:ih:flags=lanczos,colorkey=black:0.00001:0.01,format=yuva420p,crop='ih*10/16:ih:(iw-ih*10/16)/2:0',eq=contrast=1.1:saturation=2.0,split[orig][rev];[rev]reverse[r];[orig][r]concat=n=2:v=1:a=0,split[bg][fg];[fg]palettegen[pal];[bg][pal]paletteuse" -c:v libwebp_anim -loop 0 -lossless 1 -preset picture -pix_fmt yuva420p -an ${output}`.quiet();
        return `Video processed, Output: ${output}`;
      },
    },
  ]);

  prompt.outro('Done!');
}
