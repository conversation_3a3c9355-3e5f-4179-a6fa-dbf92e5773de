import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Volcanic Ash',
  name: 'Volcanic Ash',
  element: 'Sirius',
  manaCost: 25,
  cooldown: 4,
  description: 'The caster summons a cloud of volcanic ash, reducing the accuracy of all enemies.',
  execute: (caster, target, formulas) => {
    // This would require a status effect system to handle the accuracy reduction.
    return {
      log: `${caster.name} summons a cloud of volcanic ash.`,
    };
  },
});
