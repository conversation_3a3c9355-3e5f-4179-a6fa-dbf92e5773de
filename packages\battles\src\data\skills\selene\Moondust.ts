import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Moondust',
  name: 'Moon<PERSON>',
  element: '<PERSON><PERSON>',
  manaCost: 20,
  cooldown: 3,
  description: 'Scatters moondust on the target, causing them to have a chance to miss their attacks.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Accuracy Down',
        name: 'Moondust',
        duration: 3,
        sourceId: caster.id,
      },
      log: `${caster.name} scatters moondust on ${target.name}.`,
    };
  },
});
