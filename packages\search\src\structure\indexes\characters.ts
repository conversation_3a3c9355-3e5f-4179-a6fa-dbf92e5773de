import type { characters, InferSelectModel } from '@megami/database';
import MiniSearch, { type Query, type SearchOptions } from 'minisearch';

type Character = InferSelectModel<typeof characters>;

export class MegamiCharactersIndex {
  public instance: MiniSearch<Character>;

  constructor() {
    this.instance = new MiniSearch<Character>({
      fields: ['name', 'description', 'element', 'skill'],
      storeFields: ['name', 'description', 'element', 'skill'],
    });
  }

  public async add(...character: Character[]) {
    return await this.instance.addAllAsync(character);
  }

  public remove(...character: Character[]) {
    return this.instance.removeAll(character);
  }

  public search(query: Query, options: SearchOptions = {}) {
    return this.instance.search(query, options);
  }
}
