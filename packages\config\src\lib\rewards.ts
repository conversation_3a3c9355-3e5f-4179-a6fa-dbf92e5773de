import type { CurrencyNames } from './currencies';

export type SkinRarity = 'Stardust' | 'Moonlight' | 'Celestial';

export interface CurrencyReward {
  currency: CurrencyNames;
  amount: number;
}

export interface ExperienceReward {
  amount: number;
}

export interface SkinApprovalReward {
  rarity: SkinRarity;
  currencies: CurrencyReward[];
  experience: ExperienceReward;
  description: string;
}

export const skinApprovalRewards: Readonly<SkinApprovalReward[]> = [
  {
    rarity: 'Stardust',
    currencies: [
      { currency: 'Shards', amount: 500 },
      { currency: 'Rifts', amount: 2 },
    ],
    experience: { amount: 100 },
    description: 'A delightful contribution to my collection. Your artistic spirit shines through, darling.',
  },
  {
    rarity: 'Moonlight',
    currencies: [
      { currency: 'Shards', amount: 1000 },
      { currency: 'Crowns', amount: 50 },
      { currency: 'Rifts', amount: 5 },
    ],
    experience: { amount: 250 },
    description: 'Impressive work! Your talent is beginning to catch my attention in all the right ways.',
  },
  {
    rarity: 'Celestial',
    currencies: [
      { currency: 'Shards', amount: 2000 },
      { currency: 'Crowns', amount: 100 },
      { currency: 'Embers', amount: 25 },
      { currency: 'Rifts', amount: 10 },
    ],
    experience: { amount: 500 },
    description: 'Absolutely divine! This masterpiece is worthy of my most exclusive collection. You have truly earned my admiration.',
  },
] as const;

export function getSkinRewardByRarity(rarity: SkinRarity): SkinApprovalReward | undefined {
  return skinApprovalRewards.find((reward) => reward.rarity === rarity);
}

export function getRarityEmoji(rarity: SkinRarity): string {
  const emojis: Record<SkinRarity, string> = {
    Stardust: '⭐',
    Moonlight: '🌙', 
    Celestial: '✨',
  };
  return emojis[rarity];
}

export function getRarityColor(rarity: SkinRarity): number {
  const colors: Record<SkinRarity, number> = {
    Stardust: 0xFFD700, // Gold
    Moonlight: 0x9370DB, // Medium Purple
    Celestial: 0xFF69B4, // Hot Pink
  };
  return colors[rarity];
}

