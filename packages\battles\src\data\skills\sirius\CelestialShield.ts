import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Celestial Shield',
  name: 'Celestial Shield',
  element: 'Sirius',
  manaCost: 12,
  cooldown: 3,
  description: 'Surround yourself with celestial light, reducing incoming damage and healing over time.',
  execute: (caster) => {
    return {
      appliedEffect: {
        id: 'celestial_shield',
        name: 'Celestial Shield',
        duration: 3,
        potency: 0.7, // 30% damage reduction + healing
        sourceId: caster.id,
      },
      log: `${caster.name} creates a Celestial Shield, reducing damage and providing healing!`,
    };
  },
});
