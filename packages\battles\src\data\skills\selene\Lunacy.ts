import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: '<PERSON><PERSON>',
  name: '<PERSON><PERSON>',
  element: '<PERSON><PERSON>',
  manaCost: 30,
  cooldown: 5,
  description: 'The caster is driven to lunacy, increasing their attack speed and causing their attacks to have a chance to confuse the target.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Frenzy',
        name: '<PERSON><PERSON>',
        duration: 3,
        sourceId: caster.id,
      },
      log: `${caster.name} is driven to lunacy.`,
    };
  },
});
