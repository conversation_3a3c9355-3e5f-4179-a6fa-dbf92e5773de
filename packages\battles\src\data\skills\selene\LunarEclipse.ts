import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Lunar Eclipse',
  name: 'Lunar Eclipse',
  element: 'Selene',
  manaCost: 30,
  cooldown: 5,
  description: 'Invoke a lunar eclipse that deals massive damage and applies multiple debuffs.',
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const eclipseDamage = Math.round(damage * 2.1);

    return {
      damage: eclipseDamage,
      isCritical,
      appliedEffect: {
        id: 'lunar_eclipse',
        name: 'Lunar Eclipse',
        duration: 4,
        potency: -8, // Multiple stat reduction
        sourceId: caster.id,
      },
      log: `${caster.name} invokes a Lunar Eclipse, devastating ${target.name} for ${eclipseDamage} damage${isCritical ? ' (CRIT!)' : ''} and cursing them!`,
    };
  },
});
