import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Flame Dash',
  name: 'Flame Dash',
  element: 'Sirius',
  manaCost: 15,
  cooldown: 3,
  description: 'The caster dashes forward, leaving a trail of fire that damages enemies.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Burn',
        name: '<PERSON> Dash',
        duration: 2,
        sourceId: caster.id,
      },
      log: `${caster.name} dashes forward, leaving a trail of fire.`,
    };
  },
});
