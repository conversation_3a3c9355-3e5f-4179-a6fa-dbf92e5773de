import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Waxing Moon',
  name: 'Waxing Moon',
  element: 'Selene',
  manaCost: 35,
  cooldown: 5,
  description: 'The waxing moon empowers the caster, increasing their healing received and causing their attacks to have a chance to restore mana.',
  execute: (caster, target, formulas) => {
    // This would require a status effect system to handle the buffs.
    return {
      log: `${caster.name} is empowered by the waxing moon.`,
    };
  },
});
