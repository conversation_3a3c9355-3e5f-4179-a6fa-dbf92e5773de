import fs from 'node:fs';
import path from 'node:path';
import type { Effect } from './defineEffect';

const EFFECTS_DIR = import.meta.dirname;
const EFFECT_FILES: string[] = [];

// Recursively collect all .ts files in subfolders (excluding index.ts)
function collect(dir: string) {
  const entries = fs.readdirSync(dir, { withFileTypes: true });
  for (const entry of entries) {
    const fullPath = path.join(dir, entry.name);
    if (entry.isDirectory()) {
      collect(fullPath);
    } else if (entry.isFile() && entry.name.endsWith('.ts') && entry.name !== 'index.ts') {
      EFFECT_FILES.push(fullPath);
    }
  }
}

collect(EFFECTS_DIR);

const effectsSet: Record<string, Set<Effect>> = {};

for await (const file of EFFECT_FILES) {
  const effect = (await import(file)).default as Effect;
  if (!effectsSet[effect.element]) {
    effectsSet[effect.element] = new Set();
  }

  effectsSet[effect.element as keyof typeof effectsSet]!.add(effect);
}

export const effects = effectsSet;
