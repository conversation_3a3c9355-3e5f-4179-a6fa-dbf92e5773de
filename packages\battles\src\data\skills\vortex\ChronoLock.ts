import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Chrono Lock',
  name: 'Chrono Lock',
  element: 'Vortex',
  manaCost: 22,
  cooldown: 4,
  description: 'Lock the target in time, preventing them from acting and dealing damage over time.',
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);

    return {
      damage: Math.round(damage * 0.7),
      isCritical,
      appliedEffect: {
        id: 'chrono_lock',
        name: 'Chrono Lock',
        duration: 2,
        potency: Math.round(caster.stats.attack * 0.3), // DoT effect
        sourceId: caster.id,
      },
      log: `${caster.name} locks ${target.name} in time for ${Math.round(damage * 0.7)} damage${isCritical ? ' (CRIT!)' : ''} and temporal imprisonment!`,
    };
  },
});
