import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Overheat',
  name: 'Overheat',
  element: 'Sirius',
  manaCost: 40,
  cooldown: 6,
  description: 'The caster overheats, increasing their damage but also taking damage over time.',
  execute: (caster, target, formulas) => {
    // This would require a status effect system to handle the buff and DoT.
    return {
      log: `${caster.name} overheats.`,
    };
  },
});
