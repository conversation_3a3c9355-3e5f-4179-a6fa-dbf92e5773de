{"compilerOptions": {"target": "ESNext", "jsx": "react-jsx", "lib": ["ESNext"], "moduleDetection": "force", "module": "Preserve", "moduleResolution": "bundler", "allowImportingTsExtensions": true, "allowJs": true, "strict": true, "noFallthroughCasesInSwitch": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": false, "noUncheckedIndexedAccess": true, "noUnusedLocals": false, "noUnusedParameters": false, "noEmit": true, "verbatimModuleSyntax": true, "skipLibCheck": true}}