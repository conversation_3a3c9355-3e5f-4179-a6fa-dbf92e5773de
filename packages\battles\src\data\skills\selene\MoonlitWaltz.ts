import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Moonlit Waltz',
  name: 'Moonlit Waltz',
  element: '<PERSON><PERSON>',
  manaCost: 25,
  cooldown: 5,
  description: 'The caster performs a graceful waltz, dodging all incoming attacks for one turn.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Evasion Up',
        name: 'Moonlit Waltz',
        duration: 1,
        sourceId: caster.id,
      },
      log: `${caster.name} performs a Moonlit Waltz, gracefully dodging all incoming attacks.`,
    };
  },
});
