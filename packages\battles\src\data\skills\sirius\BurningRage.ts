import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Burning Rage',
  name: 'Burning Rage',
  element: 'Sirius',
  manaCost: 30,
  cooldown: 5,
  description: 'The caster is filled with a burning rage, increasing their attack damage but reducing their defense.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Dark Side of the Moon Effect',
        name: 'Burning Rage',
        duration: 3,
        sourceId: caster.id,
      },
      log: `${caster.name} is filled with a burning rage.`,
    };
  },
});
