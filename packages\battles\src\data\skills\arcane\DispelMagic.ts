import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Dispel Magic',
  name: 'Dispel Magic',
  element: 'Arcane',
  manaCost: 15,
  cooldown: 2,
  description: 'Removes all magical effects from the target.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Dispel',
        name: '<PERSON>spel',
        duration: 0,
        sourceId: caster.id,
      },
      log: `${caster.name} dispels all magical effects from ${target.name}.`,
    };
  },
});
