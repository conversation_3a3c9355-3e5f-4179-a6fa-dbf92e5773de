/* biome-ignore-all lint/complexity/useOptionalChain: Do not like the linting */

import { CDN } from '@megami/cdn';
import * as config from '@megami/config/lib/team';
import { MegamiDatabaseClient } from '@megami/database';
import { MegamiSearch } from '@megami/search';
import {
  ActivityType,
  type ApplicationCommandDataResolvable,
  Client,
  type ClientOptions,
  GatewayIntentBits,
} from 'discord.js';
import { ClusterClient, getInfo } from 'discord-hybrid-sharding';
import type { Logger } from 'winston';
import { defineCommands } from '../handlers/command';
import { defineEvents } from '../handlers/event';
import { defineTasks } from '../handlers/tasks';
import { logger } from './logger';
import { MegamiScheduler } from './scheduler';
import { MegamiStorage } from './storage';

export class MegamiClient extends Client {
  // @ts-expect-error - we define this later
  public override database: MegamiDatabaseClient;
  // @ts-expect-error - we define this later
  public override cdn: CDN;
  public override logger: Logger = logger;
  public override storage: MegamiStorage;
  public override cluster: ClusterClient;
  public override scheduler: MegamiScheduler;
  public override search: MegamiSearch;

  constructor(options?: ClientOptions) {
    super({
      ...options,
      presence: {
        status: 'dnd',
        activities: [
          {
            name: 'Hard to Get!',
            type: ActivityType.Playing,
          },
        ],
      },
      shards: getInfo().SHARD_LIST,
      shardCount: getInfo().TOTAL_SHARDS,
      intents: [GatewayIntentBits.Guilds],
    });

    this.storage = new MegamiStorage();
    this.cluster = new ClusterClient(this);
    this.scheduler = new MegamiScheduler(this);
    this.search = new MegamiSearch();
  }

  public async start(token: string) {
    await defineCommands(this);
    await defineEvents(this);
    await defineTasks(this);

    // Redefine because environment variables would not be set in start-up but only shortly before start command
    this.database = new MegamiDatabaseClient({
      url: process.env.DATABASE_URL || 'file:./dev.db',
    });
    this.cdn = new CDN({
      cloud_name: process.env.CLOUDINARY_CLOUD_NAME || '',
      api_key: process.env.CLOUDINARY_API_KEY || '',
      api_secret: process.env.CLOUDINARY_API_SECRET || '',
    });

    // Seed database with initial data
    await this.database.seed();

    await super.login(token);

    if (this.application) {
      const commands = this.storage.commands.toJSON();

      const configured = commands.filter((command) => command.config.only);
      const remaining = commands.filter((command) => !command.config.only);

      await this.application.commands.set(
        remaining.map((command) => command.builder.toJSON()) as ApplicationCommandDataResolvable[]
      );

      await this.application.commands.set(
        configured.map((command) => command.builder.toJSON()) as ApplicationCommandDataResolvable[],
        config.server
      );
    }
  }
}
