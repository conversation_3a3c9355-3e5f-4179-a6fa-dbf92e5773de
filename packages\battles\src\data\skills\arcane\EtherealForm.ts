import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Ethereal Form',
  name: 'Ethereal Form',
  element: 'Arcane',
  manaCost: 35,
  cooldown: 5,
  description: 'The caster becomes ethereal, making them immune to physical attacks.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Magic Immunity',
        name: 'Ethereal Form',
        duration: 2,
        sourceId: caster.id,
      },
      log: `${caster.name} becomes ethereal, immune to physical attacks.`,
    };
  },
});
