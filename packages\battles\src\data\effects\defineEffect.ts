import type { formulas } from '../../formulas';
import type { ActiveStatusEffect, BattleEntity } from '../../types';

export type EffectElement = 'Arcane' | 'Vortex' | 'Sirius' | 'Selene';

export interface Effect {
  id: string;
  name: string;
  element: EffectElement;
  description: string;
  onTurnStart?: (target: BattleEntity, effect: ActiveStatusEffect, operators: typeof formulas) => EffectResult;
  onApply?: (target: BattleEntity, effect: ActiveStatusEffect, operators: typeof formulas) => EffectResult;
  onExpire?: (target: BattleEntity, effect: ActiveStatusEffect, operators: typeof formulas) => EffectResult;
}

export interface EffectResult {
  damage?: number;
  log?: string;
  [key: string]: any;
}

export function defineEffect(effect: Effect): Effect {
  return effect;
}
