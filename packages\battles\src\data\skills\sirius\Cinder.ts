import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Cinder',
  name: 'Cinder',
  element: 'Sirius',
  manaCost: 15,
  cooldown: 3,
  description: 'The caster throws a cinder at the target, dealing small damage and reducing their accuracy.',
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const finalDamage = Math.round(damage * 0.8);
    // This would also require a status effect system to handle the accuracy reduction.
    return {
      damage: finalDamage,
      isCritical,
      log: `${caster.name} throws a cinder at ${target.name}, dealing ${finalDamage} damage and reducing their accuracy${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
