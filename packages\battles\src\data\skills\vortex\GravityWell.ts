import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Gravity Well',
  name: 'Gravity Well',
  element: 'Vortex',
  manaCost: 18,
  cooldown: 3,
  description: 'Create a gravity well that crushes the target, dealing heavy damage.',
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    return {
      damage: Math.round(damage * 1.4),
      isCritical,
      log: `${caster.name} creates a crushing gravity well around ${target.name} for ${Math.round(damage * 1.4)} damage${isCritical ? ' (CRIT!)' : ''}!`,
    };
  },
});
