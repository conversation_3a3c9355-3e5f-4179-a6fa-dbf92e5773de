import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Forcefield',
  name: 'Forcefield',
  element: 'Vortex',
  manaCost: 30,
  cooldown: 5,
  description: 'Creates a forcefield around the caster that absorbs a certain amount of damage.',
  execute: (caster, target, formulas) => {
    // This would require a status effect system to handle the shield.
    return {
      log: `${caster.name} creates a Forcefield.`,
    };
  },
});
