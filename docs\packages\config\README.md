# `packages/config`

This package contains the central configuration for the "Megami" project. It defines all the core game data.

## Installation

To install dependencies:

```bash
bun install
```

## Usage

To run:

```bash
bun run index.ts
```

---

## Overview

The `config` package provides a centralized location for all game-related configuration data. This data is used throughout the application to drive game mechanics, define rewards, and more.

### Activity (`lib/activity.ts`)

Defines daily, weekly, and monthly rewards for player activity. It also includes functions for calculating streaks and checking if a player can claim a reward.

### Currencies (`lib/currencies.ts`)

Defines the different types of currencies in the game, such as Shards, Crowns, Embers, and Rifts. Each currency has a name, description, and tier.

### Drops (`lib/drops.ts`)

Configures the drop system, including different drop types, rarity odds, and penalties. It also includes functions for calculating which rarity a player gets and whether a penalty should be applied.

### Elements (`lib/elements.ts`)

Defines the elemental types in the game (Arcane, Vortex, Sirius, Selene), including their advantages and disadvantages.

### Emotes (`lib/emotes.ts`)

A map of Discord emotes used throughout the application. It also includes a helper function `getIDFromEmote` to extract the ID from an emote string.

### Items (`lib/items.ts`)

A comprehensive list of all items in the game, including their type, rarity, effects, and other metadata.

### Rewards (`lib/rewards.ts`)

Defines the rewards for various in-game activities, such as skin approvals.

### Team (`lib/team.ts`)

A list of team members and their roles, with a function to check a user's role and permissions.

### Themes (`lib/themes.ts`)

A list of available themes for the application.
