import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Void Step',
  name: 'Void Step',
  element: 'Vortex',
  manaCost: 14,
  cooldown: 2,
  description: 'Step through the void to strike with increased critical chance and speed boost.',
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    // Force higher crit chance for this skill
    const enhancedCrit = isCritical || formulas.percentage(0.3); // 30% bonus crit chance
    const finalDamage = enhancedCrit ? Math.round(damage * 1.5) : damage;

    return {
      damage: finalDamage,
      isCritical: enhancedCrit,
      appliedEffect: {
        id: 'void_step_boost',
        name: 'Void Step',
        duration: 2,
        potency: 5, // +5 speed
        sourceId: caster.id,
      },
      log: `${caster.name} steps through the void to strike ${target.name} for ${finalDamage} damage${enhancedCrit ? ' (CRIT!)' : ''} and gains speed!`,
    };
  },
});
