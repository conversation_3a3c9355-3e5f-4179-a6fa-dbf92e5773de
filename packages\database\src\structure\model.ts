import * as schema from '../schema/index';
import type { MegamiDatabaseClient } from './client';

export class BaseModel {
  public client: MegamiDatabaseClient;
  public schemas = schema;

  constructor(client: MegamiDatabaseClient) {
    this.client = client;
  }

  /**
   * Seed initial data for this model
   * Override in subclasses that need seeding
   */
  public async seed(): Promise<void> {
    // Default implementation does nothing
    // Override in subclasses that need seeding
  }
}
