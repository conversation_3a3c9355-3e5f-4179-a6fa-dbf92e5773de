import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Dream Eater',
  name: 'Dream Eater',
  element: 'Selene',
  manaCost: 30,
  cooldown: 5,
  description: 'The caster consumes the dreams of a sleeping target, dealing massive damage and healing the caster.',
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const finalDamage = Math.round(damage * 2);
    const healAmount = Math.round(finalDamage * 0.5);
    caster.health += healAmount;
    return {
      damage: finalDamage,
      isCritical,
      log: `${caster.name} consumes the dreams of ${target.name} for ${finalDamage} damage and heals for ${healAmount}${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
