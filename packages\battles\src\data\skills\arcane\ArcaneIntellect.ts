import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Arcane Intellect',
  name: 'Arcane Intellect',
  element: 'Arcane',
  manaCost: 30,
  cooldown: 5,
  description: "Boosts the caster's intelligence, increasing their spell power.",
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Intelligence Up',
        name: 'Intelligence Up',
        duration: 4,
        sourceId: caster.id,
      },
      log: `${caster.name} boosts their arcane intellect, increasing their spell power.`,
    };
  },
});
