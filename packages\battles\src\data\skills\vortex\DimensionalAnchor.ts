import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Dimensional Anchor',
  name: 'Dimensional Anchor',
  element: 'Vortex',
  manaCost: 35,
  cooldown: 5,
  description: 'The target is anchored to their current dimension, preventing them from being teleported or moved.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Root',
        name: 'Dimensional Anchor',
        duration: 3,
        sourceId: caster.id,
      },
      log: `${caster.name} anchors ${target.name} to their current dimension.`,
    };
  },
});
