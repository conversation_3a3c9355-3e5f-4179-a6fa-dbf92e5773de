import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: '<PERSON>',
  name: '<PERSON>',
  element: 'Vortex',
  manaCost: 20,
  cooldown: 4,
  description: 'The caster creates an echo of their last used skill, casting it again at no cost.',
  execute: (caster, target, formulas) => {
    // This would require a complex system to track and cast previous skills.
    return {
      log: `${caster.name} creates an echo of their last skill.`,
    };
  },
});
