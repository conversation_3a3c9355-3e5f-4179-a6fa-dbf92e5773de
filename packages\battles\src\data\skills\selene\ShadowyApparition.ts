import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Shadowy Apparition',
  name: 'Shadowy Apparition',
  element: '<PERSON><PERSON>',
  manaCost: 25,
  cooldown: 4,
  description: 'Summons a shadowy apparition to haunt the target, dealing damage over time.',
  execute: (caster, target, formulas) => {
    // This would require a status effect system to handle the DoT.
    return {
      log: `${caster.name} summons a shadowy apparition to haunt ${target.name}.`,
    };
  },
});
