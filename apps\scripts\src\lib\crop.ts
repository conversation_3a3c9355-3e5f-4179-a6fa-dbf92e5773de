/* biome-ignore-all lint/suspicious/useAwait: We need this to be a promise */

import * as prompt from '@clack/prompts';
import ffmpeg from 'fluent-ffmpeg';

/**
 * Crops a video of 1920x1088 to 9:16 (612x1088), center-aligned.
 */
export async function crop(input: string, output: string, target?: string): Promise<void> {
  return new Promise((resolve, reject) => {
    const inputWidth = 1280;
    const inputHeight = 720;
    const targetAspectRatio = 9 / 16;
    const targetWidth = Math.floor(inputHeight * targetAspectRatio); // 612
    const cropX = Math.floor((inputWidth - targetWidth) / 2); // crop from center

    ffmpeg(input)
      .videoFilters(target ? `crop=${target}` : `crop=${targetWidth}:${inputHeight}:${cropX}:0`)
      .outputOptions([
        '-c:v libx264',
        '-crf 18', // High quality (lower CRF = better quality, 18-23 is typical)
        '-preset veryslow', // Better compression efficiency
        '-c:a copy', // Copy audio without re-encoding
      ])
      .on('start', (command) => {
        prompt.log.info(`Running: ${command}`);
      })
      .on('error', (err, _stdout, stderr) => {
        prompt.log.error(`Error: ${err.message}`);
        prompt.log.error(`ffmpeg stderr: ${stderr}`);
        reject(err);
      })
      .on('end', () => {
        prompt.log.info('Cropping finished successfully!');
        resolve();
      })
      .save(output);
  });
}
