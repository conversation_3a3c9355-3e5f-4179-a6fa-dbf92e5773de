'use client';

import { motion } from 'framer-motion';
import { Navbar } from '@/components/navbar';
import { PricingCard } from '@megami/ui/components/jules/pricing-card';

const specialPacks = [
  {
    title: "Be<PERSON><PERSON>'s Blessing",
    price: '$3.99',
    features: [
      '200 Crowns',
      '1 Alt Art Unlock',
      '2 Basic Drops',
      'Exclusive Frame',
    ],
    buttonText: 'Purchase',
  },
  {
    title: 'Riftmaster Bundle',
    price: '$9.99',
    features: [
      '500 Crowns',
      '5 Enhanced Drops',
      'Rift Regen x2 (3 days)',
    ],
    buttonText: 'Purchase',
    isFeatured: true,
  },
  {
    title: 'Celestial Starter',
    price: '$14.99',
    features: [
      '750 Crowns',
      '1 Celestial Box (10% guaranteed)',
      'Mystic Mirror',
    ],
    buttonText: 'Purchase',
  },
  {
    title: 'Prestige Vault',
    price: '$29.99',
    features: [
      '1600 Crowns',
      '1 Exclusive Title',
      '1 Penalty Ward',
      'Alt Art Key',
    ],
    buttonText: 'Purchase',
  },
];

export default function SpecialPacksPage() {
  return (
    <main className="min-h-screen w-full bg-black text-white">
      <Navbar />
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1 }}
        className="container mx-auto px-4 py-8"
      >
        <h1 className="text-5xl font-bold text-center mb-4">Special Packs</h1>
        <p className="text-lg text-gray-400 text-center mb-12">
          Get a head start with our one-time special packs.
        </p>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {specialPacks.map((pack, i) => (
            <motion.div
              key={pack.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: i * 0.1 }}
            >
              <PricingCard {...pack} />
            </motion.div>
          ))}
        </div>
      </motion.div>
    </main>
  );
}
