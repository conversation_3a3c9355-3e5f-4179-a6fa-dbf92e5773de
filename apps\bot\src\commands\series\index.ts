import { getObject, translate } from '@megami/locale';
import type { SlashCommandSubcommandBuilder } from 'discord.js';
import { defineCommand } from '../../handlers/command';
import search from './modules/search';

export default defineCommand((builder) => ({
  builder: builder
    .setName(translate('commands.series.name'))
    .setNameLocalizations(getObject('commands.series.name'))
    .setDescription(translate('commands.series.description'))
    .setDescriptionLocalizations(getObject('commands.series.description'))
    .addSubcommand(search.builder as SlashCommandSubcommandBuilder),

  config: {},

  async execute(interaction) {
    const subcommand = interaction.options.getSubcommand();

    switch (subcommand) {
      case translate('commands.series.modules.search.name'): {
        await search.execute(interaction);
        break;
      }

      default: {
        await interaction.reply({
          content: 'Unknown subcommand.',
          ephemeral: true,
        });
      }
    }
  },
}));
