import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Nether Vortex',
  name: 'Nether Vortex',
  element: 'Vortex',
  manaCost: 50,
  cooldown: 7,
  description: 'Creates a nether vortex that damages and drains mana from all enemies in an area over time.',
  execute: (caster, target, formulas) => {
    // This would require a more complex targeting system, a way to drain mana, and a status effect system to handle the DoT.
    return {
      log: `${caster.name} creates a Nether Vortex.`,
    };
  },
});
