import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Spell Steal',
  name: 'Spell Steal',
  element: 'Arcane',
  manaCost: 25,
  cooldown: 4,
  description: 'Steals a spell from the target, allowing the caster to use it once.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Disarm',
        name: 'Spell Steal',
        duration: 1,
        sourceId: caster.id,
      },
      log: `${caster.name} steals a spell from ${target.name}.`,
    };
  },
});
