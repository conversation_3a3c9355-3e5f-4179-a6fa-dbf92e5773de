import { createGoogleGenerativeA<PERSON> as defineGoogle } from '@ai-sdk/google';
import { createGroq as defineGroq } from '@ai-sdk/groq';
import { MegamiCharacterAI } from './characters';

export interface AIClientOptions {
  API_KEY: string;
}

export type Providers = 'gemini' | 'groq';

export type MegamiAIOptions = Record<Providers, AIClientOptions>;

export const MAPPINGS = {
  gemini: defineGoogle,
  groq: defineGroq,
} as const;

export class MegamiAI {
  public options: MegamiAIOptions;
  public providers: {
    google: ReturnType<typeof defineGoogle>;
    groq: ReturnType<typeof defineGroq>;
  };

  public character: MegamiCharacterAI;

  constructor(options: MegamiAIOptions) {
    this.options = options;

    // @ts-expect-error - we are defining the keys below.
    this.providers = {};

    for (const [key, value] of Object.entries(options)) {
      this.providers = {
        ...this.providers,
        [key]: MAPPINGS[key as keyof typeof MAPPINGS]({ apiKey: value.API_KEY }),
      };
    }

    this.character = new MegamiCharacterAI(this);
  }
}
