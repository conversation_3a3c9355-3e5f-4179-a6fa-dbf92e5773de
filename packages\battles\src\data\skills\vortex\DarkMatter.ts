import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Dark Matter',
  name: 'Dark Matter',
  element: 'Vortex',
  manaCost: 35,
  cooldown: 5,
  description: 'The target is shrouded in dark matter, reducing their damage and causing them to take damage over time.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Dark Matter Effect',
        name: 'Dark Matter',
        duration: 3,
        sourceId: caster.id,
      },
      log: `${caster.name} shrouds ${target.name} in dark matter.`,
    };
  },
});
