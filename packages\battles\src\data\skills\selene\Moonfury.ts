import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Moonfury',
  name: 'Moonfury',
  element: 'Selene',
  manaCost: 35,
  cooldown: 5,
  description: 'The caster is filled with moonfury, increasing their attack speed and causing their attacks to have a chance to strike twice.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Moonfury Effect',
        name: 'Moonfury',
        duration: 3,
        sourceId: caster.id,
      },
      log: `${caster.name} is filled with moonfury.`,
    };
  },
});
