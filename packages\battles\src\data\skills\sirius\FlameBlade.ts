import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Flame Blade',
  name: 'Flame Blade',
  element: 'Sirius',
  manaCost: 15,
  cooldown: 2,
  description: 'The caster\'s weapon is engulfed in flames, causing their next basic attack to deal bonus fire damage.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Attack Up',
        name: 'Flame Blade',
        duration: 1,
        sourceId: caster.id,
      },
      log: `${caster.name} engulfs their weapon in flames.`,
    };
  },
});
