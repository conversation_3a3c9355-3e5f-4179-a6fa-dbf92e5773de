import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Shadow Stalk',
  name: 'Shadow Stalk',
  element: '<PERSON><PERSON>',
  manaCost: 20,
  cooldown: 4,
  description: 'The caster becomes invisible, making them untargetable for a short time.',
  execute: (caster, target, formulas) => {
    // This would require a status effect system to handle invisibility.
    return {
      log: `${caster.name} stalks the shadows, becoming invisible.`,
    };
  },
});
