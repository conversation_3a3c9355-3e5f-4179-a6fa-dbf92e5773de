import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Unstable Magic',
  name: 'Unstable Magic',
  element: 'Arcane',
  manaCost: 10,
  cooldown: 0,
  description: 'A chaotic spell with a random effect.',
  execute: (caster, target, formulas) => {
    // This would require a system for handling random effects.
    return {
      log: `${caster.name} casts a chaotic spell with unpredictable results.`,
    };
  },
});
