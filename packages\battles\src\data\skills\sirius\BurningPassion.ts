import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Burning Passion',
  name: 'Burning Passion',
  element: 'Sirius',
  manaCost: 30,
  cooldown: 5,
  description: 'The caster is filled with a burning passion, increasing their attack speed and causing their attacks to burn the target.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Burning Passion Effect',
        name: 'Burning Passion',
        duration: 3,
        sourceId: caster.id,
      },
      log: `${caster.name} is filled with a burning passion.`,
    };
  },
});
