.evangelion {
  --background: oklch(0.95 0.01 300);
  --foreground: oklch(0.2 0.05 300);
  --card: oklch(0.98 0.02 300);
  --card-foreground: oklch(0.2 0.05 300);
  --popover: oklch(0.98 0.02 300);
  --popover-foreground: oklch(0.2 0.05 300);
  --primary: oklch(0.7 0.15 280);
  --primary-foreground: oklch(0.95 0.01 300);
  --secondary: oklch(0.8 0.1 150);
  --secondary-foreground: oklch(0.2 0.05 300);
  --muted: oklch(0.9 0.03 300);
  --muted-foreground: oklch(0.5 0.04 300);
  --accent: oklch(0.7 0.15 150);
  --accent-foreground: oklch(0.95 0.01 300);
  --destructive: oklch(0.6 0.15 30);
  --destructive-foreground: oklch(0.95 0.01 300);
  --border: oklch(0.85 0.04 300);
  --input: oklch(0.9 0.04 300);
  --ring: oklch(0.7 0.15 280);
  --chart-1: oklch(0.7 0.15 280);
  --chart-2: oklch(0.8 0.1 150);
  --chart-3: oklch(0.6 0.12 290);
  --chart-4: oklch(0.7 0.08 140);
  --chart-5: oklch(0.8 0.06 160);
  --sidebar: oklch(0.96 0.01 300);
  --sidebar-foreground: oklch(0.2 0.05 300);
  --sidebar-primary: oklch(0.7 0.15 280);
  --sidebar-primary-foreground: oklch(0.95 0.01 300);
  --sidebar-accent: oklch(0.7 0.15 150);
  --sidebar-accent-foreground: oklch(0.95 0.01 300);
  --sidebar-border: oklch(0.9 0.03 300);
  --sidebar-ring: oklch(0.7 0.15 280);
  --font-sans: Geist, sans-serif;
  --font-serif: "Lora", Georgia, serif;
  --font-mono: "Fira Code", "Courier New", monospace;
  --radius: 0.5rem;
  --shadow-2xs: 1px 2px 5px 1px hsl(0 0% 0% / 0.03);
  --shadow-xs: 1px 2px 5px 1px hsl(0 0% 0% / 0.03);
  --shadow-sm:
    1px 2px 5px 1px hsl(0 0% 0% / 0.06), 1px 1px 2px 0px hsl(0 0% 0% / 0.06);
  --shadow:
    1px 2px 5px 1px hsl(0 0% 0% / 0.06), 1px 1px 2px 0px hsl(0 0% 0% / 0.06);
  --shadow-md:
    1px 2px 5px 1px hsl(0 0% 0% / 0.06), 1px 2px 4px 0px hsl(0 0% 0% / 0.06);
  --shadow-lg:
    1px 2px 5px 1px hsl(0 0% 0% / 0.06), 1px 4px 6px 0px hsl(0 0% 0% / 0.06);
  --shadow-xl:
    1px 2px 5px 1px hsl(0 0% 0% / 0.06), 1px 8px 10px 0px hsl(0 0% 0% / 0.06);
  --shadow-2xl: 1px 2px 5px 1px hsl(0 0% 0% / 0.15);
}

.evangelion-dark {
  --background: oklch(0.2 0.05 300);
  --foreground: oklch(0.9 0.02 150);
  --card: oklch(0.25 0.06 300);
  --card-foreground: oklch(0.9 0.02 150);
  --popover: oklch(0.25 0.06 300);
  --popover-foreground: oklch(0.9 0.02 150);
  --primary: oklch(0.8 0.15 280);
  --primary-foreground: oklch(0.2 0.05 300);
  --secondary: oklch(0.6 0.12 150);
  --secondary-foreground: oklch(0.9 0.02 150);
  --muted: oklch(0.25 0.06 300);
  --muted-foreground: oklch(0.7 0.04 150);
  --accent: oklch(0.8 0.15 150);
  --accent-foreground: oklch(0.2 0.05 300);
  --destructive: oklch(0.7 0.2 30);
  --destructive-foreground: oklch(0.2 0.05 300);
  --border: oklch(0.3 0.07 300);
  --input: oklch(0.28 0.07 300);
  --ring: oklch(0.8 0.15 280);
  --chart-1: oklch(0.8 0.15 280);
  --chart-2: oklch(0.7 0.12 150);
  --chart-3: oklch(0.6 0.1 290);
  --chart-4: oklch(0.5 0.08 140);
  --chart-5: oklch(0.6 0.06 160);
  --sidebar: oklch(0.18 0.05 300);
  --sidebar-foreground: oklch(0.9 0.02 150);
  --sidebar-primary: oklch(0.8 0.15 280);
  --sidebar-primary-foreground: oklch(0.2 0.05 300);
  --sidebar-accent: oklch(0.8 0.15 150);
  --sidebar-accent-foreground: oklch(0.2 0.05 300);
  --sidebar-border: oklch(0.28 0.07 300);
  --sidebar-ring: oklch(0.8 0.15 280);
  --font-sans: Geist, sans-serif;
  --font-serif: "Lora", Georgia, serif;
  --font-mono: "Fira Code", "Courier New", monospace;
  --radius: 0.5rem;
  --shadow-2xs: 1px 2px 5px 1px hsl(0 0% 0% / 0.03);
  --shadow-xs: 1px 2px 5px 1px hsl(0 0% 0% / 0.03);
  --shadow-sm:
    1px 2px 5px 1px hsl(0 0% 0% / 0.06), 1px 1px 2px 0px hsl(0 0% 0% / 0.06);
  --shadow:
    1px 2px 5px 1px hsl(0 0% 0% / 0.06), 1px 1px 2px 0px hsl(0 0% 0% / 0.06);
  --shadow-md:
    1px 2px 5px 1px hsl(0 0% 0% / 0.06), 1px 2px 4px 0px hsl(0 0% 0% / 0.06);
  --shadow-lg:
    1px 2px 5px 1px hsl(0 0% 0% / 0.06), 1px 4px 6px 0px hsl(0 0% 0% / 0.06);
  --shadow-xl:
    1px 2px 5px 1px hsl(0 0% 0% / 0.06), 1px 8px 10px 0px hsl(0 0% 0% / 0.06);
  --shadow-2xl: 1px 2px 5px 1px hsl(0 0% 0% / 0.15);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
}
