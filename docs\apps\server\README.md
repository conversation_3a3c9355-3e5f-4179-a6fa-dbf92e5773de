# `apps/server`

This app is a server that runs scheduled jobs.

## Installation

To install dependencies:

```bash
bun install
```

## Usage

To run the server:

```bash
bun run index.ts
```

---

## Architecture

The server is designed to run scheduled jobs. It has a `Scheduler` class that uses `cron` to run jobs at specified intervals.

### `Server` Class

The `Server` class is the main entry point for the server. It initializes the scheduler and starts the jobs.

### Scheduler

The `Scheduler` class in `src/structure/scheduler.ts` is responsible for managing and running the scheduled jobs. Jobs are defined in the `src/jobs` directory and are loaded by the scheduler.

### Pinterest Scraper

The `PinterestScraper` class in `src/structure/pinterest.ts` uses Puppeteer to scrape images from Pinterest. It can scrape a single pin and all its related images.

### Auto-Tagger

The `autotagger.ts` library provides a function for automatically tagging images using an external API. It categorizes tags as "internal" or "external" based on a confidence score.
