{"$schema": "https://ui.shadcn.com/schema.json", "style": "new-york", "rsc": true, "tsx": true, "tailwind": {"config": "", "css": "src/styles/globals.css", "baseColor": "neutral", "cssVariables": true}, "iconLibrary": "lucide", "aliases": {"components": "@megami/ui/components", "utils": "@megami/ui/lib/utils", "hooks": "@megami/ui/hooks", "lib": "@megami/ui/lib", "ui": "@megami/ui/components/registry"}}