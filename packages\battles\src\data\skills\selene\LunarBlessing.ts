import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Lunar Blessing',
  name: 'Lunar Blessing',
  element: 'Se<PERSON>',
  manaCost: 25,
  cooldown: 4,
  description: 'The caster is blessed by the moon, increasing their evasion and critical hit chance.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Lunar Blessing Effect',
        name: 'Lunar Blessing',
        duration: 3,
        sourceId: caster.id,
      },
      log: `${caster.name} is blessed by the moon.`,
    };
  },
});
