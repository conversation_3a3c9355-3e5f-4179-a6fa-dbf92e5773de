import type { SecretsListResponse } from '@dopplerhq/node-sdk';
import { parse } from './lib/schema';

export interface Secret {
  computed: string;
  raw: string;
  note: string;
}

export async function defineVariables(token: string) {
  const url = `https://api.doppler.com/v3/configs/config/secrets?project=${encodeURI('megami')}&config=${encodeURI('prd')}&include_dynamic_secrets=false&include_managed_secrets=true`;
  const options = {
    method: 'GET',
    headers: { accept: 'application/json', authorization: `Bearer ${token}` },
  };

  const { secrets } = await fetch(url, options).then((response) => response.json() as Promise<SecretsListResponse>);

  return parse(secrets);
}
