'use client';

import { motion } from 'framer-motion';
import { Navbar } from '@/components/navbar';
import { TimelineItem } from '@megami/ui/components/jules/timeline-item';

const changelogs = [
  {
    title: 'Version 1.1.0',
    date: 'July 29, 2025',
    changes: [
      'Added new summer event.',
      'Fixed a bug with the trading system.',
      'Improved performance of the gacha animation.',
    ],
  },
  {
    title: 'Version 1.0.0',
    date: 'July 20, 2025',
    changes: [
      'Megami is now live!',
      'Initial release with all basic features.',
    ],
  },
];

export default function ChangelogPage() {
  return (
    <main className="min-h-screen w-full bg-black text-white">
      <Navbar />
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1 }}
        className="container mx-auto px-4 py-8"
      >
        <h1 className="text-4xl font-bold text-center mb-8">Changelog</h1>
        <div className="space-y-8">
          {changelogs.map((log, i) => (
            <motion.div
              key={log.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: i * 0.2 }}
            >
              <TimelineItem title={log.title} date={log.date}>
                <ul className="list-disc list-inside">
                  {log.changes.map((change) => (
                    <li key={change}>{change}</li>
                  ))}
                </ul>
              </TimelineItem>
            </motion.div>
          ))}
        </div>
      </motion.div>
    </main>
  );
}
