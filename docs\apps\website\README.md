# `apps/website`

This is the main website for the "Megami" project.

## Getting Started

First, run the development server:

```bash
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Architecture

The website is a Next.js application with a standard project structure.

### Pages

The `src/app` directory contains the pages for the website:

-   **/ (landing page)**: The main landing page of the website.
-   **/blog**: A blog with posts about the project.
-   **/changelog**: A log of all the changes made to the project.
-   **/commands**: A list of all the available commands for the Discord bot.
-   **/features**: A description of the features of the project.
-   **/premium**: Information about the premium features of the project.
-   **/team**: A list of the team members.

### Components

The `src/components` directory contains reusable components that are used across multiple pages, such as the `Navbar` and `Footer`.

### Styling

The website uses CSS Modules for styling, with global styles defined in `src/styles/globals.css`.
