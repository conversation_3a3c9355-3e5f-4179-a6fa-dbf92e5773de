import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Phoenix Fire',
  name: 'Phoenix Fire',
  element: 'Sirius',
  manaCost: 50,
  cooldown: 7,
  description: 'The caster is reborn in phoenix fire, healing to full health and dealing damage to all nearby enemies.',
  execute: (caster, target, formulas) => {
    // This would require a healing formula and a more complex targeting system.
    return {
      log: `${caster.name} is reborn in phoenix fire.`,
    };
  },
});
