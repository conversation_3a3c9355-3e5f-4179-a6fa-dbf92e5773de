name: Format Pull Request Description

on:
  pull_request:
    types: [opened]

jobs:
  format-pr-description:
    runs-on: ubuntu-latest
    permissions:
      pull-requests: write
      contents: read
      models: read

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Summarize PR
        id: summarize
        uses: actions/ai-inference@v1
        with:
          enable-github-mcp: true
          token: ${{ secrets.USER_PAT }}
          prompt: |
            Summarize the following pull request in bullet points to match the template. |
            Pull Request: ${{ github.event.pull_request.title }} ${{ github.event.pull_request.url }}

            <!--- Please provide a general summary of your changes in the title above -->

            ## Pull Request type

            <!-- Please try to limit your pull request to one type; submit multiple pull requests if needed. -->

            Please check the type of change your PR introduces:

            - [ ] Bugfix
            - [ ] Feature
            - [ ] Code style update (formatting, renaming)
            - [ ] Refactoring (no functional changes, no API changes)
            - [ ] Build-related changes
            - [ ] Documentation content changes
            - [ ] Other (please describe):

            ## What is the current behavior?

            <!-- Please describe the current behavior that you are modifying, or link to a relevant issue. -->

            Issue Number: N/A

            ## What is the new behavior?

            <!-- Please describe the behavior or changes that are being added by this PR. -->

            ## Does this introduce a breaking change?

            - [ ] Yes
            - [ ] No

            <!-- If this does introduce a breaking change, please describe the impact and migration path for existing applications below. -->

            ## Other information

            <!-- Any other information that is important to this PR, such as screenshots of how the component looks before and after the change. -->

      - name: Update PR description
        run: |
          gh pr edit $PR_URL --body "$UPDATED_BODY"
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          PR_URL: ${{ github.event.pull_request.html_url }}
          UPDATED_BODY: ${{ steps.summarize.outputs.response }}  