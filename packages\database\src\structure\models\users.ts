import { eq } from 'drizzle-orm';
import { createSelectSchema } from 'drizzle-zod';
import { BaseModel } from '../model';

export class Users extends BaseModel {
  public schema = createSelectSchema(this.schemas.users);

  public async get(id: string) {
    return await this.client.instance.query.users.findFirst({
      where: {
        id,
      },
    });
  }

  public async create(id: string) {
    const [user] = await this.client.instance.insert(this.schemas.users).values({ id }).returning().execute();

    // Initialize user currencies including rifts
    await this.client.currencies.initializeUserCurrencies(id);

    return user!;
  }

  /**
   * Initialize a new user
   */
  // private async intialize(id: string): Promise<void> {
  //   await this.client.
  // }

  public async exists(id: string): Promise<boolean> {
    const user = await this.get(id);
    return user !== undefined;
  }

  public async getOrCreate(id: string) {
    const existingUser = await this.get(id);
    if (existingUser) {
      return { user: existingUser, isNew: false };
    }

    const newUser = await this.create(id);
    return { user: newUser, isNew: true };
  }

  public async updateDailyStreak(id: string, newStreak: number, lastClaim: number) {
    await this.client.instance
      .update(this.schemas.users)
      .set({
        dailyStreak: newStreak,
        lastDailyClaim: lastClaim,
      })
      .where(eq(this.schemas.users.id, id));
  }

  public async resetDailyStreak(id: string) {
    await this.client.instance
      .update(this.schemas.users)
      .set({
        dailyStreak: 0,
        lastDailyClaim: 0,
      })
      .where(eq(this.schemas.users.id, id));
  }

  public async resetAllDailyStreaks() {
    await this.client.instance.update(this.schemas.users).set({
      dailyStreak: 0,
      lastDailyClaim: 0,
    });
  }

  public async updateVotingStreak(id: string, newStreak: number, lastVote: number) {
    await this.client.instance
      .update(this.schemas.users)
      .set({
        votingStreak: newStreak,
        lastVote,
      })
      .where(eq(this.schemas.users.id, id));
  }

  public async resetVotingStreak(id: string) {
    await this.client.instance
      .update(this.schemas.users)
      .set({
        votingStreak: 0,
        lastVote: 0,
      })
      .where(eq(this.schemas.users.id, id));
  }

  public async resetAllVotingStreaks() {
    await this.client.instance.update(this.schemas.users).set({
      votingStreak: 0,
      lastVote: 0,
    });
  }

  /**
   * Process voting reward for a user
   * This method is exported for use by external voting webhook
   */
  public async processVotingReward(userId: string): Promise<{
    success: boolean;
    error?: string;
    reward?: {
      baseReward: { rifts: number; shards: number };
      bonusPercentage: number;
      totalReward: { rifts: number; shards: number };
      newStreak: number;
      streakDescription: string;
    };
  }> {
    try {
      const { calculateVotingReward, calculateNewVotingStreak, canVote } = await import('@megami/config/lib/voting');

      const user = await this.get(userId);
      if (!user) {
        return { success: false, error: 'User not found' };
      }

      // Check if user can vote
      const voteCheck = canVote(user.lastVote);
      if (!voteCheck.canVote) {
        return { success: false, error: voteCheck.reason };
      }

      const now = Date.now();

      // Calculate new streak
      const newStreak = calculateNewVotingStreak(user.lastVote, user.votingStreak);

      // Calculate rewards
      const rewardData = calculateVotingReward(newStreak);

      // Apply rewards
      const promises: Promise<unknown>[] = [];

      // Add rifts
      promises.push(this.addRifts(userId, rewardData.totalReward.rifts));

      // Add shards
      const shardsCurrency = await this.client.currencies.getCurrencyByName('Shards');
      if (shardsCurrency) {
        promises.push(
          this.client.currencies.addToUserBalance(userId, shardsCurrency.id, rewardData.totalReward.shards)
        );
      }

      // Update voting streak
      promises.push(this.updateVotingStreak(userId, newStreak, now));

      await Promise.all(promises);

      return {
        success: true,
        reward: {
          baseReward: rewardData.baseReward,
          bonusPercentage: rewardData.bonusPercentage,
          totalReward: rewardData.totalReward,
          newStreak,
          streakDescription: rewardData.streakDescription,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  public async getRifts(id: string): Promise<number> {
    const user = await this.get(id);
    if (!user) throw new Error('User not found');

    // Get rifts currency from balances table
    const riftsCurrency = await this.client.currencies.getCurrencyByName('Rifts');
    if (!riftsCurrency) throw new Error('Rifts currency not found');

    const balance = await this.client.currencies.getUserCurrency(id, riftsCurrency.id);
    return balance?.amount ?? 0;
  }

  public async addRifts(id: string, rifts: number): Promise<number> {
    const user = await this.get(id);
    if (!user) throw new Error('User not found');

    // Calculate effects for rift gains
    const effects = await this.client.effects.calculateRiftEffects(id);
    const finalAmount = Math.floor(rifts * effects.multiplier + effects.bonus);

    // Get rifts currency from balances table
    const riftsCurrency = await this.client.currencies.getCurrencyByName('Rifts');
    if (!riftsCurrency) throw new Error('Rifts currency not found');

    const updatedBalance = await this.client.currencies.addToUserBalance(id, riftsCurrency.id, finalAmount);
    return updatedBalance.amount;
  }

  /**
   * Add experience with effects applied
   */
  public async addExperience(id: string, experience: number) {
    const user = await this.get(id);
    if (!user) throw new Error('User not found');

    // Calculate effects for experience gains
    const effects = await this.client.effects.calculateExpEffects(id);
    const finalAmount = Math.floor(experience * effects.multiplier + effects.bonus);

    const newExperience = user.experience + finalAmount;
    const newLevel = this.calculateLevel(newExperience);

    const [updatedUser] = await this.client.instance
      .update(this.schemas.users)
      .set({
        experience: newExperience,
        level: newLevel,
        updatedAt: new Date(),
      })
      .where(eq(this.schemas.users.id, id))
      .returning();

    return updatedUser!;
  }

  /**
   * Get all active effects for a user
   */
  public async getEffects(id: string) {
    return await this.client.effects.getUserEffects(id);
  }

  private calculateLevel(experience: number): number {
    return Math.floor(experience / 4000) + 1;
  }
}
