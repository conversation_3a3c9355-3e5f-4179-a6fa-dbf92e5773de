'use client';

import { DotLottieReact } from '@lottiefiles/dotlottie-react';
import AnimatedGradientBackground from '@megami/ui/components/registry/animated-gradient-background';
import { motion } from 'motion/react';

export default function NotFound() {
  return (
    <div className="relative h-screen w-full overflow-hidden">
      <AnimatedGradientBackground />
      <div className="relative z-10 flex h-full flex-col items-center justify-start px-4 pt-32 text-center">
        <motion.div delay={0.4} duration={0.9}>
          <DotLottieReact
            autoplay
            loop
            src="https://lottie.host/8cf4ba71-e5fb-44f3-8134-178c4d389417/0CCsdcgNIP.json"
          />
        </motion.div>
        <p className="mt-4 max-w-lg text-gray-300 text-lg md:text-xl">The Page was not found.</p>
      </div>
    </div>
  );
}
