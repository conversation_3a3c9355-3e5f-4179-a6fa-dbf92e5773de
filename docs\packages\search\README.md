# `packages/search`

This package provides a search client for the "Megami" project, using `minisearch` to create in-memory search indexes.

## Installation

To install dependencies:

```bash
bun install
```

## Usage

To run:

```bash
bun run index.ts
```

---

## Architecture

The `search` package provides a `MegamiSearch` client that contains separate search indexes for characters, series, and skins.

### `MegamiSearch` Client

The `MegamiSearch` client is the main entry point for interacting with the search indexes.

**Properties:**

-   `characters`: An instance of `MegamiCharactersIndex`.
-   `series`: An instance of `MegamiSeriesIndex`.
-   `skins`: An instance of `MegamiSkinsIndex`.

**Example:**

```typescript
import { MegamiSearch } from './structure/client';

const search = new MegamiSearch();
```

### Search Indexes

Each search index (e.g., `MegamiCharactersIndex`) provides the following methods:

-   **`add(...documents)`**: Adds one or more documents to the index.
-   **`remove(...documents)`**: Removes one or more documents from the index.
-   **`search(query, options)`**: Searches the index for a given query.

**Example:**

```typescript
// Add a character to the index
await search.characters.add({
  id: '1',
  name: 'Gandalf',
  description: 'A powerful wizard',
  element: 'Arcane',
  skill: 'Fireball',
});

// Search for characters
const results = search.characters.search('wizard');

console.log(results);
```
