import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Gravity Flux',
  name: 'Gravity Flux',
  element: 'Arcane',
  manaCost: 30,
  cooldown: 3,
  description: 'Reverses gravity for the target, stunning them for one turn.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Stun',
        name: 'Gravity Flux',
        duration: 1,
        sourceId: caster.id,
      },
      log: `${caster.name} reverses gravity on ${target.name}, leaving them unable to act.`,
    };
  },
});
