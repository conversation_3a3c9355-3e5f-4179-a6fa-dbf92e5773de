import { getObject, translate } from '@megami/locale';
import { <PERSON><PERSON><PERSON>s, SeparatorBuilder, SeparatorSpacingSize, TextDisplayBuilder } from 'discord.js';
import { defineSubCommand } from '../../../../../handlers/command';
import { MegamiContainer } from '../../../../../helpers/containers';
import { defer } from '../../../../../helpers/defer';

export default defineSubCommand((builder) => ({
  builder: builder
    .setName(translate('commands.management.modules.items.modules.delete.name'))
    .setNameLocalizations(getObject('commands.management.modules.items.modules.delete.name'))
    .setDescription(translate('commands.management.modules.items.modules.delete.description'))
    .setDescriptionLocalizations(getObject('commands.management.modules.items.modules.delete.description'))
    .addStringOption((option) =>
      option
        .setName(translate('commands.management.modules.items.modules.delete.options.item.name'))
        .setNameLocalizations(getObject('commands.management.modules.items.modules.delete.options.item.name'))
        .setDescription(translate('commands.management.modules.items.modules.delete.options.item.description'))
        .setDescriptionLocalizations(
          getObject('commands.management.modules.items.modules.delete.options.item.description')
        )
        .setRequired(true)
        .setAutocomplete(true)
    )
    .addBooleanOption((option) =>
      option
        .setName(translate('commands.management.modules.items.modules.delete.options.confirm.name'))
        .setNameLocalizations(getObject('commands.management.modules.items.modules.delete.options.confirm.name'))
        .setDescription(translate('commands.management.modules.items.modules.delete.options.confirm.description'))
        .setDescriptionLocalizations(
          getObject('commands.management.modules.items.modules.delete.options.confirm.description')
        )
        .setRequired(false)
    ),
  config: {},
  autocomplete: async (interaction) => {
    const focused = interaction.options.getFocused(true);

    if (focused.name === translate('commands.management.modules.items.modules.delete.options.item.name')) {
      try {
        const items = await interaction.client.database.items.search({
          name: focused.value,
          isActive: true,
          limit: 25,
        });

        await interaction.respond(
          items.map((item) => ({
            name: `${item.icon || '📦'} ${item.name} (${item.rarity})`,
            value: item.name,
          }))
        );
      } catch (_error) {
        await interaction.respond([]);
      }
    }
  },
  execute: async (interaction) => {
    await defer(interaction, false);

    const itemName = interaction.options.getString('item', true);
    const hardDelete = interaction.options.getBoolean('hard-delete') ?? false;

    try {
      // Find the item
      const item = await interaction.client.database.items.getByName(itemName);
      if (!item) {
        const container = new MegamiContainer([
          new TextDisplayBuilder().setContent('❌ **Item Not Found**'),
          new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
          new TextDisplayBuilder().setContent(`No item found with the name "${itemName}".`),
          new TextDisplayBuilder().setContent('Please check the spelling and try again.'),
        ]);

        return await interaction.editReply({
          components: [container],
          flags: MessageFlags.IsComponentsV2,
        });
      }

      // Check if item is already inactive (soft deleted)
      if (!(item.isActive || hardDelete)) {
        const container = new MegamiContainer([
          new TextDisplayBuilder().setContent('⚠️ **Item Already Deleted**'),
          new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
          new TextDisplayBuilder().setContent(`The item "${item.name}" is already deleted.`),
          new TextDisplayBuilder().setContent('Use `hard-delete: true` to permanently remove it.'),
        ]);

        return await interaction.editReply({
          components: [container],
          flags: MessageFlags.IsComponentsV2,
        });
      }

      // Perform deletion
      let success: boolean;
      if (hardDelete) {
        success = await interaction.client.database.items.hardDelete(item.id);
      } else {
        success = await interaction.client.database.items.delete(item.id);
      }

      if (!success) {
        const container = new MegamiContainer([
          new TextDisplayBuilder().setContent('❌ **Deletion Failed**'),
          new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
          new TextDisplayBuilder().setContent('Failed to delete the item.'),
          new TextDisplayBuilder().setContent('Please try again or contact an administrator.'),
        ]);

        return await interaction.editReply({
          components: [container],
          flags: MessageFlags.IsComponentsV2,
        });
      }

      const typeEmoji = getTypeEmoji(item.type);
      const rarityEmoji = getRarityEmoji(item.rarity);

      const container = new MegamiContainer([
        new TextDisplayBuilder().setContent(hardDelete ? '🗑️ **Item Permanently Deleted**' : '✅ **Item Deleted**'),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent(`${item.icon || typeEmoji} **${item.name}**`),
        new TextDisplayBuilder().setContent(`${rarityEmoji} ${item.rarity} ${typeEmoji} ${item.type}`),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent(
          hardDelete
            ? 'The item has been permanently removed from the database.'
            : 'The item has been deactivated and can no longer be obtained.'
        ),
        new TextDisplayBuilder().setContent(
          hardDelete ? '⚠️ This action cannot be undone.' : 'Existing user holdings are preserved.'
        ),
      ]);

      await interaction.editReply({
        components: [container],
        flags: MessageFlags.IsComponentsV2,
      });
    } catch (error) {
      const container = new MegamiContainer([
        new TextDisplayBuilder().setContent('❌ **Error Deleting Item**'),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent('An unexpected error occurred while deleting the item.'),
        new TextDisplayBuilder().setContent('Please try again or contact an administrator.'),
      ]);

      await interaction.editReply({
        components: [container],
        flags: MessageFlags.IsComponentsV2,
      });

      // Log the error for debugging
      interaction.client.logger.error('Error deleting item:', error);
    }
  },
}));

function getTypeEmoji(type: string): string {
  const emojis: Record<string, string> = {
    EQUIPMENT: '⚔️',
    CONSUMABLE: '🧪',
    MATERIAL: '🔨',
    COLLECTIBLE: '💎',
    CURRENCY: '💰',
    GIFT: '🎁',
    KEY: '🗝️',
    MISC: '📦',
  };
  return emojis[type] || '📦';
}

function getRarityEmoji(rarity: string): string {
  const emojis: Record<string, string> = {
    COMMON: '⚪',
    UNCOMMON: '🟢',
    RARE: '🔵',
    EPIC: '🟣',
    LEGENDARY: '🟡',
    MYTHIC: '🔴',
  };
  return emojis[rarity] || '⚪';
}
