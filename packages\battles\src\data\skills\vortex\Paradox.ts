import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Paradox',
  name: 'Paradox',
  element: 'Vortex',
  manaCost: 40,
  cooldown: 6,
  description: 'The caster creates a paradox, causing all enemies to be unable to act for a short time.',
  execute: (caster, target, formulas) => {
    // This would require a status effect system to handle the stun.
    return {
      log: `${caster.name} creates a paradox, stunning all enemies.`,
    };
  },
});
