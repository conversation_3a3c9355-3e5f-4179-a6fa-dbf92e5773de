# `packages/ai`

This package provides a client for interacting with various AI models, with a specific focus on character analysis for the "Megami" project.

## Installation

To install dependencies:

```bash
bun install
```

## Usage

To run:

```bash
bun run index.ts
```

---

## Architecture

The `ai` package is designed to provide a unified interface for interacting with different AI providers like Google Gemini and Groq. It is primarily used for character analysis within the "Megami" project.

### `MegamiAI` Client

The main class in this package is `MegamiAI`. It is used to configure and interact with the AI providers. It takes a configuration object in its constructor that specifies the API keys for the different providers.

**Properties:**

-   `options`: The configuration options for the AI client.
-   `providers`: An object containing the configured AI providers (e.g., `google`, `groq`).
-   `character`: An instance of `MegamiCharacterAI` for character analysis.

**Example:**

```typescript
import { MegamiAI } from './structure/client';

const ai = new MegamiAI({
  gemini: { API_KEY: '...' },
  groq: { API_KEY: '...' },
});
```

### `MegamiCharacterAI`

This class provides functionality for character analysis. It can take a character's name and description and use an AI model to determine the character's elemental affinity.

**Methods:**

-   **`element(input: CharacterAnalysisInput)`**: Analyzes a character and returns an `ElementAnalysisResult`.
    -   `input`: An object containing the character's `name` and `description`.
    -   **Returns:** A promise that resolves to an object containing the analysis result, including the assigned element, confidence score, and reasoning.

The available elements are:
- Arcane
- Vortex
- Sirius
- Selene

**Example:**

```typescript
import { MegamiAI } from './structure/client';

const ai = new MegamiAI({
  gemini: { API_KEY: '...' },
  groq: { API_KEY: '...' },
});

const analysis = await ai.character.element({
  name: 'Gandalf',
  description: 'A powerful wizard who uses magic and wisdom to guide the forces of good.',
});

console.log(analysis);
```
