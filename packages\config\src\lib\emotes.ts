import { formatEmoji } from 'discord.js';

export const emotes = {
  currencies: {
    crowns: formatEmoji('1398933457308225587', true),
    embers: formatEmoji('1398669795779678218', true),
    shards: formatEmoji('1398933730931773541', true),
    rifts: formatEmoji('1398879354699776050', true),
  },
  elements: {
    arcane: formatEmoji('1398931137920765952', true),
    selene: formatEmoji('1398932079319978157', true),
    sirius: formatEmoji('1398932502575579168', true),
    vortex: formatEmoji('1398933008303788123', true),
  },
  misc: {
    confirmations: {
      confirm: formatEmoji('1398644808775499816', true),
      cancel: formatEmoji('1398644583176339517', true),
    },
    bars: {
      filled: {
        start: formatEmoji('1396505234267705406', false),
        middle: formatEmoji('1396505552124641334', false),
        end: formatEmoji('1396505560127508490', false),
      },
      empty: {
        start: formatEmoji('1396490733787021395', false),
        middle: formatEmoji('1396490682646138921', false),
        end: formatEmoji('1396490762530852945', false),
      },
    },
  },
  items: {
    type: {
      consumables: formatEmoji('1399406816240336927', true),
    },
  },
} as const;

type NestedKeyOf<T> = T extends object
  ? {
      [K in keyof T]: K extends string | number ? `${K}` | `${K}.${NestedKeyOf<T[K]>}` : never;
    }[keyof T]
  : '';

export function getIDFromEmote(key: NestedKeyOf<typeof emotes>): string {
  const parts = key.split('.');

  // @ts-expect-error - ignore this
  let value: keyof typeof emotes = emotes;

  for (const part of parts) {
    if (value && typeof value === 'object' && part in value) {
      value = value[part];
    } else {
      throw new Error(`Invalid emote key: ${key}`);
    }
  }

  if (typeof value !== 'string') {
    throw new Error(`Emote value for key "${key}" is not a string`);
  }

  const split = value.split(':');
  if (split.length < 3) {
    throw new Error(`Emote string for key "${key}" is not in the expected format`);
  }

  return split[2]!.split('>')[0] as string;
}
