import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Dark Side of the Moon',
  name: 'Dark Side of the Moon',
  element: 'Se<PERSON>',
  manaCost: 40,
  cooldown: 6,
  description: 'The caster embraces the dark side of the moon, increasing their damage but also taking damage over time.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Dark Side of the Moon Effect',
        name: 'Dark Side of the Moon',
        duration: 3,
        sourceId: caster.id,
      },
      log: `${caster.name} embraces the dark side of the moon.`,
    };
  },
});
