import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Arcane Binding',
  name: 'Arcane Binding',
  element: 'Arcane',
  manaCost: 20,
  cooldown: 4,
  description: 'Binds the target with arcane energy, damaging and rooting them.',
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const finalDamage = Math.round(damage * 1.2);
    // This would also require a status effect system to handle the root.
    return {
      damage: finalDamage,
      isCritical,
      log: `${caster.name} binds ${target.name} with Arcane Binding for ${finalDamage} damage${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
