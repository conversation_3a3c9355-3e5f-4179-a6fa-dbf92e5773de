import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Scorched Earth',
  name: 'Scorched Earth',
  element: 'Sirius',
  manaCost: 50,
  cooldown: 7,
  description: 'The caster scorches the earth, creating a field of fire that damages and burns any enemy that enters it.',
  execute: (caster, target, formulas) => {
    // This would require a system for creating and managing persistent area effects.
    return {
      log: `${caster.name} scorches the earth.`,
    };
  },
});
