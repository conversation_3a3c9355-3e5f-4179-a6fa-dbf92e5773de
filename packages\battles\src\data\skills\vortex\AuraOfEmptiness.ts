import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Aura of Emptiness',
  name: 'Aura of Emptiness',
  element: 'Vortex',
  manaCost: 30,
  cooldown: 5,
  description: 'The caster is surrounded by an aura of emptiness, silencing any enemy that comes near them.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Silence',
        name: 'Aura of Emptiness',
        duration: 2,
        sourceId: caster.id,
      },
      log: `${caster.name} is surrounded by an Aura of Emptiness.`,
    };
  },
});
