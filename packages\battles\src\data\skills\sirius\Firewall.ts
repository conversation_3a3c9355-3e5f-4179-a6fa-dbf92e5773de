import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Firewall',
  name: 'Firewall',
  element: 'Sirius',
  manaCost: 30,
  cooldown: 5,
  description: 'Creates a wall of fire that damages any enemy that passes through it.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Burn',
        name: 'Firewall',
        duration: 3,
        sourceId: caster.id,
      },
      log: `${caster.name} creates a Firewall.`,
    };
  },
});
