import { getIDFromEmote } from '@megami/config/lib/emotes';
import {
  ActionRowBuilder,
  ButtonBuilder,
  type ButtonInteraction,
  ButtonStyle,
  type ChatInputCommandInteraction,
  ComponentType,
  type InteractionCollector,
  type InteractionEditReplyOptions,
  type Message,
  MessageFlags,
  TextDisplayBuilder,
} from 'discord.js';
import { MegamiContainer } from '../../helpers/containers';
import { logger } from '../../structure/logger';

export interface ConfirmationOptions {
  interaction: ChatInputCommandInteraction;
  content: string[];
  confirmText?: string;
  cancelText?: string;
  confirmEmoji?: string;
  cancelEmoji?: string;
  timeout?: number;
  editReply?: boolean;
  userOnly?: boolean;
}

export interface ConfirmationResult {
  confirmed: boolean;
  interaction: ButtonInteraction;
  timedOut: boolean;
}

/**
 * Creates a confirmation dialog with Yes/No buttons
 * @param options Configuration options for the confirmation
 * @returns Promise that resolves to the user's choice
 */
export async function createConfirmation(options: ConfirmationOptions): Promise<ConfirmationResult | null> {
  const {
    interaction,
    content,
    confirmText = 'Confirm',
    cancelText = 'Cancel',
    timeout = 30_000,
    editReply = true,
    userOnly = true,
  } = options;

  // Create unique custom IDs to avoid conflicts
  const confirmId = `confirm_${interaction.id}_${Date.now()}`;
  const cancelId = `cancel_${interaction.id}_${Date.now()}`;

  // Create buttons
  const confirmButton = new ButtonBuilder()
    .setCustomId(confirmId)
    .setLabel(confirmText)
    .setStyle(ButtonStyle.Secondary)
    .setEmoji({ id: getIDFromEmote('misc.confirmations.confirm') });

  const cancelButton = new ButtonBuilder()
    .setCustomId(cancelId)
    .setLabel(cancelText)
    .setStyle(ButtonStyle.Secondary)
    .setEmoji({ id: getIDFromEmote('misc.confirmations.cancel') });

  // Create action row
  const actionRow = new ActionRowBuilder<ButtonBuilder>().addComponents(confirmButton, cancelButton);

  const container = new MegamiContainer([
    ...content.map((text) => new TextDisplayBuilder().setContent(text)),
    actionRow,
  ]);

  const messageOptions = {
    components: [container],
    flags: MessageFlags.IsComponentsV2,
  } as InteractionEditReplyOptions;

  // Send or edit the message
  let message: Message;
  try {
    if (editReply) {
      message = await interaction.editReply(messageOptions);
    } else {
      // @ts-expect-error - ignore this shit
      message = (await interaction.reply({ ...messageOptions, withResponse: true })) as Message;
    }
  } catch (error) {
    logger.error('Failed to send confirmation message:', error);
    return null;
  }

  // Create collector
  const collector: InteractionCollector<ButtonInteraction> = message.createMessageComponentCollector({
    componentType: ComponentType.Button,
    time: timeout,
    filter: (buttonInteraction) => {
      // Check if it's one of our buttons
      if (![confirmId, cancelId].includes(buttonInteraction.customId)) {
        return false;
      }

      // If userOnly is true, only allow the original user
      if (userOnly && buttonInteraction.user.id !== interaction.user.id) {
        // Respond with ephemeral message to other users
        buttonInteraction
          .reply({
            content: 'This confirmation is not for you.',
            ephemeral: true,
          })
          .catch(() => {
            // Ignore errors if interaction has already been acknowledged
          });
        return false;
      }

      return true;
    },
  });

  return new Promise((resolve) => {
    let resolved = false;

    collector.on('collect', async (buttonInteraction: ButtonInteraction) => {
      if (resolved) return;
      resolved = true;

      const confirmed = buttonInteraction.customId === confirmId;

      // Disable all buttons
      const disabledRow = new ActionRowBuilder<ButtonBuilder>().addComponents(
        confirmButton.setDisabled(true),
        cancelButton.setDisabled(true)
      );

      try {
        // Update the message to disable buttons
        await buttonInteraction.update({
          components: [disabledRow],
        });
      } catch (error) {
        logger.error('Failed to update confirmation message:', error);
      }

      // Stop the collector
      collector.stop('collected');

      // Resolve with the result
      resolve({
        confirmed,
        interaction: buttonInteraction,
        timedOut: false,
      });
    });

    collector.on('end', async (_collected, reason) => {
      if (resolved) return;
      resolved = true;

      if (reason === 'time') {
        // Disable all buttons on timeout
        const disabledRow = new ActionRowBuilder<ButtonBuilder>().addComponents(
          confirmButton.setDisabled(true),
          cancelButton.setDisabled(true)
        );

        try {
          await interaction.editReply({
            components: [disabledRow],
          });
        } catch (error) {
          logger.error('Failed to update timed out confirmation message:', error);
        }

        resolve({
          confirmed: false,
          // @ts-expect-error - ignore
          interaction: null as undefined, // No interaction on timeout
          timedOut: true,
        });
      } else {
        resolve(null);
      }
    });
  });
}

/**
 * Simple confirmation helper that returns just a boolean
 * @param options Configuration options for the confirmation
 * @returns Promise that resolves to true if confirmed, false if cancelled or timed out
 */
export async function confirm(options: ConfirmationOptions): Promise<boolean> {
  const result = await createConfirmation(options);
  return result?.confirmed ?? false;
}
