import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Alternate Reality',
  name: 'Alternate Reality',
  element: 'Vortex',
  manaCost: 60,
  cooldown: 8,
  description: 'The caster transports all enemies to an alternate reality where they are weakened and take damage over time.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Alternate Reality Effect',
        name: 'Alternate Reality',
        duration: 3,
        sourceId: caster.id,
      },
      log: `${caster.name} transports all enemies to an alternate reality.`,
    };
  },
});
