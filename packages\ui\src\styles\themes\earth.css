.earth {
  --background: oklch(0.97 0.01 81.76);
  --foreground: oklch(0.3 0.04 29.2);
  --card: oklch(0.97 0.01 81.76);
  --card-foreground: oklch(0.3 0.04 29.2);
  --popover: oklch(0.97 0.01 81.76);
  --popover-foreground: oklch(0.3 0.04 29.2);
  --primary: oklch(0.52 0.13 144.33);
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.96 0.02 147.54);
  --secondary-foreground: oklch(0.43 0.12 144.33);
  --muted: oklch(0.94 0.01 72.65);
  --muted-foreground: oklch(0.45 0.05 38.69);
  --accent: oklch(0.9 0.05 146.01);
  --accent-foreground: oklch(0.43 0.12 144.33);
  --destructive: oklch(0.54 0.19 26.9);
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.88 0.02 77.29);
  --input: oklch(0.88 0.02 77.29);
  --ring: oklch(0.52 0.13 144.33);
  --chart-1: oklch(0.67 0.16 144.06);
  --chart-2: oklch(0.58 0.14 144.14);
  --chart-3: oklch(0.52 0.13 144.33);
  --chart-4: oklch(0.43 0.12 144.33);
  --chart-5: oklch(0.22 0.05 145.19);
  --sidebar: oklch(0.94 0.01 72.65);
  --sidebar-foreground: oklch(0.3 0.04 29.2);
  --sidebar-primary: oklch(0.52 0.13 144.33);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.9 0.05 146.01);
  --sidebar-accent-foreground: oklch(0.43 0.12 144.33);
  --sidebar-border: oklch(0.88 0.02 77.29);
  --sidebar-ring: oklch(0.52 0.13 144.33);

  --font-sans: Montserrat, sans-serif;
  --font-serif: Merriweather, serif;
  --font-mono: Source Code Pro, monospace;

  --radius: 0.5rem;

  --shadow-2xs: 0 1px 3px 0px oklch(0 0 0 / 0.05);
  --shadow-xs: 0 1px 3px 0px oklch(0 0 0 / 0.05);
  --shadow-sm:
    0 1px 3px 0px oklch(0 0 0 / 0.1), 0 1px 2px -1px oklch(0 0 0 / 0.1);
  --shadow: 0 1px 3px 0px oklch(0 0 0 / 0.1), 0 1px 2px -1px oklch(0 0 0 / 0.1);
  --shadow-md:
    0 1px 3px 0px oklch(0 0 0 / 0.1), 0 2px 4px -1px oklch(0 0 0 / 0.1);
  --shadow-lg:
    0 1px 3px 0px oklch(0 0 0 / 0.1), 0 4px 6px -1px oklch(0 0 0 / 0.1);
  --shadow-xl:
    0 1px 3px 0px oklch(0 0 0 / 0.1), 0 8px 10px -1px oklch(0 0 0 / 0.1);
  --shadow-2xl: 0 1px 3px 0px oklch(0 0 0 / 0.25);
}

.earth-dark {
  --background: oklch(0.27 0.03 150.18);
  --foreground: oklch(0.94 0.01 72.65);
  --card: oklch(0.33 0.03 146.53);
  --card-foreground: oklch(0.94 0.01 72.65);
  --popover: oklch(0.33 0.03 146.53);
  --popover-foreground: oklch(0.94 0.01 72.65);
  --primary: oklch(0.67 0.16 144.06);
  --primary-foreground: oklch(0.22 0.05 145.19);
  --secondary: oklch(0.39 0.03 143.09);
  --secondary-foreground: oklch(0.9 0.02 142.94);
  --muted: oklch(0.33 0.03 146.53);
  --muted-foreground: oklch(0.86 0.02 77.29);
  --accent: oklch(0.58 0.14 144.14);
  --accent-foreground: oklch(0.94 0.01 72.65);
  --destructive: oklch(0.54 0.19 26.9);
  --destructive-foreground: oklch(0.94 0.01 72.65);
  --border: oklch(0.39 0.03 143.09);
  --input: oklch(0.39 0.03 143.09);
  --ring: oklch(0.67 0.16 144.06);
  --chart-1: oklch(0.77 0.12 145.23);
  --chart-2: oklch(0.72 0.14 144.92);
  --chart-3: oklch(0.67 0.16 144.06);
  --chart-4: oklch(0.63 0.15 144.32);
  --chart-5: oklch(0.58 0.14 144.14);
  --sidebar: oklch(0.27 0.03 150.18);
  --sidebar-foreground: oklch(0.94 0.01 72.65);
  --sidebar-primary: oklch(0.67 0.16 144.06);
  --sidebar-primary-foreground: oklch(0.22 0.05 145.19);
  --sidebar-accent: oklch(0.58 0.14 144.14);
  --sidebar-accent-foreground: oklch(0.94 0.01 72.65);
  --sidebar-border: oklch(0.39 0.03 143.09);
  --sidebar-ring: oklch(0.67 0.16 144.06);

  --shadow-2xs: 0 1px 3px 0px oklch(0 0 0 / 0.05);
  --shadow-xs: 0 1px 3px 0px oklch(0 0 0 / 0.05);
  --shadow-sm:
    0 1px 3px 0px oklch(0 0 0 / 0.1), 0 1px 2px -1px oklch(0 0 0 / 0.1);
  --shadow: 0 1px 3px 0px oklch(0 0 0 / 0.1), 0 1px 2px -1px oklch(0 0 0 / 0.1);
  --shadow-md:
    0 1px 3px 0px oklch(0 0 0 / 0.1), 0 2px 4px -1px oklch(0 0 0 / 0.1);
  --shadow-lg:
    0 1px 3px 0px oklch(0 0 0 / 0.1), 0 4px 6px -1px oklch(0 0 0 / 0.1);
  --shadow-xl:
    0 1px 3px 0px oklch(0 0 0 / 0.1), 0 8px 10px -1px oklch(0 0 0 / 0.1);
  --shadow-2xl: 0 1px 3px 0px oklch(0 0 0 / 0.25);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
}
