import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Orb of Deception',
  name: 'Orb of Deception',
  element: 'Selene',
  manaCost: 25,
  cooldown: 4,
  description: 'Throws an orb of deception that damages and charms the first enemy it hits.',
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const finalDamage = Math.round(damage * 1.1);
    // This would also require a status effect system to handle the charm.
    return {
      damage: finalDamage,
      isCritical,
      log: `${caster.name} throws an Orb of Deception at ${target.name}, dealing ${finalDamage} damage${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
