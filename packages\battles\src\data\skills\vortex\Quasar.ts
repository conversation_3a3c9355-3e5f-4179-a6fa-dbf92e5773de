import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Quasar',
  name: 'Quasar',
  element: 'Vortex',
  manaCost: 70,
  cooldown: 9,
  description: 'The caster summons a quasar that unleashes a devastating beam of energy, dealing massive damage to all enemies in a line.',
  execute: (caster, target, formulas) => {
    // This would require a more complex targeting system.
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const finalDamage = Math.round(damage * 3.5);
    return {
      damage: finalDamage,
      isCritical,
      log: `${caster.name} summons a Quasar, unleashing a devastating beam of energy for ${finalDamage} damage${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
