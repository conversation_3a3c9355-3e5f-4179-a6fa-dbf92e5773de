import type { skins, InferSelectModel } from '@megami/database';
import MiniSearch, { type Query, type SearchOptions } from 'minisearch';

type Skin = InferSelectModel<typeof skins>;

export class MegamiSkinsIndex {
  public instance: MiniSearch<Skin>;

  constructor() {
    this.instance = new MiniSearch<Skin>({
      fields: ['name', 'rarity'],
      storeFields: ['name', 'rarity', 'approved', 'url', 'authorId', 'characterId'],
    });
  }

  public async add(...skin: Skin[]) {
    return await this.instance.addAllAsync(skin);
  }

  public remove(...skin: Skin[]) {
    return this.instance.removeAll(skin);
  }

  public search(query: Query, options: SearchOptions = {}) {
    return this.instance.search(query, options);
  }
}
