import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Black Hole',
  name: 'Black Hole',
  element: 'Vortex',
  manaCost: 35,
  cooldown: 5,
  description: 'Create a devastating black hole that deals massive damage and applies a crushing DoT effect.',
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const blackHoleDamage = Math.round(damage * 2.0);

    return {
      damage: blackHoleDamage,
      isCritical,
      appliedEffect: {
        id: 'crushed',
        name: 'Crushed',
        duration: 3,
        potency: Math.round(caster.stats.attack * 0.4),
        sourceId: caster.id,
      },
      log: `${caster.name} creates a Black Hole that devastates ${target.name} for ${blackHoleDamage} damage${isCritical ? ' (CRIT!)' : ''} and crushes them!`,
    };
  },
});
