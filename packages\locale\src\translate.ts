/* biome-ignore-all lint/suspicious/noConsole: Shut UP */
import fs from 'node:fs';
import path from 'node:path';
import { wait } from '@megami/utils/lib/wait';
import { translate as ai } from './ai';
import { DISCORD_LANGUAGE_CODES } from './discord';

const read = fs.promises.readFile;
const write = fs.promises.writeFile;
const mkdir = fs.promises.mkdir;

const LOCALES_DIR = path.join(__dirname, 'locales');

interface TranslationDiff {
  filePath: string;
  missingKeys: string[];
  keysToTranslate: Record<string, object>;
}

async function getJsonFiles(langDir: string): Promise<string[]> {
  const files: string[] = [];

  try {
    const entries = fs.readdirSync(langDir, { withFileTypes: true });

    for await (const entry of entries) {
      if (entry.isFile() && entry.name.endsWith('.json')) {
        files.push(path.join(langDir, entry.name));
      } else if (entry.isDirectory()) {
        const subFiles = await getJsonFiles(path.join(langDir, entry.name));
        files.push(...subFiles);
      }
    }
  } catch {
    // Directory doesn't exist or can't be read
  }

  return files;
}

function getNestedKeys(obj: object, prefix = ''): string[] {
  const keys: string[] = [];

  for (const [key, value] of Object.entries(obj)) {
    const fullKey = prefix ? `${prefix}.${key}` : key;

    if (value && typeof value === 'object' && !Array.isArray(value)) {
      keys.push(...getNestedKeys(value, fullKey));
    } else {
      keys.push(fullKey);
    }
  }

  return keys;
}

function getValueByPath(obj: object, keypath: string) {
  return keypath.split('.').reduce((current, key) => current?.[key as keyof typeof current], obj);
}

function setValueByPath(obj: object, keypath: string, value: unknown): void {
  const keys = keypath.split('.');
  const lastKey = keys.pop()!;
  const target = keys.reduce((current, key) => {
    if (!current[key as keyof typeof current] || typeof current[key as keyof typeof current] !== 'object') {
      // @ts-expect-error - ignore this.
      current[key as keyof typeof current] = {};
    }

    return current[key as keyof typeof current];
  }, obj);

  // @ts-expect-error - ignore this.
  target[lastKey as keyof typeof target] = value;
}

function getMissingKeys(baseObj: object, targetObj: object): string[] {
  const baseKeys = getNestedKeys(baseObj);
  const targetKeys = new Set(getNestedKeys(targetObj));

  return baseKeys.filter((key) => !targetKeys.has(key));
}

function extractKeysByPaths(obj: object, paths: string[]): Record<string, unknown> {
  const result: Record<string, unknown> = {};

  for (const p of paths) {
    const value = getValueByPath(obj, p);
    if (value !== undefined) {
      setValueByPath(result, p, value);
    }
  }

  return result;
}

async function analyzeTranslationNeeds(baseFile: string, targetFile: string): Promise<TranslationDiff | null> {
  try {
    const baseContent = await read(baseFile, 'utf8');
    const baseJson = JSON.parse(baseContent);

    let targetJson = {};
    if (fs.existsSync(targetFile)) {
      const targetContent = await read(targetFile, 'utf8');
      targetJson = JSON.parse(targetContent);
    }

    const missingKeys = getMissingKeys(baseJson, targetJson);

    if (missingKeys.length === 0) {
      return null; // No translation needed
    }

    const keysToTranslate = extractKeysByPaths(baseJson, missingKeys);

    return {
      filePath: targetFile,
      missingKeys,
      keysToTranslate: keysToTranslate as Record<string, object>,
    };
  } catch (error) {
    console.error(`Error analyzing ${targetFile}:`, error);
    return null;
  }
}

async function mergeTranslations(targetFile: string, newTranslations: object): Promise<void> {
  let existingJson = {};

  if (fs.existsSync(targetFile)) {
    const existingContent = await read(targetFile, 'utf8');
    existingJson = JSON.parse(existingContent);
  }

  // Deep merge the translations
  const mergedJson = deepMerge(existingJson, newTranslations);
  await write(targetFile, JSON.stringify(mergedJson, null, 2), 'utf8');
}

function deepMerge(target: object, source: object): object {
  const result = { ...target };

  for (const [key, value] of Object.entries(source)) {
    if (value && typeof value === 'object' && !Array.isArray(value)) {
      // @ts-expect-error - ignore this.
      result[key as keyof typeof result] = deepMerge(target[key as keyof typeof result] || {}, value);
    } else {
      // @ts-expect-error - ignore this.
      result[key as keyof typeof result] = value;
    }
  }

  return result;
}

async function main() {
  const DEFAULT_LANGUAGE = 'en-US';
  const baseDir = path.join(LOCALES_DIR, DEFAULT_LANGUAGE);

  console.log('🔍 Scanning for translation needs...');

  try {
    const baseFiles = await getJsonFiles(baseDir);

    if (baseFiles.length === 0) {
      console.log('❌ No base language files found');
      return;
    }

    let totalTranslationsNeeded = 0;
    let languagesProcessed = 0;

    for await (const lang of DISCORD_LANGUAGE_CODES) {
      if (lang === (DEFAULT_LANGUAGE as (typeof DISCORD_LANGUAGE_CODES)[number])) {
        continue; // Skip base language
      }

      const targetDir = path.join(LOCALES_DIR, lang);

      // Ensure directory exists
      if (!fs.existsSync(targetDir)) {
        await mkdir(targetDir, { recursive: true });
        console.log(`📁 Created directory: ${targetDir}`);
      }

      const translationTasks: TranslationDiff[] = [];

      // Analyze each base file for missing translations
      for await (const baseFile of baseFiles) {
        const relativePath = path.relative(baseDir, baseFile);
        const targetFile = path.join(targetDir, relativePath);

        // Ensure target subdirectories exist
        const targetSubDir = path.dirname(targetFile);
        if (!fs.existsSync(targetSubDir)) {
          await mkdir(targetSubDir, { recursive: true });
        }

        const diff = await analyzeTranslationNeeds(baseFile, targetFile);
        if (diff) {
          translationTasks.push(diff);
        }
      }

      if (translationTasks.length === 0) {
        console.log(`✅ ${lang}: All translations up to date`);
        continue;
      }

      console.log(`🔄 ${lang}: Found ${translationTasks.length} files needing translation`);
      languagesProcessed++;

      // Process translations for this language
      for await (const task of translationTasks) {
        const fileName = path.basename(task.filePath);
        const keyCount = task.missingKeys.length;

        console.log(`  📝 Translating ${keyCount} keys in ${fileName}...`);

        try {
          const translatedContent = await ai(task.keysToTranslate, DEFAULT_LANGUAGE, lang);
          await mergeTranslations(task.filePath, translatedContent);

          console.log(`  ✅ Completed ${fileName} (${keyCount} keys)`);
          totalTranslationsNeeded += keyCount;

          await wait(2000);
        } catch (error) {
          console.error(`  ❌ Failed to translate ${fileName} - defaulting to EN-US File Content, Error:`, error);
          await mergeTranslations(task.filePath, task.keysToTranslate);
        }
      }

      // Longer wait between languages
      if (translationTasks.length > 0) {
        await wait(5000);
      }
    }

    if (languagesProcessed === 0) {
      console.log('🎉 All translations are up to date!');
    } else {
      console.log('\n🎉 Translation complete!');
      console.log(`   Languages processed: ${languagesProcessed}`);
      console.log(`   Total keys translated: ${totalTranslationsNeeded}`);
    }
  } catch (error) {
    console.error('💥 Fatal error:', error);
    throw error;
  }
}

main().catch((err) => {
  console.error('💥 Script failed:', err);
  process.exit(1);
});
