import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Moonlight Serenade',
  name: 'Moonlight Serenade',
  element: 'Selene',
  manaCost: 15,
  cooldown: 3,
  description: 'Sing a haunting serenade that puts the target to sleep and restores your mana.',
  execute: (caster, target, formulas) => {
    const baseChance = 50;
    const sleepChance = formulas.calculateEffectChance(caster, target, baseChance);
    const didSleep = formulas.percentage(sleepChance / 100);
    const manaRestored = 10;

    caster.stats.mana = Math.min(caster.stats.maxMana, caster.stats.mana + manaRestored);

    return {
      appliedEffect: didSleep
        ? {
            id: 'sleep',
            name: 'Sleep',
            duration: 2,
            potency: 0, // Sleep effect (skip turns)
            sourceId: caster.id,
          }
        : undefined,
      log: `${caster.name} sings a Moonlight Serenade${didSleep ? `, putting ${target.name} to sleep` : ''} and restores ${manaRestored} mana!`,
    };
  },
});
