import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Mel<PERSON>down',
  name: 'Mel<PERSON><PERSON>',
  element: 'Sirius',
  manaCost: 40,
  cooldown: 6,
  description: 'The target has a meltdown, taking massive damage over time and spreading a damaging aura to nearby enemies.',
  execute: (caster, target, formulas) => {
    // This would require a status effect system to handle the DoT and aura.
    return {
      log: `${caster.name} causes ${target.name} to have a meltdown.`,
    };
  },
});
