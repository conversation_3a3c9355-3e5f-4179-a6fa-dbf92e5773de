import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Abyss',
  name: 'Abyss',
  element: 'Vortex',
  manaCost: 60,
  cooldown: 8,
  description: 'Opens an abyss that pulls all enemies towards it and deals damage over time.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Abyss Effect',
        name: 'Abyss',
        duration: 3,
        sourceId: caster.id,
      },
      log: `${caster.name} opens an Abyss.`,
    };
  },
});
