# `packages/utils`

This package provides a collection of utility functions for various purposes, from array manipulation to experience calculation.

## Installation

To install dependencies:

```bash
bun install
```

## Usage

To run:

```bash
bun run index.ts
```

---

## Features

### Array Utilities (`lib/array.ts`)

A comprehensive set of functions for array manipulation.

-   **`first<T>(array: T[]): T | undefined`**
    Returns the first element of an array.
    -   `array`: The input array.
    -   **Returns:** The first element, or `undefined` if the array is empty.

-   **`last<T>(array: T[]): T | undefined`**
    Returns the last element of an array.
    -   `array`: The input array.
    -   **Returns:** The last element, or `undefined` if the array is empty.

-   **`sample<T>(array: T[]): T | undefined`**
    Returns a random element from an array.
    -   `array`: The input array.
    -   **Returns:** A random element, or `undefined` if the array is empty.

-   **`shuffle<T>(array: T[]): T[]`**
    Shuffles an array in place using the Fisher-Yates algorithm.
    -   `array`: The array to shuffle.
    -   **Returns:** The shuffled array.

-   **`unique<T>(array: T[]): T[]`**
    Returns a new array with unique elements from the input array.
    -   `array`: The input array.
    -   **Returns:** A new array with unique elements.

-   **`chunk<T>(array: T[], size: number): T[][]`**
    Chunks an array into smaller arrays of a specified size.
    -   `array`: The array to chunk.
    -   `size`: The size of each chunk.
    -   **Returns:** An array of chunks.

-   **`zip<T, U>(a: T[], b: U[]): [T, U][]`**
    Zips two arrays together into an array of tuples.
    -   `a`: The first array.
    -   `b`: The second array.
    -   **Returns:** An array of tuples, where each tuple contains elements from the input arrays at the same index.

-   **`unzip<T, U>(array: [T, U][]): [T[], U[]]`**
    Unzips an array of tuples into two arrays.
    -   `array`: The array of tuples to unzip.
    -   **Returns:** A tuple containing two arrays.

**Example:**

```typescript
import { first, chunk, shuffle } from './lib/array';

const arr = [1, 2, 3, 4, 5];
console.log(first(arr)); // 1
console.log(chunk(arr, 2)); // [[1, 2], [3, 4], [5]]
console.log(shuffle(arr)); // [3, 1, 5, 2, 4] (example output)
```

### Case Conversion (`lib/case.ts`)

-   **`toProperCase<T extends string>(input: T): Capitalize<T>`**
    Converts a string to proper case (e.g., "hello world" -> "Hello World").
    -   `input`: The string to convert.
    -   **Returns:** The proper-cased string.

### Experience Calculation (`lib/experience.ts`)

-   **`calculateNextLevelExp(level: number): number`**
    Calculates the experience required to reach the next level based on a formula.
    -   `level`: The current level.
    -   **Returns:** The experience required for the next level.

### Map Utilities (`lib/map.ts`)

A set of wrapper functions for `Map` objects, providing a more functional-style interface.

### Object Utilities (`lib/object.ts`)

A set of functions for object manipulation, similar to the array utilities.

### Progress Bar (`lib/progress.ts`)

-   **`defineProgress(current: number, total: number): string`**
    Creates a visual progress bar string using custom emotes.
    -   `current`: The current progress value.
    -   `total`: The total value.
    -   **Returns:** A string representing the progress bar.

### Random Utilities (`lib/random.ts`)

-   **`percentage(chance: number): boolean`**
    Returns a boolean indicating if a percentage chance is met.
    -   `chance`: The percentage chance (0 to 1).
    -   **Returns:** `true` if the chance was met, `false` otherwise.

-   **`randomInRange(min: number, max: number): number`**
    Returns a random integer between a minimum and maximum value (inclusive).
    -   `min`: The minimum value.
    -   `max`: The maximum value.
    -   **Returns:** A random integer.

### Set Utilities (`lib/set.ts`)

Functions for performing set operations.

-   `union(a, b)`
-   `intersection(a, b)`
-   `difference(a, b)`
-   `symmetric(a, b)`

### Skills (`lib/skills.ts`)

-   **`random.skill(element: string)`**
    Returns a random skill for a given element.
    -   `element`: The element to get a random skill for.
    -   **Returns:** A random skill object, or `undefined` if the element has no skills.

### Stats (`lib/stats.ts`)

-   **`generate()`**
    Generates random stats for different tiers (1, 2, and 3).
    -   **Returns:** An object containing the generated stats for each tier.

### Wait (`lib/wait.ts`)

-   **`wait(ms: number)`**
    Pauses execution for a specified number of milliseconds.
    -   `ms`: The number of milliseconds to wait.
    -   **Returns:** A promise that resolves after the specified time.
