import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Mirror Image',
  name: 'Mirror Image',
  element: 'Selene',
  manaCost: 18,
  cooldown: 4,
  description: 'Create mirror images that greatly reduce incoming damage and boost evasion.',
  execute: (caster) => {
    return {
      appliedEffect: {
        id: 'mirror_image',
        name: 'Mirror Image',
        duration: 3,
        potency: 0.3, // 70% damage reduction
        sourceId: caster.id,
      },
      log: `${caster.name} creates Mirror Images, greatly reducing incoming damage!`,
    };
  },
});
