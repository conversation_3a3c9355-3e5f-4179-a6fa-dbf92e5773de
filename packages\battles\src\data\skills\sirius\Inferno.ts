import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Inferno',
  name: 'Inferno',
  element: 'Sirius',
  manaCost: 60,
  cooldown: 8,
  description: 'Engulfs the entire battlefield in an inferno, dealing massive damage to all enemies over time.',
  execute: (caster, target, formulas) => {
    // This would require a status effect system to handle the DoT.
    return {
      log: `${caster.name} engulfs the battlefield in an Inferno.`,
    };
  },
});
