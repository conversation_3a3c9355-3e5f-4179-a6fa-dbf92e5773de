import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Quantum Entanglement',
  name: 'Quantum Entanglement',
  element: 'Vortex',
  manaCost: 35,
  cooldown: 5,
  description: 'The caster entangles two enemies. Whenever one takes damage, the other takes a portion of that damage.',
  execute: (caster, target, formulas) => {
    // This would require a more complex targeting and status effect system.
    return {
      log: `${caster.name} entangles two enemies.`,
    };
  },
});
