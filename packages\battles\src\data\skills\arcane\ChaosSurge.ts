import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Chaos Surge',
  name: 'Chaos Surge',
  element: 'Arcane',
  manaCost: 18,
  cooldown: 1,
  description: 'Unleash chaotic energy, dealing high damage with a chance to confuse the target.',
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const baseChance = 40;
    const confuseChance = formulas.calculateEffectChance(caster, target, baseChance);
    const didConfuse = formulas.percentage(confuseChance / 100);
    return {
      damage: Math.round(damage * 1.2),
      isCritical,
      appliedEffect: didConfuse
        ? {
            id: 'confuse',
            name: 'Confused',
            duration: 2,
            potency: 0,
            sourceId: caster.id,
          }
        : undefined,
      log: `${caster.name} surges chaos at ${target.name} for ${Math.round(damage * 1.2)} damage${isCritical ? ' (CRIT!)' : ''}.${didConfuse ? ` ${target.name} is confused!` : ''}`,
    };
  },
});
