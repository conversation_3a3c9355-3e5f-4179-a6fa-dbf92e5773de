import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Mind Twist',
  name: 'Mind Twist',
  element: 'Vortex',
  manaCost: 35,
  cooldown: 5,
  description: 'Twists the target\'s mind, causing them to be confused and have a chance to attack their allies.',
  execute: (caster, target, formulas) => {
    // This would require a status effect system to handle confusion and a more complex targeting system.
    return {
      log: `${caster.name} twists the mind of ${target.name}.`,
    };
  },
});
