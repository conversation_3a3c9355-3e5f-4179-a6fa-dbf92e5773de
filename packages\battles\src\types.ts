import type { Skill } from './registry';

// The fundamental stats for any entity in battle.
export type StatKey = 'health' | 'maxHealth' | 'mana' | 'maxMana' | 'attack' | 'defense' | 'speed' | 'luck';

// A dictionary to hold the numerical value for each stat.
export type Stats = Record<StatKey, number>;

// Represents an active status effect on an entity.
export interface ActiveStatusEffect {
  id: string; // e.g., 'poison', 'burn', 'attack-boost'
  name: string; // e.g., 'Poisoned', 'On Fire', 'Strengthened'
  duration: number; // Turns remaining. Use Infinity for permanent effects.
  potency: number; // e.g., damage per turn for poison, or % boost for a buff.
  sourceId: string; // ID of the entity that applied the effect.
}

// Represents a single entity (player or enemy) in a battle.
export interface BattleEntity {
  id: string; // Unique identifier.
  name: string;
  isPlayer: boolean;
  element: 'Arcane' | 'Vortex' | 'Sirius' | 'Selene';
  stats: Stats;
  skill: Skill;
  activeEffects: ActiveStatusEffect[];
  cooldowns: Record<string, number>; // Tracks skill cooldowns, mapping skillId to turns remaining.
}

// Defines a player's available actions in a turn.
export type PlayerAction =
  | { type: 'BASIC_ATTACK'; targetId: string }
  | { type: 'SKILL'; skillId: string; targetId: string }
  | { type: 'HEAL'; amount: number }; // Simple heal for now.

// A log entry to narrate the battle events for the Discord embed.
export interface BattleLogEntry {
  turn: number;
  message: string;
}
