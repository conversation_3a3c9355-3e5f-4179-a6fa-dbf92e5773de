import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Gravitational Field',
  name: 'Gravitational Field',
  element: 'Vortex',
  manaCost: 40,
  cooldown: 6,
  description: 'Creates a gravitational field that slows all enemies and deals damage over time.',
  execute: (caster, target, formulas) => {
    // This would require a system for creating and managing persistent area effects with a slow and DoT.
    return {
      log: `${caster.name} creates a Gravitational Field.`,
    };
  },
});
