import { GlowEffect } from '../registry/glow-effect';

export function GlowEffectCardBackground({ children }: { children: React.ReactNode }) {
  return (
    <div className="relative h-44 w-64">
      <GlowEffect blur="medium" colors={['#0894FF', '#C959DD', '#FF2E54', '#FF9004']} mode="static" />
      <div className="relative h-44 w-64 rounded-lg bg-black p-2 text-white dark:bg-white dark:text-black">
        {children}
      </div>
    </div>
  );
}
