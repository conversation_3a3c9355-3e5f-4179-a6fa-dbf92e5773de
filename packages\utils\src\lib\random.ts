/**
 * Returns a boolean indicating if a percentage chance is met.
 * @param chance The percentage chance, between 0 and 1.
 * @returns A boolean indicating if the chance was met.
 */
export function percentage(chance: number): boolean {
  return Math.random() < chance;
}

/**
 * Returns a random integer between a minimum and maximum value (inclusive).
 * @param min The minimum value.
 * @param max The maximum value.
 * @returns A random integer between the minimum and maximum.
 */
export function randomInRange(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}
