import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Hellfire',
  name: 'Hellfire',
  element: 'Sirius',
  manaCost: 45,
  cooldown: 6,
  description: 'Calls down hellfire, damaging all enemies in an area and reducing their defense.',
  execute: (caster, target, formulas) => {
    // This would require a more complex targeting and status effect system.
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const finalDamage = Math.round(damage * 1.6);
    return {
      damage: finalDamage,
      isCritical,
      log: `${caster.name} calls down Hellfire, damaging and weakening all enemies for ${finalDamage} damage${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
