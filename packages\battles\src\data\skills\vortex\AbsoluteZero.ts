import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Absolute Zero',
  name: 'Absolute Zero',
  element: 'Vortex',
  manaCost: 50,
  cooldown: 7,
  description: 'The caster lowers the temperature to absolute zero, freezing all enemies and dealing damage over time.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Absolute Zero Effect',
        name: 'Absolute Zero',
        duration: 2,
        sourceId: caster.id,
      },
      log: `${caster.name} lowers the temperature to absolute zero.`,
    };
  },
});
