import { createSelectSchema } from 'drizzle-zod';
import type { MegamiDatabaseClient } from '../client';
import { BaseModel } from '../model';

export class Mails extends BaseModel {
  public schema = createSelectSchema(this.schemas.mails);

  constructor(client: MegamiDatabaseClient, data: unknown) {
    super(client);

    const validated = this.schema.safeParse(data);
    if (validated.error) {
      throw new Error(`Mails Schema Parse Error: ${validated.error.issues.length}`);
    }
  }
}
