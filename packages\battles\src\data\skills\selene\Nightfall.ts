import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Nightfall',
  name: 'Nightfall',
  element: 'Selene',
  manaCost: 30,
  cooldown: 5,
  description: 'Summons night, reducing the accuracy of all enemies.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Accuracy Down',
        name: 'Nightfall',
        duration: 3,
        sourceId: caster.id,
      },
      log: `${caster.name} summons night, making it harder for enemies to hit.`,
    };
  },
});
