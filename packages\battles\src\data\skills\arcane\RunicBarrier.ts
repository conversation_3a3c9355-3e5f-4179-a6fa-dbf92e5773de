import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Runic Barrier',
  name: 'Runic Barrier',
  element: 'Arcane',
  manaCost: 20,
  cooldown: 3,
  description: 'Creates a barrier of runes that absorbs incoming damage.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Defense Up',
        name: 'Runic Barrier',
        duration: 3,
        sourceId: caster.id,
      },
      log: `${caster.name} creates a Runic Barrier, absorbing incoming damage.`,
    };
  },
});
