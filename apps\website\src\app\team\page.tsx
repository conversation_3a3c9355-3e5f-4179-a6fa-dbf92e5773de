'use client';

import { motion } from 'framer-motion';
import { Navbar } from '@/components/navbar';
import { TeamMemberCard } from '@megami/ui/components/jules/team-member-card';

const teamMembers = [
  {
    name: '<PERSON><PERSON><PERSON>',
    role: 'Lead Developer',
    avatarSrc: 'https://placehold.co/128x128/8A2BE2/FFFFFF/png?text=K',
  },
  {
    name: '<PERSON>',
    role: 'AI Engineer',
    avatarSrc: 'https://placehold.co/128x128/000000/FFFFFF/png?text=J',
  },
];

export default function TeamPage() {
  return (
    <main className="min-h-screen w-full bg-black text-white">
      <Navbar />
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1 }}
        className="container mx-auto px-4 py-8"
      >
        <h1 className="text-4xl font-bold text-center mb-8">Meet the Team</h1>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 justify-center">
          {teamMembers.map((member, i) => (
            <motion.div
              key={member.name}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: i * 0.1 }}
            >
              <TeamMemberCard {...member} />
            </motion.div>
          ))}
        </div>
      </motion.div>
    </main>
  );
}
