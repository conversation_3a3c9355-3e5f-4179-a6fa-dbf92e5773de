import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Flame Pillar',
  name: 'Flame Pillar',
  element: 'Sirius',
  manaCost: 25,
  cooldown: 3,
  description: 'A pillar of flame erupts from the ground, damaging and knocking up all enemies in a small area.',
  execute: (caster, target, formulas) => {
    // This would require a more complex targeting and status effect system.
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const finalDamage = Math.round(damage * 1.2);
    return {
      damage: finalDamage,
      isCritical,
      log: `${caster.name} summons a Flame Pillar, damaging and knocking up all nearby enemies for ${finalDamage} damage${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
