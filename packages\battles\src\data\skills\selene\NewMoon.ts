import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'New Moon',
  name: 'New Moon',
  element: '<PERSON><PERSON>',
  manaCost: 30,
  cooldown: 5,
  description: 'The caster is shrouded by the new moon, becoming invisible and silencing all enemies in an area.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'New Moon Effect',
        name: 'New Moon',
        duration: 3,
        sourceId: caster.id,
      },
      log: `${caster.name} is shrouded by the new moon.`,
    };
  },
});
