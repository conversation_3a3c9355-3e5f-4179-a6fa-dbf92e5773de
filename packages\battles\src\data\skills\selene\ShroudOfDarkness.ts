import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Shroud of Darkness',
  name: 'Shroud of Darkness',
  element: 'Se<PERSON>',
  manaCost: 30,
  cooldown: 5,
  description: 'The caster is shrouded in darkness, becoming invisible and gaining bonus movement speed. Their first attack out of invisibility will fear the target.',
  execute: (caster, target, formulas) => {
    // This would require a status effect system to handle invisibility, bonus movement speed, and fear.
    return {
      log: `${caster.name} is shrouded in darkness.`,
    };
  },
});
