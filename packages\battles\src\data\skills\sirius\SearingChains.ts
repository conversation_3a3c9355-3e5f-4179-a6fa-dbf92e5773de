import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Searing Chains',
  name: 'Searing Chains',
  element: 'Sirius',
  manaCost: 30,
  cooldown: 5,
  description: 'Binds the target in searing chains, damaging and rooting them.',
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const finalDamage = Math.round(damage * 1.3);
    // This would also require a status effect system to handle the root.
    return {
      damage: finalDamage,
      isCritical,
      log: `${caster.name} binds ${target.name} in Searing Chains for ${finalDamage} damage${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
