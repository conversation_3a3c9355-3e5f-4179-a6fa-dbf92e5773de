import { boolean, index, integer, jsonb, pgTable, text, timestamp, uniqueIndex, uuid } from 'drizzle-orm/pg-core';
import { items } from './items';
import { users } from './users';

export interface HoldingMetadata {
  // For equipment: current durability, enchantments, etc.
  durability?: {
    current: number;
    maximum: number;
  };

  // For consumables: expiration date, potency, etc.
  consumable?: {
    expiresAt?: number; // timestamp
    potency?: number; // 0-100, affects effectiveness
  };

  // For any item: custom properties, acquisition info, etc.
  acquisition?: {
    source: 'PURCHASE' | 'REWARD' | 'CRAFT' | 'TRADE' | 'DROP' | 'GIFT' | 'EVENT';
    sourceId?: string; // ID of the source (quest, shop, etc.)
    acquiredAt: number; // timestamp
  };

  // Equipment-specific: current slot, modifications
  equipment?: {
    equipped: boolean;
    slot?: 'WEAPON' | 'ARMOR' | 'ACCESSORY' | 'CONSUMABLE_SLOT';
    enchantments?: Array<{
      type: string;
      level: number;
      value: number;
    }>;
  };

  // User customization
  custom?: {
    nickname?: string; // User-given name for the item
    notes?: string; // User notes
    favorite?: boolean; // Marked as favorite
    locked?: boolean; // Prevent accidental deletion/sale
  };
}

export const holdings = pgTable(
  'holdings',
  {
    id: uuid('id').primaryKey().defaultRandom(),

    // Foreign keys
    userId: text('user_id')
      .references(() => users.id, { onDelete: 'cascade' })
      .notNull(),
    itemId: uuid('item_id')
      .references(() => items.id, { onDelete: 'cascade' })
      .notNull(),

    // Quantity and state
    quantity: integer('quantity').default(1).notNull(),

    // Item-specific metadata (durability, enchantments, etc.)
    metadata: jsonb('metadata').$type<HoldingMetadata>().default({}).notNull(),

    // Inventory organization
    inventorySlot: integer('inventory_slot'), // For organized inventories
    isEquipped: boolean('is_equipped').default(false).notNull(),
    isFavorite: boolean('is_favorite').default(false).notNull(),
    isLocked: boolean('is_locked').default(false).notNull(), // Prevent accidental actions

    // Timestamps
    obtainedAt: timestamp('obtained_at').defaultNow().notNull(),
    lastUsedAt: timestamp('last_used_at'),
    updatedAt: timestamp('updated_at').defaultNow().notNull(),
  },
  (table) => [
    // Indexes for efficient queries
    index('holdings_user_idx').on(table.userId),
    index('holdings_item_idx').on(table.itemId),
    index('holdings_user_item_idx').on(table.userId, table.itemId),
    index('holdings_equipped_idx').on(table.userId, table.isEquipped),
    index('holdings_slot_idx').on(table.userId, table.inventorySlot),

    // Unique constraint for non-stackable items (one per user per item)
    // Note: This will be handled in application logic for stackable items
    uniqueIndex('holdings_unique_idx').on(table.userId, table.itemId, table.inventorySlot),
  ]
);
