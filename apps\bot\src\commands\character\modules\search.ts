import { getObject, translate } from '@megami/locale';
import { defineSubCommand } from '../../../handlers/command';
import { EmbedBuilder } from 'discord.js';

export default defineSubCommand((builder) => ({
  builder: builder
    .setName(translate('commands.character.modules.search.name'))
    .setNameLocalizations(getObject('commands.character.modules.search.name'))
    .setDescription(translate('commands.character.modules.search.description'))
    .setDescriptionLocalizations(getObject('commands.character.modules.search.description'))
    .addStringOption((option) =>
      option
        .setName(translate('commands.character.modules.search.options.query.name'))
        .setNameLocalizations(getObject('commands.character.modules.search.options.query.name'))
        .setDescription(translate('commands.character.modules.search.options.query.description'))
        .setDescriptionLocalizations(getObject('commands.character.modules.search.options.query.description'))
        .setRequired(true)
        .setAutocomplete(true)
    )
    .addStringOption((option) =>
      option
        .setName(translate('commands.character.modules.search.options.skill.name'))
        .setNameLocalizations(getObject('commands.character.modules.search.options.skill.name'))
        .setDescription(translate('commands.character.modules.search.options.skill.description'))
        .setDescriptionLocalizations(getObject('commands.character.modules.search.options.skill.description'))
        .setRequired(false)
    )
    .addStringOption((option) =>
        option
          .setName(translate('commands.character.modules.search.options.element.name'))
          .setNameLocalizations(getObject('commands.character.modules.search.options.element.name'))
          .setDescription(translate('commands.character.modules.search.options.element.description'))
          .setDescriptionLocalizations(getObject('commands.character.modules.search.options.element.description'))
          .setRequired(false)
      )
    .addStringOption((option) =>
      option
        .setName(translate('commands.character.modules.search.options.series.name'))
        .setNameLocalizations(getObject('commands.character.modules.search.options.series.name'))
        .setDescription(translate('commands.character.modules.search.options.series.description'))
        .setDescriptionLocalizations(getObject('commands.character.modules.search.options.series.description'))
        .setRequired(false)
        .setAutocomplete(true)
    ),
  config: {},
  async autocomplete(interaction) {
    const focused = interaction.options.getFocused(true);

    if (focused.name === translate('commands.character.modules.search.options.query.name')) {
      try {
        const characters = interaction.client.search.characters.search(focused.value);
        await interaction.respond(
          characters.map((character) => ({
            name: `${character.name} (${character.series?.name || 'Unknown Series'})`,
            value: character.id,
          }))
        );
      } catch (error) {
        interaction.client.logger.error('Error in character autocomplete:', error);
        await interaction.respond([]);
      }
    }

    if (focused.name === translate('commands.character.modules.search.options.series.name')) {
      try {
        const series = interaction.client.search.series.search(focused.value);
        await interaction.respond(
          series.map((s) => ({
            name: s.name,
            value: s.id,
          }))
        );
      } catch (error) {
        interaction.client.logger.error('Error in series autocomplete:', error);
        await interaction.respond([]);
      }
    }
  },
  async execute(interaction) {
    const query = interaction.options.getString(translate('commands.character.modules.search.options.query.name'), true);
    const skill = interaction.options.getString(translate('commands.character.modules.search.options.skill.name'));
    const element = interaction.options.getString(translate('commands.character.modules.search.options.element.name'));
    const seriesId = interaction.options.getString(translate('commands.character.modules.search.options.series.name'));

    const results = interaction.client.search.characters.search(query, {
      filter: (result) => {
        if (skill && result.skill !== skill) {
          return false;
        }
        if (element && result.element !== element) {
          return false;
        }
        if (seriesId && result.seriesId !== seriesId) {
          return false;
        }
        return true;
      },
    });

    if (results.length === 0) {
      await interaction.reply({
        content: 'No characters found.',
        ephemeral: true,
      });
      return;
    }

    const embeds = results.map((result) => {
      const embed = new EmbedBuilder()
        .setTitle(result.name)
        .setDescription(result.description || 'No description available.')
        .addFields(
            { name: 'ID', value: result.id, inline: true },
            { name: 'Skill', value: result.skill || 'N/A', inline: true },
            { name: 'Element', value: result.element || 'N/A', inline: true },
            { name: 'Series', value: result.series?.name || 'N/A', inline: true }
        );
      if (result.image) {
        embed.setThumbnail(result.image);
      }
      return embed;
    });

    await interaction.reply({ embeds });
  },
}));
