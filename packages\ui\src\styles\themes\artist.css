.artist {
  --background: oklch(0.96 0.03 106.92);
  --foreground: oklch(0.4 0.07 90.8);
  --card: oklch(0.98 0.04 97.73);
  --card-foreground: oklch(0.32 0 0);
  --popover: oklch(0.98 0.04 97.73);
  --popover-foreground: oklch(0.32 0 0);
  --primary: oklch(0.77 0.14 91.27);
  --primary-foreground: oklch(0 0 0);
  --secondary: oklch(0.67 0.13 61.58);
  --secondary-foreground: oklch(0 0 0);
  --muted: oklch(0.93 0.03 106.94);
  --muted-foreground: oklch(0.32 0 0);
  --accent: oklch(0.89 0.18 95.47);
  --accent-foreground: oklch(0.32 0 0);
  --destructive: oklch(0.69 0.2 32.29);
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.83 0.11 93.01);
  --input: oklch(0.65 0.13 81.66);
  --ring: oklch(0.75 0.15 84.05);
  --chart-1: oklch(0.89 0.18 95.47);
  --chart-2: oklch(0.67 0.13 61.58);
  --chart-3: oklch(0.65 0.13 81.66);
  --chart-4: oklch(0.75 0.15 84.05);
  --chart-5: oklch(0.77 0.14 91.27);
  --sidebar: oklch(0.96 0.03 106.92);
  --sidebar-foreground: oklch(0.32 0 0);
  --sidebar-primary: oklch(0.77 0.14 91.27);
  --sidebar-primary-foreground: oklch(0.32 0 0);
  --sidebar-accent: oklch(0.89 0.18 95.47);
  --sidebar-accent-foreground: oklch(0.32 0 0);
  --sidebar-border: oklch(0.65 0.13 81.66);
  --sidebar-ring: oklch(0.75 0.15 84.05);

  --font-sans: Delius Swash Caps;
  --font-serif: Delius Swash Caps;
  --font-mono: Delius Swash Caps;

  --radius: 0.625rem;

  --shadow-2xs: 0px 1px 3px 0px oklch(0.7 0.17 28.12 / 30% / 0.03);
  --shadow-xs: 0px 1px 3px 0px oklch(0.7 0.17 28.12 / 30% / 0.03);
  --shadow-sm:
    0px 1px 3px 0px oklch(0.7 0.17 28.12 / 30% / 0.05), 0px 1px 2px -1px
    oklch(0.7 0.17 28.12 / 30% / 0.05);
  --shadow:
    0px 1px 3px 0px oklch(0.7 0.17 28.12 / 30% / 0.05), 0px 1px 2px -1px
    oklch(0.7 0.17 28.12 / 30% / 0.05);
  --shadow-md:
    0px 1px 3px 0px oklch(0.7 0.17 28.12 / 30% / 0.05), 0px 2px 4px -1px
    oklch(0.7 0.17 28.12 / 30% / 0.05);
  --shadow-lg:
    0px 1px 3px 0px oklch(0.7 0.17 28.12 / 30% / 0.05), 0px 4px 6px -1px
    oklch(0.7 0.17 28.12 / 30% / 0.05);
  --shadow-xl:
    0px 1px 3px 0px oklch(0.7 0.17 28.12 / 30% / 0.05), 0px 8px 10px -1px
    oklch(0.7 0.17 28.12 / 30% / 0.05);
  --shadow-2xl: 0px 1px 3px 0px oklch(0.7 0.17 28.12 / 30% / 0.13);
}

.artist-dark {
  --background: oklch(0.32 0 0);
  --foreground: oklch(0.96 0.03 106.92);
  --card: oklch(0.41 0 0);
  --card-foreground: oklch(0.96 0.03 106.92);
  --popover: oklch(0.41 0 0);
  --popover-foreground: oklch(0.96 0.03 106.92);
  --primary: oklch(0.84 0.17 83.07);
  --primary-foreground: oklch(0 0 0);
  --secondary: oklch(0.47 0.11 50.64);
  --secondary-foreground: oklch(0.96 0.03 106.92);
  --muted: oklch(0.44 0 0);
  --muted-foreground: oklch(0.96 0.03 106.92);
  --accent: oklch(0.66 0.14 79.74);
  --accent-foreground: oklch(0 0 0);
  --destructive: oklch(0.66 0.23 35.22);
  --border: oklch(0.47 0.11 50.64);
  --input: oklch(0.47 0.11 50.64);
  --ring: oklch(0.65 0.13 81.66);
  --chart-1: oklch(0.75 0.15 84.05);
  --chart-2: oklch(0.47 0.11 50.64);
  --chart-3: oklch(0.65 0.13 81.66);
  --chart-4: oklch(0.75 0.15 84.05);
  --chart-5: oklch(0.65 0.13 81.66);
  --sidebar: oklch(0.32 0 0);
  --sidebar-foreground: oklch(1 0 0);
  --sidebar-primary: oklch(0.61 0.13 79.65);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.75 0.15 84.05);
  --sidebar-accent-foreground: oklch(0.96 0.03 106.92);
  --sidebar-border: oklch(0.47 0.11 50.64);
  --sidebar-ring: oklch(0.65 0.13 81.66);

  --shadow-2xs: 0px 1px 3px 0px oklch(0 0 0 / 5% / 0.03);
  --shadow-xs: 0px 1px 3px 0px oklch(0 0 0 / 5% / 0.03);
  --shadow-sm:
    0px 1px 3px 0px oklch(0 0 0 / 5% / 0.05), 0px 1px 2px -1px
    oklch(0 0 0 / 5% / 0.05);
  --shadow:
    0px 1px 3px 0px oklch(0 0 0 / 5% / 0.05), 0px 1px 2px -1px
    oklch(0 0 0 / 5% / 0.05);
  --shadow-md:
    0px 1px 3px 0px oklch(0 0 0 / 5% / 0.05), 0px 2px 4px -1px
    oklch(0 0 0 / 5% / 0.05);
  --shadow-lg:
    0px 1px 3px 0px oklch(0 0 0 / 5% / 0.05), 0px 4px 6px -1px
    oklch(0 0 0 / 5% / 0.05);
  --shadow-xl:
    0px 1px 3px 0px oklch(0 0 0 / 5% / 0.05), 0px 8px 10px -1px
    oklch(0 0 0 / 5% / 0.05);
  --shadow-2xl: 0px 1px 3px 0px oklch(0 0 0 / 5% / 0.13);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
}
