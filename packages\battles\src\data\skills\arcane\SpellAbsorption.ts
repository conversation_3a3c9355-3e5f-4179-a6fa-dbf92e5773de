import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Spell Absorption',
  name: 'Spell Absorption',
  element: 'Arcane',
  manaCost: 20,
  cooldown: 4,
  description: 'The caster absorbs the next spell cast at them, converting it into mana.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Magic Immunity',
        name: 'Spell Absorption',
        duration: 1,
        sourceId: caster.id,
      },
      log: `${caster.name} prepares to absorb the next spell cast at them.`,
    };
  },
});
