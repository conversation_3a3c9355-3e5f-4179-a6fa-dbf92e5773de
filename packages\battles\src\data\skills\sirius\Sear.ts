import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Sear',
  name: 'Sear',
  element: 'Sirius',
  manaCost: 10,
  cooldown: 2,
  description: 'Sears the target, dealing damage and reducing their defense.',
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const finalDamage = Math.round(damage * 0.9);
    // This would also require a status effect system to handle the defense reduction.
    return {
      damage: finalDamage,
      isCritical,
      log: `${caster.name} sears ${target.name}, dealing ${finalDamage} damage and reducing their defense${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
