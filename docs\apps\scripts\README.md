# `apps/scripts`

This app is a command-line interface (CLI) for running various scripts.

## Installation

To install dependencies:

```bash
bun install
```

## Usage

To run the CLI:

```bash
bun run index.ts
```

---

## Architecture

The `scripts` app uses the `@clack/prompts` library to provide an interactive CLI for running various scripts. The main entry point is `src/index.ts`, which prompts the user to select a command to run.

### Available Commands

-   **`5DX`**: Converts a MP4 video to an animated WebP with a 9:16 aspect ratio and no background.
-   **`Emote`**: Converts a MP4 video to a square GIF with no background, suitable for use as a Discord emote.

### Image/Video Processing

The scripts use `ffmpeg` to perform the video and image processing. The `src/lib` directory contains helper functions for processing videos, such as removing the background, cropping, and trimming.
