import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Lunar Guidance',
  name: 'Lunar Guidance',
  element: 'Selene',
  manaCost: 20,
  cooldown: 4,
  description: 'The caster is guided by the moon, increasing their accuracy and evasion.',
  execute: (caster, target, formulas) => {
    // This would require a status effect system to handle the buffs.
    return {
      log: `${caster.name} is guided by the moon.`,
    };
  },
});
