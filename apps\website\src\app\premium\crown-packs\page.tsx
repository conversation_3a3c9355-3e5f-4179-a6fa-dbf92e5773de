'use client';

import { motion } from 'framer-motion';
import { Navbar } from '@/components/navbar';
import { PricingCard } from '@megami/ui/components/jules/pricing-card';

const crownPacks = [
  {
    title: 'New Collector Pack',
    price: '$1.99',
    features: ['100 Crowns', 'No Bonus', '$0.019 / Crown'],
    buttonText: 'Purchase',
  },
  {
    title: 'Crown Pack I',
    price: '$4.99',
    features: ['275 Crowns', '+10% Bonus', '$0.018 / Crown'],
    buttonText: 'Purchase',
  },
  {
    title: 'Crown Pack II',
    price: '$9.99',
    features: ['600 Crowns', '+20% Bonus', '$0.016 / Crown'],
    buttonText: 'Purchase',
    isFeatured: true,
  },
  {
    title: 'Crown Pack III',
    price: '$24.99',
    features: ['1600 Crowns', '+28% Bonus', '$0.015 / Crown'],
    buttonText: 'Purchase',
  },
  {
    title: 'Crown Pack IV',
    price: '$49.99',
    features: ['3500 Crowns', '+40% Bonus', '$0.014 / Crown'],
    buttonText: 'Purchase',
  },
  {
    title: 'Royal Vault',
    price: '$99.99',
    features: ['8000 Crowns', '+60% Bonus', '$0.0125 / Crown'],
    buttonText: 'Purchase',
  },
];

export default function CrownPacksPage() {
  return (
    <main className="min-h-screen w-full bg-black text-white">
      <Navbar />
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1 }}
        className="container mx-auto px-4 py-8"
      >
        <h1 className="text-5xl font-bold text-center mb-4">Crown Packs</h1>
        <p className="text-lg text-gray-400 text-center mb-12">
          Purchase Crowns to use in the game. The more you buy, the more you save!
        </p>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {crownPacks.map((pack, i) => (
            <motion.div
              key={pack.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: i * 0.1 }}
            >
              <PricingCard {...pack} />
            </motion.div>
          ))}
        </div>
      </motion.div>
    </main>
  );
}
