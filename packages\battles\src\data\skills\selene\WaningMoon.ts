import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Waning Moon',
  name: 'Waning Moon',
  element: 'Selene',
  manaCost: 35,
  cooldown: 5,
  description: 'The waning moon weakens the target, reducing their damage and defense.',
  execute: (caster, target, formulas) => {
    // This would require a status effect system to handle the debuffs.
    return {
      log: `${caster.name} weakens ${target.name} with the waning moon.`,
    };
  },
});
