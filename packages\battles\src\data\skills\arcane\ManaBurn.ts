import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Mana Burn',
  name: 'Mana Burn',
  element: 'Arcane',
  manaCost: 15,
  cooldown: 2,
  description: 'Burns the target\'s mana, dealing damage equal to the amount of mana burned.',
  execute: (caster, target, formulas) => {
    const manaBurned = 15;
    // In a real implementation, this would actually burn mana.
    return {
      damage: manaBurned,
      log: `${caster.name} burns ${manaBurned} of ${target.name}'s mana, dealing ${manaBurned} damage.`,
    };
  },
});
