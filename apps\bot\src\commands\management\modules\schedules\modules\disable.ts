import { getObject, translate } from '@megami/locale';
import { <PERSON><PERSON><PERSON><PERSON>, SeparatorBuilder, SeparatorSpacingSize, TextDisplayBuilder } from 'discord.js';
import { defineSubCommand } from '../../../../../handlers/command';
import { MegamiContainer } from '../../../../../helpers/containers';
import { defer } from '../../../../../helpers/defer';

export default defineSubCommand((builder) => ({
  builder: builder
    .setName(translate('commands.management.modules.schedules.modules.disable.name'))
    .setNameLocalizations(getObject('commands.management.modules.schedules.modules.disable.name'))
    .setDescription(translate('commands.management.modules.schedules.modules.disable.description'))
    .setDescriptionLocalizations(getObject('commands.management.modules.schedules.modules.disable.description'))
    .addStringOption((option) =>
      option
        .setName(translate('commands.management.modules.schedules.modules.disable.options.task.name'))
        .setNameLocalizations(getObject('commands.management.modules.schedules.modules.disable.options.task.name'))
        .setDescription(translate('commands.management.modules.schedules.modules.disable.options.task.description'))
        .setDescriptionLocalizations(
          getObject('commands.management.modules.schedules.modules.disable.options.task.description')
        )
        .setRequired(true)
        .setAutocomplete(true)
    )
    .addBooleanOption((option) =>
      option
        .setName(translate('commands.management.modules.schedules.modules.disable.options.force.name'))
        .setNameLocalizations(getObject('commands.management.modules.schedules.modules.disable.options.force.name'))
        .setDescription(translate('commands.management.modules.schedules.modules.disable.options.force.description'))
        .setDescriptionLocalizations(
          getObject('commands.management.modules.schedules.modules.disable.options.force.description')
        )
        .setRequired(false)
    ),
  config: {},
  execute: async (interaction) => {
    await defer(interaction, false);

    const taskId = interaction.options.getString('task', true);
    const force = interaction.options.getBoolean('force');
    const scheduler = interaction.client.scheduler;

    try {
      // Check if task exists
      const task = scheduler.getTask(taskId);
      if (!task) {
        const container = new MegamiContainer([
          new TextDisplayBuilder().setContent('❌ **Task Not Found**'),
          new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
          new TextDisplayBuilder().setContent(`Task with ID \`${taskId}\` does not exist.`),
          new TextDisplayBuilder().setContent('Use `/management schedules list` to see available tasks.'),
        ]);

        return await interaction.editReply({
          components: [container],
          flags: MessageFlags.IsComponentsV2,
        });
      }

      // Check if task is already disabled
      if (!task.enabled) {
        const container = new MegamiContainer([
          new TextDisplayBuilder().setContent('ℹ️ **Task Already Disabled**'),
          new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
          new TextDisplayBuilder().setContent(`**Task:** ${task.name} (\`${taskId}\`)`),
          new TextDisplayBuilder().setContent('**Status:** ⏹️ Already Disabled'),
          new TextDisplayBuilder().setContent(`**Schedule:** \`${task.expression}\``),
          new TextDisplayBuilder().setContent('This task is not currently running or scheduled.'),
        ]);

        return await interaction.editReply({
          components: [container],
          flags: MessageFlags.IsComponentsV2,
        });
      }

      // Check if task is currently running and force is not enabled
      const isRunning = scheduler.getRunningTasks().some((rt) => rt.id === taskId);
      if (isRunning && !force) {
        const container = new MegamiContainer([
          new TextDisplayBuilder().setContent('⚠️ **Task Currently Running**'),
          new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
          new TextDisplayBuilder().setContent(`**Task:** ${task.name} (\`${taskId}\`)`),
          new TextDisplayBuilder().setContent('**Status:** 🔄 Currently Executing'),
          new TextDisplayBuilder().setContent(
            'This task is currently running. To disable it anyway, use the `force` option.'
          ),
          new TextDisplayBuilder().setContent('**Warning:** Force disabling may interrupt the current execution.'),
        ]);

        return await interaction.editReply({
          components: [container],
          flags: MessageFlags.IsComponentsV2,
        });
      }

      const success = scheduler.disableTask(taskId);

      if (success) {
        const wasRunning = isRunning ? '\n**Note:** The running instance was terminated.' : '';

        const container = new MegamiContainer([
          new TextDisplayBuilder().setContent('✅ **Task Disabled Successfully**'),
          new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
          new TextDisplayBuilder().setContent(`**Task:** ${task.name} (\`${taskId}\`)`),
          new TextDisplayBuilder().setContent(`**Schedule:** \`${task.expression}\``),
          new TextDisplayBuilder().setContent(`**Status:** ⏹️ Now Disabled${wasRunning}`),
          new TextDisplayBuilder().setContent('The task will no longer execute automatically.'),
        ]);

        await interaction.editReply({
          components: [container],
          flags: MessageFlags.IsComponentsV2,
        });

        // Log the action
        interaction.client.logger.info(`Task ${taskId} disabled by ${interaction.user.tag}${force ? ' (forced)' : ''}`);
      } else {
        const container = new MegamiContainer([
          new TextDisplayBuilder().setContent('❌ **Failed to Disable Task**'),
          new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
          new TextDisplayBuilder().setContent(`Could not disable task \`${taskId}\`.`),
          new TextDisplayBuilder().setContent(
            'The task may be in an invalid state or the scheduler may be unavailable.'
          ),
          new TextDisplayBuilder().setContent('Please check the logs or contact an administrator.'),
        ]);

        await interaction.editReply({
          components: [container],
          flags: MessageFlags.IsComponentsV2,
        });
      }
    } catch (error) {
      const container = new MegamiContainer([
        new TextDisplayBuilder().setContent('❌ **Error Disabling Task**'),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent('An unexpected error occurred while disabling the task.'),
        new TextDisplayBuilder().setContent('Please try again or contact support if the issue persists.'),
      ]);

      await interaction.editReply({
        components: [container],
        flags: MessageFlags.IsComponentsV2,
      });

      interaction.client.logger.error(`Error disabling task ${taskId}:`, error);
    }
  },
  autocomplete: async (interaction) => {
    const focusedValue = interaction.options.getFocused();
    const scheduler = interaction.client.scheduler;

    // Get enabled tasks for autocomplete
    const enabledTasks = scheduler
      .getEnabledTasks()
      .filter(
        (task) =>
          task.id.toLowerCase().includes(focusedValue.toLowerCase()) ||
          task.name.toLowerCase().includes(focusedValue.toLowerCase())
      )
      .slice(0, 25);

    const choices = enabledTasks.map((task) => {
      const isRunning = scheduler.getRunningTasks().some((rt) => rt.id === task.id);
      const status = isRunning ? ' (Running)' : ' (Idle)';
      return {
        name: `${task.name} (${task.id})${status} - ${task.expression}`,
        value: task.id,
      };
    });

    await interaction.respond(choices);
  },
}));
