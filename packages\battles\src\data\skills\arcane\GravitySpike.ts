import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Gravity Spike',
  name: 'Gravity Spike',
  element: 'Arcane',
  manaCost: 25,
  cooldown: 3,
  description: 'A spike of concentrated gravity that damages and roots the target.',
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const finalDamage = Math.round(damage * 1.3);
    // This would also require a status effect system to handle the root.
    return {
      damage: finalDamage,
      isCritical,
      log: `${caster.name} impales ${target.name} with a Gravity Spike for ${finalDamage} damage${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
