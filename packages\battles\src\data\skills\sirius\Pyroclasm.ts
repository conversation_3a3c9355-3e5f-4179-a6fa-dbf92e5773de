import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Pyroclasm',
  name: 'Pyroclasm',
  element: 'Sirius',
  manaCost: 45,
  cooldown: 6,
  description: 'A pyroclastic flow that damages and stuns all enemies in a line.',
  execute: (caster, target, formulas) => {
    // This would require a more complex targeting and status effect system.
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const finalDamage = Math.round(damage * 1.5);
    return {
      damage: finalDamage,
      isCritical,
      log: `${caster.name} unleashes a Pyroclasm, damaging and stunning all enemies in a line for ${finalDamage} damage${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
