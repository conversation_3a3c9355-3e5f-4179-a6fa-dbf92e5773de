export type DropRarity = 'Stardust' | 'Moonlight' | 'Celestial';
export type PenaltyType = 'SHARD_LOSS' | 'COOLDOWN_LOCK';

export interface DropPenalty {
  type: PenaltyType;
  chance: number; // Percentage chance (0-100)
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'VERY_HIGH';
  description: string;
  effect: {
    // For SHARD_LOSS
    shardAmount?: number;

    // For COOLDOWN_LOCK
    cooldownMinutes?: number;
  };
}

export interface DropTypeConfig {
  id: string;
  name: string;
  description: string;
  cost: number; // Rift cost
  icon: string;
  rarityOdds: {
    stardust: number; // Percentage
    moonlight: number; // Percentage
    celestial: number; // Percentage
  };
  penalty?: DropPenalty;
  requirements?: {
    minLevel?: number;
    maxPerDay?: number;
    cooldownMinutes?: number;
  };
}

export const dropTypes: DropTypeConfig[] = [
  {
    id: 'basic',
    name: 'Basic Drop',
    description: 'Entry-level drop for casual collectors. Safe and reliable.',
    cost: 10,
    icon: '📦',
    rarityOdds: {
      stardust: 95,
      moonlight: 5,
      celestial: 0,
    },
    // No penalty for basic drops
  },

  {
    id: 'enhanced',
    name: 'Enhanced Drop',
    description: 'Slightly better odds with minimal risk. A step up from basic.',
    cost: 25,
    icon: '✨',
    rarityOdds: {
      stardust: 80,
      moonlight: 18,
      celestial: 2,
    },
    penalty: {
      type: 'SHARD_LOSS',
      chance: 10,
      severity: 'LOW',
      description: 'Small chance to lose some Shards',
      effect: {
        shardAmount: 5,
      },
    },
  },

  {
    id: 'moonlight',
    name: 'Moonlight Drop',
    description: 'High Moonlight focus with moderate risk. Chance to lose Shards.',
    cost: 50,
    icon: '🌕',
    rarityOdds: {
      stardust: 40,
      moonlight: 55,
      celestial: 5,
    },
    penalty: {
      type: 'SHARD_LOSS',
      chance: 25,
      severity: 'MEDIUM',
      description: 'Moderate chance to lose 25 Shards',
      effect: {
        shardAmount: 25,
      },
    },
    requirements: {
      minLevel: 5,
    },
  },

  {
    id: 'celestial',
    name: 'Celestial Drop',
    description: 'High risk, high reward. Excellent Celestial odds but significant penalty risk.',
    cost: 75,
    icon: '✨',
    rarityOdds: {
      stardust: 25,
      moonlight: 50,
      celestial: 25,
    },
    penalty: {
      type: 'COOLDOWN_LOCK',
      chance: 40,
      severity: 'HIGH',
      description: 'High chance to be locked out from drops for 1 hour',
      effect: {
        cooldownMinutes: 60,
      },
    },
    requirements: {
      minLevel: 15,
      cooldownMinutes: 30, // 30 minute cooldown between celestial drops
    },
  },

  {
    id: 'fatebound',
    name: 'Fatebound Drop',
    description: 'True gamble for thrill-seekers. Maximum Celestial odds, maximum risk.',
    cost: 100,
    icon: '🔮',
    rarityOdds: {
      stardust: 10,
      moonlight: 40,
      celestial: 50,
    },
    penalty: {
      type: 'COOLDOWN_LOCK',
      chance: 50,
      severity: 'VERY_HIGH',
      description: 'Very high chance to be locked out from drops for 3 hours',
      effect: {
        cooldownMinutes: 180, // 3 hours
      },
    },
    requirements: {
      minLevel: 25,
      maxPerDay: 3, // Limited to 3 per day
      cooldownMinutes: 120, // 2 hour cooldown
    },
  },
];

export const rarityInfo = {
  Stardust: {
    name: 'Stardust',
    description: '2D Standard visual for casual collectors',
    emoji: '⭐',
    color: '#87CEEB',
    tier: 1,
  },
  Moonlight: {
    name: 'Moonlight',
    description: '3D Premium tier with enhanced visual flair',
    emoji: '🌕',
    color: '#9D4EDD',
    tier: 2,
  },
  Celestial: {
    name: 'Celestial',
    description: '5D Animated prestige tier for ultimate collectors',
    emoji: '✨',
    color: '#FFD700',
    tier: 3,
  },
} as const;

export const penaltyDescriptions = {
  SHARD_LOSS: 'Lose Shards from your balance',
  COOLDOWN_LOCK: 'Temporary lockout from making drops',
} as const;

export function getDropTypeById(id: string): DropTypeConfig | undefined {
  return dropTypes.find((drop) => drop.id === id);
}

export function getDropTypesByLevel(level: number): DropTypeConfig[] {
  return dropTypes.filter((drop) => !drop.requirements?.minLevel || drop.requirements.minLevel <= level);
}

export function calculateDropRarity(dropType: DropTypeConfig): DropRarity {
  const random = Math.random() * 100;

  if (random < dropType.rarityOdds.celestial) {
    return 'Celestial';
  }

  if (random < dropType.rarityOdds.celestial + dropType.rarityOdds.moonlight) {
    return 'Moonlight';
  }

  return 'Stardust';
}

export function shouldApplyPenalty(dropType: DropTypeConfig): boolean {
  if (!dropType.penalty) return false;

  const random = Math.random() * 100;
  return random < dropType.penalty.chance;
}

export default dropTypes;
