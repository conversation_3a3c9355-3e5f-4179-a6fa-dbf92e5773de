/* biome-ignore-all lint/performance/noNamespaceImport: Easier this way */
import { defineRelations } from 'drizzle-orm';
import * as schema from './index';

export const relations = defineRelations(schema, (tables) => ({
  character: {
    series: tables.one.series({
      from: tables.characters.seriesId,
      to: tables.series.id,
    }),
  },

  balances: {
    currency: tables.one.currencies({
      from: tables.balances.currencyId,
      to: tables.currencies.id,
    }),
    user: tables.one.users({
      from: tables.balances.userId,
      to: tables.users.id,
    }),
  },

  skins: {
    character: tables.one.characters({
      from: tables.skins.characterId,
      to: tables.characters.id,
    }),
    author: tables.one.users({
      from: tables.skins.authorId,
      to: tables.users.id,
    }),
  },

  items: {
    holdings: tables.many.holdings(),
  },

  holdings: {
    user: tables.one.users({
      from: tables.holdings.userId,
      to: tables.users.id,
    }),
    item: tables.one.items({
      from: tables.holdings.itemId,
      to: tables.items.id,
    }),
  },

  users: {
    holdings: tables.many.holdings(),
    vessels: tables.many.vessels(),
    skins: tables.many.skins(),
    effects: tables.many.effects(),
  },

  effects: {
    user: tables.one.users({
      from: tables.effects.userId,
      to: tables.users.id,
    }),
  },

  vessels: {
    user: tables.one.users({
      from: tables.vessels.userId,
      to: tables.users.id,
    }),
    character: tables.one.characters({
      from: tables.vessels.characterId,
      to: tables.characters.id,
    }),
  },

  characters: {
    vessels: tables.many.vessels(),
  },

  currencies: {
    balances: tables.many.balances(),
  },
}));
