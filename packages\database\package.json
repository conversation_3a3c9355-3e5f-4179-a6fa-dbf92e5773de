{"name": "@megami/database", "version": "0.0.1", "description": "Database package for Goddess, using Drizzle ORM.", "module": "src/index.ts", "main": "src/index.ts", "types": "src/index.ts", "scripts": {"push": "drizzle-kit push", "pull": "drizzle-kit pull", "studio": "drizzle-kit studio"}, "type": "module", "private": true, "dependencies": {"dayjs": "^1.11.13", "drizzle-orm": "beta", "drizzle-zod": "^0.8.2", "postgres": "^3.4.7", "zod": "^4.0.14"}, "devDependencies": {"@types/bun": "latest", "drizzle-kit": "beta"}}