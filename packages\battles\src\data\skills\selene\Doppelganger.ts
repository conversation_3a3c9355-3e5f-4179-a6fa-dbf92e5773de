import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Doppelganger',
  name: 'Doppelganger',
  element: '<PERSON><PERSON>',
  manaCost: 35,
  cooldown: 5,
  description: 'Creates a doppelganger of the caster that has a portion of their stats and can attack enemies.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Evasion Up',
        name: 'Doppelganger',
        duration: 4,
        sourceId: caster.id,
      },
      log: `${caster.name} creates a doppelganger.`,
    };
  },
});
