import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Sleep Powder',
  name: 'Sleep Powder',
  element: 'Selene',
  manaCost: 20,
  cooldown: 4,
  description: 'Scatters a powder that puts all enemies in an area to sleep.',
  execute: (caster, target, formulas) => {
    // This would require a status effect system to handle sleep.
    return {
      log: `${caster.name} scatters Sleep Powder, putting all nearby enemies to sleep.`,
    };
  },
});
