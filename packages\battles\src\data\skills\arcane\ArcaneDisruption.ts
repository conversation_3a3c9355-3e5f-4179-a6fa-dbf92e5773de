import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Arcane Disruption',
  name: 'Arcane Disruption',
  element: 'Arcane',
  manaCost: 30,
  cooldown: 5,
  description: 'Disrupts the target\'s connection to the arcane, increasing the mana cost of their skills and lowering their magic resistance.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Magic Resist Down',
        name: 'Magic Resist Down',
        duration: 3,
        sourceId: caster.id,
      },
      log: `${caster.name} disrupts ${target.name}'s connection to the arcane, lowering their magic resistance.`,
    };
  },
});
