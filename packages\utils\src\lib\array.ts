/**
 * Returns the first element of an array.
 * @param array The array.
 * @returns The first element of the array, or undefined if the array is empty.
 */
export function first<T>(array: T[]): T | undefined {
  return array[0];
}

/**
 * Returns the last element of an array.
 * @param array The array.
 * @returns The last element of the array, or undefined if the array is empty.
 */
export function last<T>(array: T[]): T | undefined {
  return array.at(array.length - 1);
}

/**
 * Returns a random element from an array.
 * @param array The array.
 * @returns A random element from the array, or undefined if the array is empty.
 */
export function sample<T>(array: T[]): T | undefined {
  if (array.length === 0) {
    return;
  }
  return array[Math.floor(Math.random() * array.length)];
}

/**
 * Shuffles an array in place.
 * @param array The array to shuffle.
 * @returns The shuffled array.
 */
export function shuffle<T>(array: T[]): T[] {
  for (let i = array.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [array[i], array[j]] = [array[j], array[i]] as [T, T];
  }

  return array;
}

/**
 * Returns a new array with unique elements from the input array.
 * @param array The array.
 * @returns A new array with unique elements.
 */
export function unique<T>(array: T[]): T[] {
  return [...new Set(array)];
}

/**
 * Chunks an array into smaller arrays of a specified size.
 * @param array The array to chunk.
 * @param size The size of each chunk.
 * @returns An array of chunks.
 */
export function chunk<T>(array: T[], size: number): T[][] {
  const chunks: T[][] = [];
  for (let i = 0; i < array.length; i += size) {
    chunks.push(array.slice(i, i + size));
  }
  return chunks;
}

/**
 * Zips two arrays together.
 * @param a The first array.
 * @param b The second array.
 * @returns An array of tuples, where each tuple contains elements from the input arrays at the same index.
 */
export function zip<T, U>(a: T[], b: U[]): [T, U][] {
  const length = Math.min(a.length, b.length);
  const result: [T, U][] = [];

  for (let i = 0; i < length; i++) {
    result.push([a[i], b[i]] as [T, U]);
  }

  return result;
}

/**
 * Unzips an array of tuples into two arrays.
 * @param array The array of tuples to unzip.
 * @returns A tuple containing two arrays.
 */
export function unzip<T, U>(array: [T, U][]): [T[], U[]] {
  const a: T[] = [];
  const b: U[] = [];
  for (const [x, y] of array) {
    a.push(x);
    b.push(y);
  }
  return [a, b];
}
