import { emotes } from '@megami/config/lib/emotes';
import { getObject, translate } from '@megami/locale';
import { defineProgress } from '@megami/utils/lib/progress';
import { MessageFlags, SeparatorBuilder, SeparatorSpacingSize, TextDisplayBuilder } from 'discord.js';
import { defineSubCommand } from '../../../handlers/command';
import { MegamiContainer } from '../../../helpers/containers';
import { defer } from '../../../helpers/defer';
import { type PaginationPage, SelectPaginator } from '../../../lib/components/pagination';

export default defineSubCommand((builder) => ({
  builder: builder
    .setName(translate('commands.profile.modules.view.name'))
    .setNameLocalizations(getObject('commands.profile.modules.view.name'))
    .setDescription(translate('commands.profile.modules.view.description'))
    .setDescriptionLocalizations(getObject('commands.profile.modules.view.description'))
    .addUserOption((option) =>
      option
        .setName(translate('commands.profile.modules.view.options.user.name'))
        .setNameLocalizations(getObject('commands.profile.modules.view.options.user.name'))
        .setDescription(translate('commands.profile.modules.view.options.user.description'))
        .setDescriptionLocalizations(getObject('commands.profile.modules.view.options.user.description'))
        .setRequired(false)
    ),
  config: {},
  execute: async (interaction) => {
    await defer(interaction, false);

    const targetUser = interaction.options.getUser('user') || interaction.user;
    const isOwnProfile = targetUser.id === interaction.user.id;

    try {
      // Get user data
      const user = await interaction.client.database.users.get(targetUser.id);
      if (!user) {
        const container = new MegamiContainer([
          new TextDisplayBuilder().setContent('❌ **User Not Found**'),
          new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
          new TextDisplayBuilder().setContent(
            isOwnProfile
              ? translate('errors.user.unregistered.description')
              : `${targetUser.username} is not registered in the system.`
          ),
        ]);

        return await interaction.editReply({
          components: [container],
          flags: MessageFlags.IsComponentsV2,
        });
      }

      // Get user's rifts from balances table
      const riftsAmount = await interaction.client.database.users.getRifts(user.id);

      // Get additional profile data
      const [vesselStats] = await Promise.all([interaction.client.database.vessels.getUserStats(user.id)]);

      // Calculate experience progress to next level
      const currentLevelExp = (user.level - 1) * 4000;
      const nextLevelExp = user.level * 4000;
      const progressExp = user.experience - currentLevelExp;
      const neededExp = nextLevelExp - user.experience;
      const progressPercentage = Math.round((progressExp / 4000) * 100);

      // Calculate account age
      const accountAge = Math.floor((Date.now() - user.createdAt.getTime()) / (1000 * 60 * 60 * 24));

      const pages: PaginationPage[] = [];

      pages.push(
        ...[
          {
            label: 'Basic Information',
            components: [
              new TextDisplayBuilder().setContent(`**${targetUser.username}'s Profile**`),
              new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),

              new TextDisplayBuilder().setContent('**Basic Information**'),
              new TextDisplayBuilder().setContent(`**Level:** ${user.level}`),
              new TextDisplayBuilder().setContent(`**Experience:** ${user.experience.toLocaleString()} XP`),
              new TextDisplayBuilder().setContent(
                `**Progress:** ${progressExp.toLocaleString()}/${nextLevelExp} XP (${progressPercentage}%) ${defineProgress(user.experience, nextLevelExp)}`
              ),
              new TextDisplayBuilder().setContent(`**Next Level:** ${neededExp.toLocaleString()} XP needed`),
              new TextDisplayBuilder().setContent(`**${emotes.currencies.shards} Shards:** ${user.toLocaleString()}`),
              new TextDisplayBuilder().setContent(
                `**${emotes.currencies.rifts} Rifts:** ${riftsAmount.toLocaleString()}`
              ),
              new TextDisplayBuilder().setContent(`**Account Age:** ${accountAge} days`),
              new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
              new TextDisplayBuilder().setContent(`**Joined:** <t:${Math.floor(user.createdAt.getTime() / 1000)}:R>`),
              new TextDisplayBuilder().setContent(
                `**Last Updated:** <t:${Math.floor(user.updatedAt.getTime() / 1000)}:R>`
              ),
            ],
          },
          {
            label: 'Activity Streaks',
            components: [
              new TextDisplayBuilder().setContent(`**${targetUser.username}'s Profile**`),
              new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),

              new TextDisplayBuilder().setContent('**Activity Streaks**'),
              new TextDisplayBuilder().setContent(`**Daily:** ${daily.streak} day${daily.streak !== 1 ? 's' : ''}`),
              new TextDisplayBuilder().setContent(`**Weekly:** ${weekly.streak} week${weekly.streak !== 1 ? 's' : ''}`),
              new TextDisplayBuilder().setContent(
                `**Monthly:** ${monthly.streak} month${monthly.streak !== 1 ? 's' : ''}`
              ),
            ],
          },
          {
            label: 'Character Collection',
            components: [
              new TextDisplayBuilder().setContent('**Character Collection**'),
              new TextDisplayBuilder().setContent(`**Total Vessels:** ${vesselStats.totalVessels}`),
              new TextDisplayBuilder().setContent(`**Favorite Vessels:** ⭐ ${vesselStats.favoriteVessels}`),
              new TextDisplayBuilder().setContent(`**Total Level:** ${vesselStats.totalLevel}`),
              new TextDisplayBuilder().setContent(`**Average Level:** ${vesselStats.averageLevel}`),
              new TextDisplayBuilder().setContent(
                `**Highest Level:** ${vesselStats.highestLevel} ${vesselStats.highestLevel >= 50 ? '🏆' : ''}`
              ),
              new TextDisplayBuilder().setContent(`**Total Constellations:** ⭐ ${vesselStats.totalConstellation}`),
              new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
            ],
          },
        ]
      );

      const paginator = new SelectPaginator({
        interaction,
        pages: {
          total: pages.length,
          current: 0,
        },
        editReply: true,
        users: [interaction.user.id],
        containers: pages,
      });

      await paginator.start(pages[0]!.components);

      paginator.on('change', async (current, page) => {
        await page.respond(pages[current]!.components);
      });
    } catch (error) {
      const container = new MegamiContainer([
        new TextDisplayBuilder().setContent('**Error Loading Profile**'),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent('An unexpected error occurred while loading the profile.'),
        new TextDisplayBuilder().setContent('Please try again or contact support.'),
      ]);

      await interaction.editReply({
        components: [container],
        flags: MessageFlags.IsComponentsV2,
      });

      interaction.client.logger.error('Error loading user profile:', error);
    }
  },
}));
