import { getObject, translate } from '@megami/locale';
import type { SlashCommandSubcommandBuilder } from 'discord.js';
import { defineSubCommandGroup } from '../../../../handlers/command';

import create from './modules/create';
import del from './modules/delete';

export default defineSubCommandGroup((builder) => ({
  builder: builder
    .setName(translate('commands.management.modules.items.name'))
    .setNameLocalizations(getObject('commands.management.modules.items.name'))
    .setDescription(translate('commands.management.modules.items.description'))
    .setDescriptionLocalizations(getObject('commands.management.modules.items.description'))
    .addSubcommand(create.builder as SlashCommandSubcommandBuilder)
    .addSubcommand(del.builder as SlashCommandSubcommandBuilder),
  config: {},
  execute: async (interaction) => {
    const subcommand = interaction.options.getSubcommand();

    switch (subcommand) {
      case 'create': {
        await create.execute(interaction);
        break;
      }

      case 'delete': {
        await del.execute(interaction);
        break;
      }

      default: {
        await interaction.reply({
          content: 'Unknown subcommand.',
          ephemeral: true,
        });
      }
    }
  },
}));
