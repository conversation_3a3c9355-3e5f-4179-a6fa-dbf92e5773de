/* biome-ignore-all lint/style/useDefaultSwitchClause: Don't Need It */
import { getRarityEmoji, getSkinRewardByRarity } from '@megami/config/lib/rewards';
import { getObject, translate } from '@megami/locale';
import {
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle,
  ComponentType,
  MessageFlags,
  SeparatorBuilder,
  SeparatorSpacingSize,
  TextDisplayBuilder,
} from 'discord.js';
import { defineSubCommand } from '../../../../../handlers/command';
import { MegamiContainer } from '../../../../../helpers/containers';
import { defer } from '../../../../../helpers/defer';

export default defineSubCommand((builder) => ({
  builder: builder
    .setName(translate('commands.management.modules.skins.modules.approve.name'))
    .setNameLocalizations(getObject('commands.management.modules.skins.modules.approve.name'))
    .setDescription(translate('commands.management.modules.skins.modules.approve.description'))
    .setDescriptionLocalizations(getObject('commands.management.modules.skins.modules.approve.description')),
  config: {},

  execute: async (interaction) => {
    await defer(interaction, false);

    try {
      // Get pending skins that are NOT submitted by the current user
      const allPendingSkins = await interaction.client.database.skins.getPending();
      const eligibleSkins = allPendingSkins.filter((skin) => skin.authorId !== interaction.user.id);

      if (eligibleSkins.length === 0) {
        const container = new MegamiContainer([
          new TextDisplayBuilder().setContent('🚫 **No Eligible Submissions**'),
          new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
          new TextDisplayBuilder().setContent(
            allPendingSkins.length === 0
              ? 'There are no pending submissions to review at the moment.'
              : 'All pending submissions are your own. You cannot approve your own artwork, darling.'
          ),
        ]);

        return await interaction.editReply({
          components: [container],
          flags: MessageFlags.IsComponentsV2,
        });
      }

      // Select a random skin for approval
      const randomSkin = eligibleSkins[Math.floor(Math.random() * eligibleSkins.length)]!;
      const rarityEmoji = getRarityEmoji(randomSkin.rarity);
      const rewardConfig = getSkinRewardByRarity(randomSkin.rarity);
      const submittedDate = new Date(randomSkin.createdAt).toLocaleDateString();

      // Build reward summary
      let rewardSummary = '';
      if (rewardConfig) {
        const currencyRewards = rewardConfig.currencies.map((c) => `${c.amount} ${c.currency}`).join(', ');
        rewardSummary = `**Rewards:** ${currencyRewards} + ${rewardConfig.experience.amount} XP`;
      }

      const components = [
        new TextDisplayBuilder().setContent('🎨 **Skin Approval Review**'),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),

        new TextDisplayBuilder().setContent(`**${randomSkin.name}** ${rarityEmoji}`),
        new TextDisplayBuilder().setContent(`**Character:** ${randomSkin.character?.name || 'Unknown'}`),
        new TextDisplayBuilder().setContent(`**Author:** <@${randomSkin.authorId}>`),
        new TextDisplayBuilder().setContent(`**Rarity:** ${randomSkin.rarity}`),
        new TextDisplayBuilder().setContent(`**Submitted:** ${submittedDate}`),

        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),

        new TextDisplayBuilder().setContent(`**Preview:** [View Image](${randomSkin.url})`),

        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),

        new TextDisplayBuilder().setContent(rewardSummary),
        new TextDisplayBuilder().setContent(`**ID:** \`${randomSkin.id}\``),

        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),

        new TextDisplayBuilder().setContent(
          'Review this submission carefully. Once approved, the artist will receive their rewards automatically.'
        ),
      ];

      const buttons = new ActionRowBuilder<ButtonBuilder>().addComponents(
        new ButtonBuilder()
          .setCustomId(`approve_${randomSkin.id}`)
          .setLabel('Approve')
          .setStyle(ButtonStyle.Success)
          .setEmoji('✅'),
        new ButtonBuilder()
          .setCustomId(`reject_${randomSkin.id}`)
          .setLabel('Keep Pending')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('⏸️'),
        new ButtonBuilder().setCustomId('next_skin').setLabel('Next Skin').setStyle(ButtonStyle.Primary).setEmoji('🔄')
      );

      const container = new MegamiContainer([...components, buttons]);

      const response = await interaction.editReply({
        components: [container],
        flags: MessageFlags.IsComponentsV2,
      });

      // Handle button interactions
      const collector = response.createMessageComponentCollector({
        componentType: ComponentType.Button,
        time: 300_000, // 5 minutes
      });

      collector.on('collect', async (buttonInteraction) => {
        if (buttonInteraction.user.id !== interaction.user.id) {
          await buttonInteraction.reply({
            content: 'Only the command user can approve skins.',
            ephemeral: true,
          });
          return;
        }

        const [action, skinId] = buttonInteraction.customId.split('_');

        switch (action) {
          case 'approve': {
            try {
              const result = await interaction.client.database.skins.approve(skinId!);

              if (!result) {
                await buttonInteraction.reply({
                  content: 'Failed to approve skin - it may have already been approved or not found.',
                  ephemeral: true,
                });
                return;
              }

              const { skin, rewardsGiven } = result;
              const successComponents = [
                new TextDisplayBuilder().setContent('✅ **Skin Approved Successfully**'),
                new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),

                new TextDisplayBuilder().setContent(`**${skin.name}** has been added to the approved collection!`),
                new TextDisplayBuilder().setContent(`**Author:** <@${skin.authorId}>`),
                new TextDisplayBuilder().setContent(
                  `**Rewards:** ${rewardsGiven ? '✅ Distributed' : '❌ Failed to distribute'}`
                ),

                new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),

                new TextDisplayBuilder().setContent(
                  rewardConfig?.description || 'Another fine addition to my collection.'
                ),
              ];

              const successContainer = new MegamiContainer(successComponents);

              await buttonInteraction.update({
                components: [successContainer],
                flags: MessageFlags.IsComponentsV2,
              });

              // Log the approval
              interaction.client.logger.info(
                `Skin approved: ${skin.name} (${skin.id}) by ${interaction.user.tag} (${interaction.user.id}). Rewards: ${rewardsGiven ? 'Success' : 'Failed'}`
              );

              collector.stop();
            } catch (error) {
              await buttonInteraction.reply({
                content: 'An error occurred while approving the skin. Please try again.',
                ephemeral: true,
              });

              interaction.client.logger.error('Error approving skin:', error);
            }
            break;
          }

          case 'reject': {
            const rejectComponents = [
              new TextDisplayBuilder().setContent('⏸️ **Skin Kept Pending**'),
              new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
              new TextDisplayBuilder().setContent(
                `**${randomSkin.name}** remains in the pending queue for future review.`
              ),
            ];

            const rejectContainer = new MegamiContainer(rejectComponents);

            await buttonInteraction.update({
              components: [rejectContainer],
              flags: MessageFlags.IsComponentsV2,
            });

            collector.stop();
            break;
          }

          // case 'next': {
          //   // Restart the command with a new random skin
          //   collector.stop();
          //   await this.execute(interaction);
          //   break;
          // }
        }
      });

      collector.on('end', async () => {
        try {
          // Remove buttons when collector expires
          await interaction.editReply({
            components: [container],
            flags: MessageFlags.IsComponentsV2,
          });
        } catch {
          // Ignore errors when removing buttons
        }
      });
    } catch (error) {
      const container = new MegamiContainer([
        new TextDisplayBuilder().setContent('❌ **Error Loading Approval Interface**'),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent(
          'I encountered an issue while preparing the approval interface. Please try again later.'
        ),
      ]);

      await interaction.editReply({
        components: [container],
        flags: MessageFlags.IsComponentsV2,
      });

      interaction.client.logger.error('Error in skin approval command:', error);
    }
  },
}));
