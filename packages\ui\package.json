{"name": "@megami/ui", "version": "0.1.0", "private": true, "exports": {"./globals.css": "./src/styles/globals.css", "./postcss.config": "./postcss.config.mjs", "./lib/*": "./src/lib/*.ts", "./components/*": "./src/components/*.tsx", "./components/jules/*": "./src/components/jules/*.tsx", "./hooks/*": "./src/hooks/*.ts", "./icons/*": "./src/icons/*.tsx"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@number-flow/react": "^0.5.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@radix-ui/themes": "^3.2.1", "@tabler/icons-react": "^3.34.1", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.23.12", "input-otp": "^1.4.2", "lucide-react": "^0.535.0", "motion": "^12.23.12", "next": "15.3.4", "radix-ui": "^1.4.2", "react": "^19.1.1", "react-day-picker": "9.7.0", "react-dom": "^19.1.1", "react-hook-form": "^7.61.1", "react-intersection-observer": "^9.16.0", "react-tweet": "^3.2.2", "react-youtube": "^10.1.0", "server-only": "^0.0.1", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "tailwind-variants": "^2.0.1", "tailwindcss-animate": "^1.0.7", "usehooks-ts": "^3.1.1", "vaul": "^1.1.2"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.11", "@types/node": "^24.1.0", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "tailwindcss": "^4.1.11", "typescript": "^5.8.3"}}