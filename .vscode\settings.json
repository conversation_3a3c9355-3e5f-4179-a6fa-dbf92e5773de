{"editor.cursorSmoothCaretAnimation": "on", "editor.lineNumbers": "interval", "editor.renderWhitespace": "boundary", "workbench.colorTheme": "<PERSON><PERSON><PERSON><PERSON>", "workbench.tree.indent": 10, "window.title": "${rootName}", "errorLens.enabledDiagnosticLevels": ["warning", "error"], "errorLens.excludeBySource": ["cSpell", "Grammarly", "eslint"], "editor.defaultFormatter": "biomejs.biome", "editor.formatOnSave": true, "editor.formatOnPaste": true, "emmet.showExpandedAbbreviation": "never", "editor.codeActionsOnSave": {"source.fixAll.biome": "explicit", "source.organizeImports.biome": "explicit"}, "[typescript]": {"editor.defaultFormatter": "biomejs.biome"}, "[json]": {"editor.defaultFormatter": "biomejs.biome"}, "[javascript]": {"editor.defaultFormatter": "biomejs.biome"}, "[jsonc]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescriptreact]": {"editor.defaultFormatter": "biomejs.biome"}, "editor.fontFamily": "'SF Mono', 'Fira Code'", "editor.fontSize": 14, "editor.lineHeight": 1.6, "editor.formatOnSaveMode": "file", "files.exclude": {"**/.git": true, "**/.svn": true, "**/.hg": true, "**/CVS": true, "**/.DS_Store": true, "**/Thumbs.db": true, "**/build": true}, "search.exclude": {"**/node_modules": true, "**/dist": true}, "files.watcherExclude": {"**/.git/objects/**": true, "**/.git/subtree-cache/**": true, "**/node_modules/**": true, "**/build/**": true, "**/out/**": true, "**/.turbo/**": true, "**/.next/**": true}, "typescript.tsserver.maxTsServerMemory": 1024, "terminal.integrated.localEchoStyle": "dim", "git.ignoreLimitWarning": true}