import { sample } from '@megami/utils/lib/array';
import { type ChatInputCommandInteraction, MessageFlags, TextDisplayBuilder } from 'discord.js';
import { MegamiContainer } from './containers';

export interface LoadingMessage {
  readonly title: string;
  readonly description: string;
}

export const MESSAGES: Readonly<LoadingMessage[]> = [
  {
    title: 'Gazing into the Void...',
    description: 'The Vortex churns, pulling stellar data from the cosmos.',
  },
  {
    title: 'Consulting the Stars...',
    description: 'Sirius is aligning the constellations to find your answer.',
  },
  {
    title: 'Weaving the Threads of Fate...',
    description: 'Arcane energies are being shaped to fulfill your request.',
  },
  {
    title: 'Peeking Behind the Veil...',
    description: 'Selene is parting the mists of illusion to reveal the truth.',
  },
  {
    title: 'Tuning Reality...',
    description: 'The fabric of spacetime is being adjusted. Please hold.',
  },
  {
    title: 'Summoning Ancient Knowledge...',
    description: 'Whispers from forgotten epochs are being gathered.',
  },
  {
    title: 'Polishing the Celestial Lens...',
    description: 'Making sure your results are crystal clear and star-bright.',
  },
  {
    title: 'Stoking the Cosmic Flames...',
    description: 'The engine of creation is warming up to process your command.',
  },
  {
    title: 'Decoding Lunar Glyphs...',
    description: 'The moon’s cryptic messages are being translated just for you.',
  },
  {
    title: 'Charging the Aether...',
    description: 'Gathering the raw power needed to complete your task.',
  },
];

export async function defer(interaction: ChatInputCommandInteraction, ephemeral: boolean) {
  await interaction.deferReply({ flags: ephemeral ? MessageFlags.Ephemeral : undefined });

  const random = sample(MESSAGES as LoadingMessage[])!;
  const container = new MegamiContainer([
    new TextDisplayBuilder().setContent(random.title),
    new TextDisplayBuilder().setContent(random.description),
  ]);

  return await interaction.editReply({ components: [container], flags: MessageFlags.IsComponentsV2 });
}
