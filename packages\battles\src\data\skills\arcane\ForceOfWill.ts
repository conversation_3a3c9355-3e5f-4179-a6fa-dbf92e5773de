import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Force of Will',
  name: 'Force of Will',
  element: 'Arcane',
  manaCost: 0,
  cooldown: 0,
  description: 'The caster sacrifices their own health to cast a spell for free.',
  execute: (caster, target, formulas) => {
    const selfDamage = Math.round(caster.health * 0.1);
    caster.health -= selfDamage;
    return {
      appliedEffect: {
        id: 'Spell Power Up',
        name: 'Force of Will',
        duration: 3,
        sourceId: caster.id,
      },
      log: `${caster.name} uses their life force to power their spells, taking ${selfDamage} damage.`,
    };
  },
});
