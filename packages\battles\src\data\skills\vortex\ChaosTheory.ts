import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Chaos Theory',
  name: 'Chaos Theory',
  element: 'Vortex',
  manaCost: 40,
  cooldown: 6,
  description: 'The caster embraces chaos theory, causing all skills to have a chance to have a random effect.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Confusion',
        name: 'Chaos Theory',
        duration: 2,
        sourceId: caster.id,
      },
      log: `${caster.name} embraces chaos theory.`,
    };
  },
});
