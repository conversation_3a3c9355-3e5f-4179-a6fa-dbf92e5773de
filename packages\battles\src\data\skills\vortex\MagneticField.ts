import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Magnetic Field',
  name: 'Magnetic Field',
  element: 'Vortex',
  manaCost: 40,
  cooldown: 6,
  description: 'Creates a magnetic field that pulls all enemies towards the caster and deals damage over time.',
  execute: (caster, target, formulas) => {
    // This would require a system for creating and managing persistent area effects with a pull and DoT.
    return {
      log: `${caster.name} creates a Magnetic Field.`,
    };
  },
});
