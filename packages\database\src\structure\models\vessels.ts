import type { InferSelectModel } from 'drizzle-orm';
import { createSelectSchema } from 'drizzle-zod';
import { BaseModel } from '../model';

export type VesselSelect = InferSelectModel<typeof import('../../schema/vessels').vessels>;

export interface VesselWithCharacter extends VesselSelect {
  character: InferSelectModel<typeof import('../../schema/characters').characters>;
}

export class Vessels extends BaseModel {
  public schema = createSelectSchema(this.schemas.vessels);

  /**
   * Get all vessels for a user with character details
   */
  public async getUserVessels(userId: string): Promise<VesselWithCharacter[]> {
    return (await this.client.instance.query.vessels.findMany({
      where: { userId },
      with: {
        character: true,
      },
      orderBy: { obtainedAt: 'desc' },
    })) as VesselWithCharacter[];
  }

  /**
   * Get user's favorite vessels
   */
  public async getUserFavoriteVessels(userId: string): Promise<VesselWithCharacter[]> {
    const vessels = await this.client.instance.query.vessels.findMany({
      where: { userId },
      with: {
        character: true,
      },
      orderBy: { obtainedAt: 'desc' },
    });
    return vessels.filter((vessel) => vessel.isFavorite) as VesselWithCharacter[];
  }

  /**
   * Get vessel statistics for a user
   */
  public async getUserStats(userId: string): Promise<{
    totalVessels: number;
    favoriteVessels: number;
    totalLevel: number;
    averageLevel: number;
    highestLevel: number;
    totalConstellation: number;
  }> {
    const vessels = await this.getUserVessels(userId);

    if (vessels.length === 0) {
      return {
        totalVessels: 0,
        favoriteVessels: 0,
        totalLevel: 0,
        averageLevel: 0,
        highestLevel: 0,
        totalConstellation: 0,
      };
    }

    const totalLevel = vessels.reduce((sum, vessel) => sum + vessel.level, 0);
    const totalConstellation = vessels.reduce((sum, vessel) => sum + vessel.constellation, 0);
    const favoriteVessels = vessels.filter((vessel) => vessel.isFavorite).length;
    const highestLevel = Math.max(...vessels.map((vessel) => vessel.level));

    return {
      totalVessels: vessels.length,
      favoriteVessels,
      totalLevel,
      averageLevel: Math.round((totalLevel / vessels.length) * 10) / 10,
      highestLevel,
      totalConstellation,
    };
  }
}
