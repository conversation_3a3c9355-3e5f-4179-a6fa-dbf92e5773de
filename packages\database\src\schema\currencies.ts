import type { CurrencyN<PERSON>s, CurrencyTier } from '@megami/config/lib/currencies';
import { pgTable, serial, text, timestamp, uniqueIndex } from 'drizzle-orm/pg-core';

export const currencies = pgTable(
  'currencies',
  {
    id: serial('id').primaryKey(),
    name: text('name').$type<CurrencyNames>().notNull(),
    description: text('description').notNull(),
    tier: text('tier').$type<CurrencyTier>().notNull(),
    createdAt: timestamp('created_at').defaultNow().notNull(),
    updatedAt: timestamp('updated_at').defaultNow().notNull(),
  },
  (table) => [uniqueIndex('currency_name_idx').on(table.name)]
);
