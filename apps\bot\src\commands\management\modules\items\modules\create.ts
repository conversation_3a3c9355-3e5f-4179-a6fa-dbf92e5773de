import type { ItemRarity, ItemType } from '@megami/database';
import { getObject, translate } from '@megami/locale';
import { MessageFlags, SeparatorBuilder, SeparatorSpacingSize, TextDisplayBuilder } from 'discord.js';
import { defineSubCommand } from '../../../../../handlers/command';
import { MegamiContainer } from '../../../../../helpers/containers';
import { defer } from '../../../../../helpers/defer';

export default defineSubCommand((builder) => ({
  builder: builder
    .setName(translate('commands.management.modules.items.modules.create.name'))
    .setNameLocalizations(getObject('commands.management.modules.items.modules.create.name'))
    .setDescription(translate('commands.management.modules.items.modules.create.description'))
    .setDescriptionLocalizations(getObject('commands.management.modules.items.modules.create.description'))
    .addStringOption((option) =>
      option
        .setName(translate('commands.management.modules.items.modules.create.options.name.name'))
        .setNameLocalizations(getObject('commands.management.modules.items.modules.create.options.name.name'))
        .setDescription(translate('commands.management.modules.items.modules.create.options.name.description'))
        .setDescriptionLocalizations(
          getObject('commands.management.modules.items.modules.create.options.name.description')
        )
        .setRequired(true)
        .setMaxLength(100)
    )
    .addStringOption((option) =>
      option
        .setName(translate('commands.management.modules.items.modules.create.options.description.name'))
        .setNameLocalizations(getObject('commands.management.modules.items.modules.create.options.description.name'))
        .setDescription(translate('commands.management.modules.items.modules.create.options.description.description'))
        .setDescriptionLocalizations(
          getObject('commands.management.modules.items.modules.create.options.description.description')
        )
        .setRequired(true)
        .setMaxLength(500)
    )
    .addStringOption((option) =>
      option
        .setName(translate('commands.management.modules.items.modules.create.options.type.name'))
        .setNameLocalizations(getObject('commands.management.modules.items.modules.create.options.type.name'))
        .setDescription(translate('commands.management.modules.items.modules.create.options.type.description'))
        .setDescriptionLocalizations(
          getObject('commands.management.modules.items.modules.create.options.type.description')
        )
        .setRequired(true)
        .addChoices(
          { name: 'Equipment', value: 'EQUIPMENT' },
          { name: 'Consumable', value: 'CONSUMABLE' },
          { name: 'Material', value: 'MATERIAL' },
          { name: 'Collectible', value: 'COLLECTIBLE' },
          { name: 'Currency', value: 'CURRENCY' },
          { name: 'Gift', value: 'GIFT' },
          { name: 'Key', value: 'KEY' },
          { name: 'Miscellaneous', value: 'MISC' }
        )
    )
    .addStringOption((option) =>
      option
        .setName(translate('commands.management.modules.items.modules.create.options.rarity.name'))
        .setNameLocalizations(getObject('commands.management.modules.items.modules.create.options.rarity.name'))
        .setDescription(translate('commands.management.modules.items.modules.create.options.rarity.description'))
        .setDescriptionLocalizations(
          getObject('commands.management.modules.items.modules.create.options.rarity.description')
        )
        .setRequired(true)
        .addChoices(
          { name: '⚪ Common', value: 'COMMON' },
          { name: '🟢 Uncommon', value: 'UNCOMMON' },
          { name: '🔵 Rare', value: 'RARE' },
          { name: '🟣 Epic', value: 'EPIC' },
          { name: '🟡 Legendary', value: 'LEGENDARY' },
          { name: '🔴 Mythic', value: 'MYTHIC' }
        )
    )
    .addStringOption((option) =>
      option.setName('icon').setDescription('Item icon (emoji or URL)').setRequired(false).setMaxLength(100)
    )
    .addBooleanOption((option) =>
      option.setName('stackable').setDescription('Can multiple items be stacked together?').setRequired(false)
    )
    .addIntegerOption((option) =>
      option
        .setName('max-stack')
        .setDescription('Maximum stack size (default: 99)')
        .setRequired(false)
        .setMinValue(1)
        .setMaxValue(9999)
    )
    .addBooleanOption((option) =>
      option.setName('tradeable').setDescription('Can this item be traded between users?').setRequired(false)
    )
    .addBooleanOption((option) =>
      option.setName('sellable').setDescription('Can this item be sold?').setRequired(false)
    ),
  config: {},
  execute: async (interaction) => {
    await defer(interaction, false);

    const name = interaction.options.getString('name', true);
    const description = interaction.options.getString('description', true);
    const type = interaction.options.getString('type', true) as ItemType;
    const rarity = interaction.options.getString('rarity', true) as ItemRarity;
    const icon = interaction.options.getString('icon');
    const stackable = interaction.options.getBoolean('stackable') ?? true;
    const maxStack = interaction.options.getInteger('max-stack') ?? 99;
    const tradeable = interaction.options.getBoolean('tradeable') ?? true;
    const sellable = interaction.options.getBoolean('sellable') ?? true;

    try {
      // Check if item name already exists
      const existingItem = await interaction.client.database.items.getByName(name);
      if (existingItem) {
        const container = new MegamiContainer([
          new TextDisplayBuilder().setContent('❌ **Item Creation Failed**'),
          new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
          new TextDisplayBuilder().setContent(`An item with the name "${name}" already exists.`),
          new TextDisplayBuilder().setContent('Please choose a different name.'),
        ]);

        return await interaction.editReply({
          components: [container],
          flags: MessageFlags.IsComponentsV2,
        });
      }

      // Create the item
      const item = await interaction.client.database.items.create({
        name,
        description,
        type,
        rarity,
        icon,
        stackable,
        maxStack,
        tradeable,
        sellable,
      });

      const typeEmoji = getTypeEmoji(type);
      const rarityEmoji = getRarityEmoji(rarity);

      const container = new MegamiContainer([
        new TextDisplayBuilder().setContent('✅ **Item Created Successfully**'),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent(`${icon || typeEmoji} **${item.name}**`),
        new TextDisplayBuilder().setContent(`${rarityEmoji} ${item.rarity} ${typeEmoji} ${item.type}`),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent(`**Description:** ${item.description}`),
        new TextDisplayBuilder().setContent(
          `**Stackable:** ${item.stackable ? 'Yes' : 'No'}${item.stackable ? ` (Max: ${item.maxStack})` : ''}`
        ),
        new TextDisplayBuilder().setContent(`**Tradeable:** ${item.tradeable ? 'Yes' : 'No'}`),
        new TextDisplayBuilder().setContent(`**Sellable:** ${item.sellable ? 'Yes' : 'No'}`),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent(`**Item ID:** \`${item.id}\``),
      ]);

      await interaction.editReply({
        components: [container],
        flags: MessageFlags.IsComponentsV2,
      });
    } catch (error) {
      const container = new MegamiContainer([
        new TextDisplayBuilder().setContent('❌ **Error Creating Item**'),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent('An unexpected error occurred while creating the item.'),
        new TextDisplayBuilder().setContent('Please try again or contact an administrator.'),
      ]);

      await interaction.editReply({
        components: [container],
        flags: MessageFlags.IsComponentsV2,
      });

      // Log the error for debugging
      interaction.client.logger.error('Error creating item:', error);
    }
  },
}));

function getTypeEmoji(type: ItemType): string {
  const emojis: Record<ItemType, string> = {
    EQUIPMENT: '⚔️',
    CONSUMABLE: '🧪',
    MATERIAL: '🔨',
    COLLECTIBLE: '💎',
    CURRENCY: '💰',
    GIFT: '🎁',
    KEY: '🗝️',
    MISC: '📦',
  };
  return emojis[type];
}

function getRarityEmoji(rarity: ItemRarity): string {
  const emojis: Record<ItemRarity, string> = {
    COMMON: '⚪',
    UNCOMMON: '🟢',
    RARE: '🔵',
    EPIC: '🟣',
    LEGENDARY: '🟡',
    MYTHIC: '🔴',
  };
  return emojis[rarity];
}
