import { Collection } from 'discord.js';
import type { Command } from '../handlers/command';

export class MegamiStorage {
  public data = new Collection<string, Collection<string, unknown>>();
  public executing = new Set<string>();
  public cooldowns = new Collection<string, number>();

  constructor() {
    this.data.set('commands', new Collection());
  }

  get commands(): Collection<string, Command> {
    return this.data.get('commands') as Collection<string, Command>;
  }
}
