import { weeklyRewards } from '@megami/config/lib/activity';
import { gt, gte, users as table } from '@megami/database';
import { defineTask } from '../../structure/scheduler';

export default defineTask({
  id: 'weekly-rewards',
  name: 'Weekly Rewards',
  expression: '0 0 12 * * 0', // Every Sunday at 12:00 PM
  enabled: true,
  execute: async (client) => {
    try {
      const users = await client.database.instance
        .select()
        .from(client.database.users.schemas.users)
        .where(gte(table.dailyStreak, 7));

      let rewardsGiven = 0;

      for await (const user of users) {
        const streak = user.dailyStreak;

        // Check if streak is a multiple of 7 (weekly milestone)
        if (streak % 7 === 0) {
          const weekNumber = Math.floor(streak / 7);
          const weekIndex = Math.min(weekNumber - 1, weeklyRewards.length - 1);
          const weeklyReward = weeklyRewards[weekIndex];
          if (!weeklyReward) continue;

          // Apply weekly rewards
          const promises = [];

          // Add rifts
          promises.push(client.database.users.addRifts(user.id, weeklyReward.rifts));

          // Add shards
          const shardsCurrency = await client.database.currencies.getCurrencyByName('Shards');
          if (shardsCurrency) {
            promises.push(client.database.currencies.addToUserBalance(user.id, shardsCurrency.id, weeklyReward.shards));
          }

          // Add experience
          if (weeklyReward.experience > 0) {
            promises.push(client.database.users.addExperience(user.id, weeklyReward.experience));
          }

          await Promise.all(promises);
          rewardsGiven++;

          client.logger.info(`Gave weekly reward to user ${user.id} (week ${weekNumber})`);
        }
      }

      if (rewardsGiven > 0) {
        client.logger.info(`Weekly rewards task completed: ${rewardsGiven} users received rewards`);
      }
    } catch (error) {
      client.logger.error('Error during weekly rewards task:', error);
    }
  },
});
