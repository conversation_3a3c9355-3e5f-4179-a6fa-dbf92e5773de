import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Cosmic Ray',
  name: 'Cosmic Ray',
  element: 'Vortex',
  manaCost: 20,
  cooldown: 3,
  description: 'A beam of cosmic rays that damages and has a chance to apply a random status effect.',
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const finalDamage = Math.round(damage * 1.2);
    // This would also require a system for handling random status effects.
    return {
      damage: finalDamage,
      isCritical,
      log: `${caster.name} fires a Cosmic Ray at ${target.name}, dealing ${finalDamage} damage${isCritical ? ' (CRIT!)' : ''}.`,
    };
  },
});
