import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Flame Wreath',
  name: 'Flame Wreath',
  element: 'Sirius',
  manaCost: 35,
  cooldown: 5,
  description: 'The caster is surrounded by a wreath of flames, damaging any enemy that attacks them.',
  execute: (caster, target, formulas) => {
    return {
      appliedEffect: {
        id: 'Damage Reflection',
        name: 'Flame Wreath',
        duration: 3,
        sourceId: caster.id,
      },
      log: `${caster.name} is surrounded by a Flame Wreath.`,
    };
  },
});
