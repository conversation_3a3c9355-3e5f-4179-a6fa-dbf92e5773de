import { getObject, translate } from '@megami/locale';
import { MessageFlags, SeparatorBuilder, SeparatorSpacingSize, TextDisplayBuilder } from 'discord.js';
import { defineSubCommand } from '../../../../../handlers/command';
import { MegamiContainer } from '../../../../../helpers/containers';
import { defer } from '../../../../../helpers/defer';

export default defineSubCommand((builder) => ({
  builder: builder
    .setName(translate('commands.management.modules.schedules.modules.status.name'))
    .setNameLocalizations(getObject('commands.management.modules.schedules.modules.status.name'))
    .setDescription(translate('commands.management.modules.schedules.modules.status.description'))
    .setDescriptionLocalizations(getObject('commands.management.modules.schedules.modules.status.description')),
  config: {},
  execute: async (interaction) => {
    await defer(interaction, false);

    const scheduler = interaction.client.scheduler;
    const status = scheduler.getStatus();
    const runningTasks = scheduler.getRunningTasks();

    const components = [
      new TextDisplayBuilder().setContent('📅 **Scheduler Status**'),
      new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
      new TextDisplayBuilder().setContent(`**Status:** ${status.isRunning ? '✅ Running' : '❌ Stopped'}`),
      new TextDisplayBuilder().setContent(`**Total Tasks:** ${status.totalTasks}`),
      new TextDisplayBuilder().setContent(`**Enabled Tasks:** ${status.enabledTasks}`),
      new TextDisplayBuilder().setContent(`**Running Tasks:** ${status.runningTasks}`),
      new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
    ];

    if (runningTasks.length > 0) {
      components.push(new TextDisplayBuilder().setContent('⏰ **Currently Running Tasks:**'));

      for (const task of runningTasks.slice(0, 10)) {
        const nextRun = getNextRunTime(task.expression);
        components.push(
          new TextDisplayBuilder().setContent(`  🔄 **${task.name}** (${task.id})`),
          new TextDisplayBuilder().setContent(`    └ Schedule: \`${task.expression}\` | Next: ${nextRun}`)
        );
      }

      if (runningTasks.length > 10) {
        components.push(new TextDisplayBuilder().setContent(`  ... and ${runningTasks.length - 10} more tasks`));
      }
    } else {
      components.push(new TextDisplayBuilder().setContent('No tasks currently running.'));
    }

    // Add system health info
    components.push(
      new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
      new TextDisplayBuilder().setContent('🔧 **System Health:**'),
      new TextDisplayBuilder().setContent(`**Bot Uptime:** ${formatUptime(process.uptime())}`),
      new TextDisplayBuilder().setContent(`**Memory Usage:** ${formatMemory(process.memoryUsage().heapUsed)}`),
      new TextDisplayBuilder().setContent(
        `**Scheduler Uptime:** ${status.isRunning ? formatUptime(process.uptime()) : 'Not running'}`
      )
    );

    const container = new MegamiContainer(components);
    await interaction.editReply({
      components: [container],
      flags: MessageFlags.IsComponentsV2,
    });
  },
}));

function getNextRunTime(_expression: string): string {
  try {
    // This is a simplified next run calculation
    // In a real implementation, you'd use a cron parser library
    const now = new Date();
    const nextRun = new Date(now.getTime() + 60_000); // Simplified: next minute
    return `<t:${Math.floor(nextRun.getTime() / 1000)}:R>`;
  } catch {
    return 'Unknown';
  }
}

function formatUptime(seconds: number): string {
  const days = Math.floor(seconds / 86_400);
  const hours = Math.floor((seconds % 86_400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);

  if (days > 0) return `${days}d ${hours}h ${minutes}m`;
  if (hours > 0) return `${hours}h ${minutes}m`;
  return `${minutes}m`;
}

function formatMemory(bytes: number): string {
  const mb = Math.round(bytes / 1024 / 1024);
  return `${mb} MB`;
}
