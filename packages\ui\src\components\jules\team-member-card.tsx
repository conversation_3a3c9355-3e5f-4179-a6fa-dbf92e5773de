'use client';

import { cn } from '@megami/ui/lib/utils';
import { motion } from 'framer-motion';
import * as React from 'react';
import { Avatar } from './avatar';

export interface TeamMemberCardProps extends React.HTMLAttributes<HTMLDivElement> {
  name: string;
  role: string;
  avatarSrc: string;
}

const TeamMemberCard = React.forwardRef<HTMLDivElement, TeamMemberCardProps>(
  ({ className, name, role, avatarSrc, ...props }, ref) => {
    const cardVariants = {
      hidden: { opacity: 0, scale: 0.8 },
      visible: { opacity: 1, scale: 1, transition: { duration: 0.5 } },
    };

    return (
      <motion.div
        animate="visible"
        className={cn(
          'relative w-full rounded-lg bg-gray-900/50 p-6 text-center text-white backdrop-blur-sm',
          className
        )}
        initial="hidden"
        ref={ref}
        variants={cardVariants}
        {...props}
      >
        <Avatar alt={name} className="mx-auto mb-4" size="lg" src={avatarSrc} />
        <h3 className="font-bold text-xl">{name}</h3>
        <p className="text-gray-400">{role}</p>
      </motion.div>
    );
  }
);
TeamMemberCard.displayName = 'TeamMemberCard';

export { TeamMemberCard };
